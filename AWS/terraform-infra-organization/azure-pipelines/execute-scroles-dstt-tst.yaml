trigger: none

# appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ replace(parameters.state,'/','／') }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: TST-aws-org-dstt
  values:
  - TST-aws-org-dstt

- name: state
  type: string
  default: sc-roles/otp-dstt-cdlh-acc-tst-01
  values:
  - sc-roles/otp-dstt-cbs-acc-tst-01
  - sc-roles/otp-dstt-cdlh-acc-tst-01

- name: action
  type: string
  default: plan
  values:
    - plan
    - apply

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: ' '

- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'iacbot'
  - template: env/PRD-aws-org.yaml@pipelines

resources:
  repositories:
    - repository: pipelines
      type: git
      name: pipelines
      ref: refs/tags/v0.1.0
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@pipelines
  parameters:
    action: ${{ parameters.action }}
    appCode: ${{ variables.appCode }}
    skipCheckovScan: ${{ parameters.skip_checkov }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    environment: ${{ parameters.environment }}
    awsServiceConnectionName: ${{ variables.awsServiceConnectionName }}
    awsRegion: ${{ variables.awsRegion }}
    awsS3BucketName: ${{ variables.awsS3BucketName }}
    awsS3BucketRegion: ${{ variables.awsS3BucketRegion }}
    # awsS3BucketKmsKeyId: ${{ variables.awsS3BucketKmsKeyId }}
    terraformProjectLocation: terraform/${{ parameters.state }}
    terraformVersion: 1.12.2
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    terraformRCFileForNetworkMirror: network-mirror/.terraformrc