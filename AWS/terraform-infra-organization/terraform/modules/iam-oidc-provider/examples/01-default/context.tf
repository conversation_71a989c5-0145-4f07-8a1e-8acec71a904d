module "context" {
  #checkov:skip=CKV_TF_1: Ensure Terraform module sources use a commit hash
  #checkov:skip=CKV_TF_2: Ensure Terraform module sources use a tag with a version number
  source      = "*******************:otp/otp-context-module.git"
  cloud       = var.cloud
  environment = var.environment
  department  = var.department
  project     = var.project
  region      = var.region
  tags = {
    OwnerOU      = "workload"
    OwnerContact = var.owner
    Criticality  = "low"
  }
}
# TC2
