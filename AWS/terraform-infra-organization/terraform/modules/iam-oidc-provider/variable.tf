# This variable defines the naming context used by the naming convention module
variable "context" {
  description = "terraform-context module"
  validation {
    condition     = length(var.context.project) > 0
    error_message = "(Required) Please provide value to the project in the context"
  }
}

variable "oidc_url" {
  type        = string
  description = "The URL of the identity provider. Corresponds to the iss claim."
}

variable "client_id_list" {
  type        = list(string)
  description = "A list of client IDs, also known as audiences."
}

variable "thumbprint_list" {
  type        = list(string)
  description = "A list of server certificate thumbprints for the OpenID Connect (OIDC) identity provider's server certificate(s)."
  default = []
}

variable "global_name_suffix" {
  type = string
  description = "Name suffix for the service"
}