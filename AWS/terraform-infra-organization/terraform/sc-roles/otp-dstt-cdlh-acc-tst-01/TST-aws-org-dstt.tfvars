sc_iam_role = {
  custom_role_name = "serviceconnection"

  iam_managed_policy_arns = ["arn:aws:iam::aws:policy/AdministratorAccess"]

  inline_trust_policy = <<-EOT
  {
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::795169374912:oidc-provider/vstoken.dev.azure.com/d1e29c2d-35c5-46f2-8dcf-a89f5a0622c4"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "vstoken.dev.azure.com/d1e29c2d-35c5-46f2-8dcf-a89f5a0622c4:aud": "api://AzureADTokenExchange",
          "vstoken.dev.azure.com/d1e29c2d-35c5-46f2-8dcf-a89f5a0622c4:sub": "sc://ADOS-OTPHU-01/aws-lz/TST-otp-dstt-cdlh-acc-tst-01"
        }
      }
    }
  ]
}
  EOT
}

network_sc_iam_role = {
  custom_role_name = "LZNetworkAdminRole"

  iam_managed_policy_arns = ["arn:aws:iam::aws:policy/job-function/NetworkAdministrator"]

  iam_inline_policy_arns = ["LZNetworkAdminRole.json"]

  policy_path = "../sc-policies"

  inline_trust_policy = <<-EOT
  {
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::795169374912:role/role-ec1-prd-lznetworkadminrole"
            },
            "Action": "sts:AssumeRole",
            "Condition": {}
        }
    ]
}
  EOT
}

secu_sc_iam_role = {
  custom_role_name = "LZSecurityDeploymentRole"

  iam_managed_policy_arns = ["arn:aws:iam::aws:policy/ReadOnlyAccess"]

  iam_inline_policy_arns = ["LZSecurityDeploymentRole.json"]

  policy_path = "../sc-policies"

  inline_trust_policy = <<-EOT
  {
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::701373711870:role/role-ec1-prd-lzsecuritydeploymentrole"
            },
            "Action": "sts:AssumeRole",
            "Condition": {}
        }
    ]
}
  EOT
}




azure_oidc_provider = {
  global_name_suffix = "azuredevops"

  oidc_url = "https://vstoken.dev.azure.com/d1e29c2d-35c5-46f2-8dcf-a89f5a0622c4"

  client_id_list = ["api://AzureADTokenExchange"]
}