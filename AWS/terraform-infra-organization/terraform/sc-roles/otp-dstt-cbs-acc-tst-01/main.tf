module "azure_oidc_provider" {
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/aws-lz/_git/terraform-aws-modules//iam-openid-connect-provider?ref=v0.1.0"

  global_name_suffix = var.azure_oidc_provider.global_name_suffix

  context = module.context

  oidc_url = var.azure_oidc_provider.oidc_url

  client_id_list = var.azure_oidc_provider.client_id_list

  

}

module "network_sc_iam_role" {
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-aws-iam-roles?ref=init"
  context = module.context

  custom_role_name = var.network_sc_iam_role.custom_role_name

  iam_managed_policy_arns = var.network_sc_iam_role.iam_managed_policy_arns

  iam_inline_policy_arns = var.network_sc_iam_role.iam_inline_policy_arns

  policy_path = var.network_sc_iam_role.policy_path

  inline_trust_policy = var.network_sc_iam_role.inline_trust_policy
}

module "sc_iam_role" {
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-aws-iam-roles?ref=init"
  context = module.context

  custom_role_name = var.sc_iam_role.custom_role_name

  iam_managed_policy_arns = var.sc_iam_role.iam_managed_policy_arns

  inline_trust_policy = var.sc_iam_role.inline_trust_policy
}

module "secu_sc_iam_role" {
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-aws-iam-roles?ref=init"
  context = module.context

  custom_role_name = var.secu_sc_iam_role.custom_role_name

  iam_managed_policy_arns = var.secu_sc_iam_role.iam_managed_policy_arns

  iam_inline_policy_arns = var.secu_sc_iam_role.iam_inline_policy_arns
  
  policy_path = var.secu_sc_iam_role.policy_path

  inline_trust_policy = var.secu_sc_iam_role.inline_trust_policy
}