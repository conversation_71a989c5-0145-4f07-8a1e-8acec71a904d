{"Version": "2012-10-17", "Statement": [{"Sid": "ConfigRecorderAndDeliveryChannel", "Effect": "Allow", "Action": ["config:PutConfigurationRecorder", "config:PutDeliveryChannel", "config:StartConfigurationRecorder", "config:StopConfigurationRecorder", "config:DeleteConfigurationRecorder", "config:DeleteDeliveryChannel", "config:DescribeConfigurationRecorders", "config:DescribeConfigurationRecorderStatus", "config:DescribeDeliveryChannels", "config:DescribeDeliveryChannelStatus"], "Resource": "*"}, {"Sid": "ConfigAggregationAuthorization", "Effect": "Allow", "Action": ["config:PutAggregationAuthorization", "config:DeleteAggregationAuthorization", "config:DescribeAggregationAuthorizations"], "Resource": "*"}, {"Sid": "ServiceLinkedRole", "Effect": "Allow", "Action": ["iam:CreateServiceLinkedRole", "iam:GetRole"], "Resource": ["arn:aws:iam::*:role/aws-service-role/config.amazonaws.com/AWSServiceRoleForConfig"]}]}