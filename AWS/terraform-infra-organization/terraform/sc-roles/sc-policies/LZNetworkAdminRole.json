{"Version": "2012-10-17", "Statement": [{"Sid": "ServiceLinkedRoleCreation", "Effect": "Allow", "Action": ["iam:CreateServiceLinkedRole", "iam:DeleteServiceLinkedRole", "iam:GetServiceLinkedRoleDeletionStatus"], "Resource": ["arn:aws:iam::*:role/aws-service-role/transitgateway.amazonaws.com/*"]}, {"Sid": "ResourceAccessManagerForNetworking", "Effect": "Allow", "Action": ["ram:CreateResourceShare", "ram:DeleteResourceShare", "ram:UpdateResourceShare", "ram:GetResourceShares", "ram:GetResourceShareAssociations", "ram:AssociateResourceShare", "ram:DisassociateResourceShare", "ram:AcceptResourceShareInvitation", "ram:RejectResourceShareInvitation", "ram:TagResource", "ram:UntagResource"], "Resource": "*", "Condition": {"StringEquals": {"ram:RequestedResourceType": ["ec2:TransitGateway", "ec2:Subnet"]}}}, {"Sid": "OrganizationsReadForSharing", "Effect": "Allow", "Action": ["organizations:DescribeOrganization", "organizations:DescribeAccount", "organizations:DescribeOrganizationalUnit", "organizations:ListAccounts", "organizations:ListAccountsForParent", "organizations:ListOrganizationalUnitsForParent", "organizations:ListRoots"], "Resource": "*"}, {"Sid": "NetworkingServicesPrincipals", "Effect": "Allow", "Action": ["iam:GetRole", "iam:PassRole"], "Resource": ["arn:aws:iam::*:role/aws-service-role/transitgateway.amazonaws.com/*", "arn:aws:iam::*:role/service-role/VPCFlowLogsDeliveryRole*"]}, {"Sid": "SSMParameterStoreForNetworkConfig", "Effect": "Allow", "Action": ["ssm:GetParameter", "ssm:GetParameters", "ssm:PutParameter", "ssm:DeleteParameter", "ssm:GetParametersByPath", "ssm:DescribeParameters", "ssm:AddTagsToResource", "ssm:RemoveTagsFromResource"], "Resource": ["arn:aws:ssm:*:*:parameter/network/*", "arn:aws:ssm:*:*:parameter/landing-zone/network/*"]}, {"Sid": "KMSForNetworkEncryption", "Effect": "Allow", "Action": ["kms:DescribeKey", "kms:GetKeyPolicy", "kms:ListAliases", "kms:TagResource"], "Resource": "*"}, {"Sid": "CloudWatchLogsForNetworking", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:DeleteLogGroup", "logs:DeleteLogStream", "logs:PutRetentionPolicy", "logs:TagLogGroup", "logs:UntagLogGroup"], "Resource": ["arn:aws:logs:*:*:log-group:/aws/vpc/flowlogs*", "arn:aws:logs:*:*:log-group:/aws/transitgateway/flowlogs*", "arn:aws:logs:*:*:log-group:/aws/networkfirewall/*"]}, {"Sid": "S3BucketManagement", "Effect": "Allow", "Action": ["s3:CreateBucket", "s3:DeleteBucket", "s3:GetBucketLocation", "s3:GetBucketVersioning", "s3:GetBucketAcl", "s3:GetBucketPolicy", "s3:GetBucketPolicyStatus", "s3:GetBucketPublicAccessBlock", "s3:GetBucketLogging", "s3:GetBucketNotification", "s3:GetBucketTagging", "s3:GetEncryptionConfiguration", "s3:GetLifecycleConfiguration", "s3:GetReplicationConfiguration", "s3:ListBucket", "s3:ListBucketVersions", "s3:PutBucketVersioning", "s3:PutBucketAcl", "s3:PutBucketPolicy", "s3:PutBucketLogging", "s3:PutBucketNotification", "s3:PutBucketTagging", "s3:PutEncryptionConfiguration", "s3:PutLifecycleConfiguration", "s3:PutReplicationConfiguration", "s3:DeleteBucketPolicy", "s3:GetBucketCORS", "s3:PutBucketCORS", "s3:DeleteBucketCORS"], "Resource": ["arn:aws:s3:::*network*"]}, {"Sid": "S3ServicePermissions", "Effect": "Allow", "Action": ["s3:ListAllMyBuckets", "s3:GetBucketLocation", "s3:ListBucket"], "Resource": "*"}, {"Sid": "S3ObjectManagement", "Effect": "Allow", "Action": ["s3:GetObject"], "Resource": "arn:aws:s3:::*network*/*"}]}