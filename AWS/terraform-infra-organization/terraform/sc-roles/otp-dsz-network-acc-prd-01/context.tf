module "context" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-context?ref=v0.15.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  department  = var.department
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = "IacBot" #var.owner
    "Criticality" = "low"
  }
}