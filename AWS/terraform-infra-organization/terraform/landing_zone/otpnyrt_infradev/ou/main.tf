# OTPNyrt-`InfraDEV
resource "aws_organizations_organizational_unit" "otpnyrt-infradev" {
  name      = "OTPNyrt-InfraDEV"
  parent_id = "ou-glt4-mxjp353l"
}

import {
  to = aws_organizations_organizational_unit.otpnyrt-infradev
  id = "ou-glt4-xwc8c1q3"
}

resource "aws_organizations_organizational_unit" "landingzone-infradev" {
  name = "LandingZone-InfraDEV"
  parent_id = "ou-glt4-xwc8c1q3"
}

import {
  to = aws_organizations_organizational_unit.landingzone-infradev
  id = "ou-glt4-73xw58xk"
}

resource "aws_organizations_organizational_unit" "EDV" {
  name = "EDV"
  parent_id = "ou-glt4-73xw58xk"
}

import {
  to = aws_organizations_organizational_unit.EDV
  id = "ou-glt4-2o9d72cb"
}

resource "aws_organizations_organizational_unit" "RPD" {
  name = "RPD"
  parent_id = "ou-glt4-73xw58xk"
}

import {
  to = aws_organizations_organizational_unit.RPD
  id = "ou-glt4-i46lthwv"
}

resource "aws_organizations_organizational_unit" "RPR" {
  name = "RPR"
  parent_id = "ou-glt4-73xw58xk"
}

import {
  to = aws_organizations_organizational_unit.RPR
  id = "ou-glt4-qptwix8s"
}

resource "aws_organizations_organizational_unit" "STT" {
  name = "STT"
  parent_id = "ou-glt4-73xw58xk"
}

import {
  to = aws_organizations_organizational_unit.STT
  id = "ou-glt4-gr2tsot1"
}

# Platform InfraDEV

resource "aws_organizations_organizational_unit" "platform-infradev" {
  name = "Platform-InfraDEV"
  parent_id = "ou-glt4-xwc8c1q3"
}

import {
  to = aws_organizations_organizational_unit.platform-infradev
  id = "ou-glt4-qiwyw8sy"
}

resource "aws_organizations_organizational_unit" "management-infradev" {
  name = "Management-InfraDEV"
  parent_id = "ou-glt4-qiwyw8sy"
}

import {
  to = aws_organizations_organizational_unit.management-infradev
  id = "ou-glt4-61csgbeu"
}

resource "aws_organizations_organizational_unit" "network-infradev" {
  name = "Network-InfraDEV"
  parent_id = "ou-glt4-qiwyw8sy"
}

import {
  to = aws_organizations_organizational_unit.network-infradev
  id = "ou-glt4-y3we7suf"
}

resource "aws_organizations_organizational_unit" "security-infradev" {
  name = "Security-InfraDEV"
  parent_id = "ou-glt4-xwc8c1q3"
}

import {
  to = aws_organizations_organizational_unit.security-infradev
  id = "ou-glt4-tsp7bu08"
}