# OTPGroup
resource "aws_organizations_organizational_unit" "otpgroup" {
  name      = "OTPGroup"
  parent_id = "r-glt4"
}

import {
  to = aws_organizations_organizational_unit.otpgroup
  id = "ou-glt4-mxjp353l"
}

# OTPNyrt
resource "aws_organizations_organizational_unit" "otpnyrt" {
  name      = "OTPNyrt"
  parent_id = aws_organizations_organizational_unit.otpgroup.id
}

import {
  to = aws_organizations_organizational_unit.otpnyrt
  id = "ou-glt4-vuz1lf9y"
}

resource "aws_organizations_organizational_unit" "landingzone" {
  name      = "LandingZone"
  parent_id = aws_organizations_organizational_unit.otpnyrt.id
}

import {
  to = aws_organizations_organizational_unit.landingzone
  id = "ou-glt4-9n7xziar"
}

resource "aws_organizations_organizational_unit" "DEV" {
  name      = "DEV"
  parent_id = aws_organizations_organizational_unit.landingzone.id
}

import {
  to = aws_organizations_organizational_unit.DEV
  id = "ou-glt4-3pjo9amn"
}

resource "aws_organizations_organizational_unit" "PPR" {
  name      = "PPR"
  parent_id = aws_organizations_organizational_unit.landingzone.id
}

import {
  to = aws_organizations_organizational_unit.PPR
  id = "ou-glt4-ece0rybc"
}

resource "aws_organizations_organizational_unit" "PRD" {
  name      = "PRD"
  parent_id = aws_organizations_organizational_unit.landingzone.id
}

import {
  to = aws_organizations_organizational_unit.PRD
  id = "ou-glt4-x3u7fqk9"
}

resource "aws_organizations_organizational_unit" "TST" {
  name      = "TST"
  parent_id = aws_organizations_organizational_unit.landingzone.id
}

import {
  to = aws_organizations_organizational_unit.TST
  id = "ou-glt4-mozxqeue"
}

#Platform

resource "aws_organizations_organizational_unit" "platform" {
  name      = "Platform"
  parent_id = aws_organizations_organizational_unit.otpnyrt.id
}

import {
  to = aws_organizations_organizational_unit.platform
  id = "ou-glt4-fryra3zz"
}

resource "aws_organizations_organizational_unit" "management" {
  name      = "Management"
  parent_id = aws_organizations_organizational_unit.platform.id
}

import {
  to = aws_organizations_organizational_unit.management
  id = "ou-glt4-an6stei4"
}

resource "aws_organizations_organizational_unit" "network" {
  name      = "Network"
  parent_id = aws_organizations_organizational_unit.platform.id
}

import {
  to = aws_organizations_organizational_unit.network
  id = "ou-glt4-tbzz140z"
}

#Sandbox

resource "aws_organizations_organizational_unit" "sandbox" {
  name      = "Sandbox"
  parent_id = aws_organizations_organizational_unit.otpnyrt.id
}

import {
  to = aws_organizations_organizational_unit.sandbox
  id = "ou-glt4-0dqsgjth"
}

#Security

resource "aws_organizations_organizational_unit" "security" {
  name      = "Security"
  parent_id = aws_organizations_organizational_unit.otpnyrt.id
}

import {
  to = aws_organizations_organizational_unit.security
  id = "ou-glt4-6gifj8vn"
}

