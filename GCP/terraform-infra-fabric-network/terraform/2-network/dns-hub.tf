# /**
#  * Copyright 2024 Google LLC
#  *
#  * Licensed under the Apache License, Version 2.0 (the "License");
#  * you may not use this file except in compliance with the License.
#  * You may obtain a copy of the License at
#  *
#  *      http://www.apache.org/licenses/LICENSE-2.0
#  *
#  * Unless required by applicable law or agreed to in writing, software
#  * distributed under the License is distributed on an "AS IS" BASIS,
#  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  * See the License for the specific language governing permissions and
#  * limitations under the License.
#  */

# # tfdoc:file:description Landing DNS zones and peerings setup.

# # forwarding to on-prem DNS resolvers

module "hub-dns-fwd-onprem" {
  for_each    = local.dns-entry
  # source      = "../../modules/fabric/modules/dns"
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//dns?ref=v40.1.0"
  project_id  = module.hub-project.project_id
  name        = each.value["name"]
  zone_config = {
    domain = each.value["domain"]
    forwarding = {
      client_networks = [
        module.untrusted-vpc.self_link
      ]
      forwarders = { for ip in var.dns.resolvers : ip => null }
    }
  }
}