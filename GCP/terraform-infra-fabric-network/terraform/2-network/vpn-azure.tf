/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

# tfdoc:file:description VPN between GCP and Azure.

locals {
  vpn_azure_primary_config = {
    peer_external_gateways = {
      "gateway-01" = {
        redundancy_type = "TWO_IPS_REDUNDANCY"
        interfaces = [ "***********", "************" ]
        name = "${local.naming_convention.vpn_peer_gateway}-${local.region_shortnames[var.regions.primary]}-prd-azure-01"
      }
    }
    router_config = {
      asn       = **********
      name      = "${local.naming_convention.cloud_router}-${local.region_shortnames[var.regions.primary]}-prd-azure-01"
    }
    tunnels = {
      "tunnel-0" = {
        bgp_peer ={
          address                       = "************"
          asn                           = 65515
          route_priority                = 100
          name                          = "${local.naming_convention.vpn_bgp}-${local.region_shortnames[var.regions.primary]}-prd-azure-01"
        }
        bgp_session_range               = "************/30"
        peer_gateway                    = "gateway-01"
        peer_external_gateway_interface = 0
        vpn_gateway_interface           = 0
        shared_secret                   = "ZK!-JQS&&#~BJsM~KpJzuVb5tRFXY/"
        name                            = "${local.naming_convention.vpn_tunnel}-${local.region_shortnames[var.regions.primary]}-prd-azure-01"
      }
      "tunnel-1" = {
        bgp_peer ={
          address                       = "************"
          asn                           = 65515
          route_priority                = 100
          name                          = "${local.naming_convention.vpn_bgp}-${local.region_shortnames[var.regions.primary]}-prd-azure-02"
        }
        bgp_session_range               = "************/30"
        peer_gateway                    = "gateway-01"
        peer_external_gateway_interface = 1
        vpn_gateway_interface           = 1
        shared_secret                   = "ZK!-JQS&&#~BJsM~KpJzuVb5tRFXY/"
        name                            = "${local.naming_convention.vpn_tunnel}-${local.region_shortnames[var.regions.primary]}-prd-azure-02"
      }
    }
    default = null
  }

  azure_peer_gateways = {
    primary = local.vpn_azure_primary_config.peer_external_gateways
  }

  ## Temporary
  route_policy_azure_config = {
    "export-deny-onprem" = {
      policy = {
        priority  = 1
        match     = {
          expression = "destination == '***********/16' || destination == '************/22' || destination == '10.0.0.0/8' || destination == '**********/12'"
          title      = "export_expression"
          description = "drop expression for export"
        }
        actions  = {
          expression  = "drop()"
        }
      },
    }
    "import-deny-onprem" ={
      policy = {
        priority  = 1
        match     = {
          expression = "destination == '***********/16' || destination == '************/22' || destination == '10.0.0.0/8' || destination == '**********/12'"
          title      = "import_expression"
          description = "drop expression for import"
        }
        actions  = {
          expression  = "drop()"
        }
      }
    }
  }
}

module "untrusted-to-azure-primary-vpn" {
  # source        = "../../modules/custom/net-vpn-ha"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//custom/net-vpn-ha?ref=v40.1.0"
  project_id    = module.hub-project.project_id
  network       = module.untrusted-vpc.self_link
  region        = var.regions.primary
  name          = "${local.naming_convention.vpn_gateway}-${local.region_shortnames[var.regions.primary]}-prd-azure-01"
  router_config = local.vpn_azure_primary_config.router_config
  peer_gateways = {
    for k, v in local.azure_peer_gateways.primary : k => { external = v }
  }
  tunnels = local.vpn_azure_primary_config.tunnels
  route_policy = local.route_policy_azure_config
}