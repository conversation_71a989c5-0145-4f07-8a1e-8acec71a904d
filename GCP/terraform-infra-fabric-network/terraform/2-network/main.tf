locals {
  admin_group = [ 
                  "user:<EMAIL>",
                  "user:<EMAIL>",
                  "user:<PERSON><PERSON><PERSON><PERSON><PERSON>@otpbank.hu",
                  "user:<EMAIL>",
                  "user:norbert.p<PERSON><PERSON><PERSON><PERSON>@otpbank.hu",
                  "user:<EMAIL>",
                  "user:<EMAIL>",
                  "group:<EMAIL>"
                  # "user:<EMAIL>",
                  # "user:<EMAIL>"
                ]

  # service_accounts = {
  #   for k, v in coalesce(local.service_accounts, {}) :
  #   k => "serviceAccount:${v}" if v != null
  # }

  stage3_sas_delegated_grants = [
    "roles/composer.sharedVpcAgent",
    "roles/compute.networkUser",
    "roles/compute.networkViewer",
    "roles/container.hostServiceAgentUser",
    "roles/multiclusterservicediscovery.serviceAgent",
    "roles/vpcaccess.user",
  ]
  regions = var.regions

  dns-entry = {
    onprem-otp = {
      name    = "otp"
      domain  = "otp."
    }
    onprem-netlock = {
      name    = "netlock-hu"
      domain  = "netlock.hu."
    }
    onprem-cloudmgmt = {
      name    = "cloud-mgmt"
      domain  = "cloud.mgmt."
    }
    onprem-otpgroup = {
      name    = "otp-group"
      domain  = "otp.group."
    }
    onprem-otpnexus = {
      name    = "otpnexus-hu"
      domain  = "otpnexus.hu."
    }
    onprem-otptesztnexus = {
      name    = "otptesztnexus-hu"
      domain  = "otptesztnexus.hu."
    }
  }
}