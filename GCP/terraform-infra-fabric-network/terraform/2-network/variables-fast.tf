/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

# variable "automation" {
#   # tfdoc:variable:source 0-bootstrap
#   description = "Automation resources created by the bootstrap stage."
#   type = object({
#     outputs_bucket = string
#   })
# }

# variable "folder_ids" {
#   # tfdoc:variable:source 1-resman
#   description = "Folders to be used for the networking resources in folders/nnnnnnnnnnn format. If null, folder will be created."
#   type = object({
#     platform-networking      = string
#   })
# }

# variable "organization" {
#   # tfdoc:variable:source 0-bootstrap
#   description = "Organization details."
#   type = object({
#     domain      = string
#     id          = number
#     customer_id = string
#   })
# }

# variable "prefix" {
#   # tfdoc:variable:source 0-bootstrap
#   description = "Prefix used for resources that need unique names. Use a maximum of 9 chars for organizations, and 11 chars for tenants."
#   type        = string
#   validation {
#     condition     = try(length(var.prefix), 0) < 12
#     error_message = "Use a maximum of 9 chars for organizations, and 11 chars for tenants."
#   }
# }

# variable "service_accounts" {
#   # tfdoc:variable:source 1-resman
#   description = "Automation service accounts in name => email format."
#   type = object({
#     platform-networking = string
#     landing-zones       = string
#   })
#   default = null
# }

# variable "billing_account" {
#   description = "Billing account id. If billing account is not part of the same org set `is_org_level` to `false`. To disable handling of billing IAM roles set `no_iam` to `true`."
#   type = object({
#     id           = string
#   })
#   nullable = false
# }


# variable "naming_convention" {
#   description = "Naming convention standard for resource and region"
#   type = object({
#     cloud_router        = string
#     project             = string
#     vpc                 = string
#     vpn_bgp             = string
#     vpn_gateway         = string
#     vpn_peer_gateway    = string
#     vpn_tunnel          = string
#     region_global       = string
#   })
#   nullable = false
# }
