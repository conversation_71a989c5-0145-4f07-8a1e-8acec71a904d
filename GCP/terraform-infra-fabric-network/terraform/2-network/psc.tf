module "hub-psc" {
  # source     = "../../modules/fabric/modules/net-address"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-address?ref=v40.1.0"
  project_id = module.hub-project.project_id
  psc_addresses = {
    prdhub01 = {
      address = "**************"
      network = module.trusted-vpc.self_link
      service_attachment = {
        psc_service_attachment_link = "all-apis"
      }
    }
  }
}