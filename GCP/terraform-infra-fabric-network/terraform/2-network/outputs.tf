/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

locals {
  host_project_ids = {
    prod-hub = module.hub-project.project_id
  }
  host_project_numbers = {
    prod-hub = module.hub-project.number
  }
  subnet_self_links = {
    prod-untrusted     = module.untrusted-vpc.subnet_self_links
    prod-management     = module.management-vpc.subnet_self_links
    prod-trusted = module.trusted-vpc.subnet_self_links
  }
  tfvars = {
    host_project_ids             = local.host_project_ids
    host_project_numbers         = local.host_project_numbers
    subnet_self_links            = local.subnet_self_links
    vpc_self_links               = local.vpc_self_links
  }
  vpc_self_links = {
    prod-untrusted = module.untrusted-vpc.self_link
    prod-management = module.management-vpc.self_link
    prod-trusted     = module.trusted-vpc.self_link
  }
}

resource "google_storage_bucket_object" "tfvars" {
  bucket  = local.outputs_bucket
  name    = "tfvars/2-networking.auto.tfvars.json"
  content = jsonencode(local.tfvars)
}

# outputs

output "host_project_ids" {
  description = "Network project ids."
  value       = local.host_project_ids
}

output "host_project_numbers" {
  description = "Network project numbers."
  value       = local.host_project_numbers
}

output "shared_vpc_self_links" {
  description = "Shared VPC host projects."
  value       = local.vpc_self_links
}

output "tfvars" {
  description = "Terraform variables file for the following stages."
  sensitive   = true
  value       = local.tfvars
}