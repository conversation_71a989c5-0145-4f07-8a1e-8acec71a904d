/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

# tfdoc:file:description Hub VPC and related resources.

module "hub-project" {
  # source          = "../../modules/fabric/modules/project"
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//project?ref=v40.1.0"
  billing_account = local.billing_account.id
  name            = "diszi-hub-${local.naming_convention.project}-prd-01"
  parent          = local.folder_ids.platform-networking
  prefix          = local.prefix
  services = concat([
    "compute.googleapis.com",
    "dns.googleapis.com",
    "iap.googleapis.com",
    "networkmanagement.googleapis.com",
    "stackdriver.googleapis.com",
    "servicedirectory.googleapis.com",
    ],
  )

  ## Temporary
  iam = {
    "roles/compute.admin" = concat(
      local.admin_group,
    ),
    "roles/owner" = concat(
      local.admin_group,
    ),
    "roles/compute.networkAdmin" = concat(
      [ "serviceAccount:${local.service_accounts.landing-zones}" ],
    ),
    "roles/dns.peer" = concat(
      [ "serviceAccount:${local.service_accounts.landing-zones}" ],
    ),
  }
}

# Management VPC

module "management-vpc" {
  # source     = "../../modules/fabric/modules/net-vpc"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc?ref=v40.1.0"
  project_id = module.hub-project.project_id
  name       = "${local.naming_convention.vpc}-${local.naming_convention.region_global}-prd-management-01"
  mtu        = 1500
  dns_policy = {
    inbound = true
    # logging = var.dns.enable_logging
  }
  create_googleapis_routes = {
    private    = true
    restricted = true
  }
  factories_config = {
    context        = { regions = var.regions }
    subnets_folder = "${var.factories_config.data_dir}/subnets/management"
  }
  delete_default_routes_on_create = true
}

module "management-firewall" {
  # source     = "../../modules/fabric/modules/net-vpc-firewall"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc-firewall?ref=v40.1.0"
  project_id = module.hub-project.project_id
  network    = module.management-vpc.name
  default_rules_config = {
    disabled = true
  }
}

# Trusted VPC

module "trusted-vpc" {
  # source                          = "../../modules/fabric/modules/net-vpc"
  source                          = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc?ref=v40.1.0"
  project_id                      = module.hub-project.project_id
  name                            = "${local.naming_convention.vpc}-${local.naming_convention.region_global}-prd-trusted-01"
  delete_default_routes_on_create = true
  mtu                             = 1500
  factories_config = {
    context        = { regions = var.regions }
    subnets_folder = "${var.factories_config.data_dir}/subnets/trusted"
  }
  dns_policy = {
    inbound = true
  }
  # Set explicit routes for googleapis in case the default route is deleted
  create_googleapis_routes = {
    private    = true
    restricted = true
  }
}

module "trusted-firewall" {
  # source     = "../../modules/fabric/modules/net-vpc-firewall"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc-firewall?ref=v40.1.0"
  project_id = module.hub-project.project_id
  network    = module.trusted-vpc.name
  default_rules_config = {
    disabled = true
  }
}

# Untrusted VPC

module "untrusted-vpc" {
  # source                          = "../../modules/fabric/modules/net-vpc"
  source                          = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc?ref=v40.1.0"
  project_id                      = module.hub-project.project_id
  name                            = "${local.naming_convention.vpc}-${local.naming_convention.region_global}-prd-untrusted-01"
  delete_default_routes_on_create = true
  mtu                             = 1500
  factories_config = {
    context        = { regions = var.regions }
    subnets_folder = "${var.factories_config.data_dir}/subnets/untrusted"
  }
  dns_policy = {
    inbound = true
  }
  # Set explicit routes for googleapis in case the default route is deleted
  create_googleapis_routes = {
    private    = true
    restricted = true
  }
}

module "untrusted-firewall" {
  # source     = "../../modules/fabric/modules/net-vpc-firewall"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//net-vpc-firewall?ref=v40.1.0"
  project_id = module.hub-project.project_id
  network    = module.untrusted-vpc.name
  default_rules_config = {
    disabled = true
  }
}
