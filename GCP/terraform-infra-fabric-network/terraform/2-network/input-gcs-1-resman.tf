locals {
  input_tfvars_resman = jsondecode(data.google_storage_bucket_object_content.tfvars_resman.content)
  service_accounts  = local.input_tfvars_resman["service_accounts"]
  folder_ids        = local.input_tfvars_resman["folder_ids"]
  logging          = local.input_tfvars_resman["logging"]
}

data "google_storage_bucket_object_content" "tfvars_resman" {
  bucket = local.outputs_bucket
  name   = "tfvars/1-resman.auto.tfvars.json"
}