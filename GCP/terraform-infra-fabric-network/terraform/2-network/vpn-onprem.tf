/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

# tfdoc:file:description VPN between GCP and onprem.

locals {
  vpn_onprem_primary_config = {
    peer_external_gateways = {
      "gateway-01" = {
        redundancy_type = "TWO_IPS_REDUNDANCY"
        interfaces = [ "*************", "*************" ]
        name = "${local.naming_convention.vpn_peer_gateway}-${local.region_shortnames[var.regions.primary]}-prd-onprem-01"
      }
    }
    router_config = {
      asn       = **********
      name      = "${local.naming_convention.cloud_router}-${local.region_shortnames[var.regions.primary]}-prd-onprem-01"
      custom_advertise = {
        all_subnets = true
        ip_ranges = {
          "**********/16"   = "Workload"
          "**********/22"   = "Management"
          "************/19" = ""
        }
      }
    }
    tunnels = {
      "tunnel-0" = {
        bgp_peer ={
          address                       = "***********"
          asn                           = 64518
          route_priority                = 100
          name                          = "${local.naming_convention.vpn_bgp}-${local.region_shortnames[var.regions.primary]}-prd-onprem-01"
        }
        bgp_session_range               = "***********/30"
        peer_gateway                    = "gateway-01"
        peer_external_gateway_interface = 0
        vpn_gateway_interface           = 0
        shared_secret                   = "zgIYJyi@!ycV!_!INvL7Qz_@YIH@G2"
        name                            = "${local.naming_convention.vpn_tunnel}-${local.region_shortnames[var.regions.primary]}-prd-onprem-01"
      }
      "tunnel-1" = {
        bgp_peer ={
          address                       = "***********"
          asn                           = 64518
          route_priority                = 100
          name                          = "${local.naming_convention.vpn_bgp}-${local.region_shortnames[var.regions.primary]}-prd-onprem-02"
        }
        bgp_session_range               = "***********/30"
        peer_gateway                    = "gateway-01"
        peer_external_gateway_interface = 1
        vpn_gateway_interface           = 1
        shared_secret                   = "Q=#g#C$A9PwqCX2:PCFVJSl#PG.M%l"
        name                            = "${local.naming_convention.vpn_tunnel}-${local.region_shortnames[var.regions.primary]}-prd-onprem-02"
      }
    }
    default = null
  }

  onprem_peer_gateways = {
    primary = local.vpn_onprem_primary_config.peer_external_gateways
  }
}

module "untrusted-to-onprem-primary-vpn" {
  # source        = "../../modules/fabric/modules/net-vpn-ha"
  source     = "git::https://<EMAIL>/ADOS-OTPHU-01/gcp-lz/_git/terraform-google-cloud-foundation-fabric//custom/net-vpn-ha?ref=v40.1.0"
  project_id    = module.hub-project.project_id
  network       = module.untrusted-vpc.self_link
  region        = var.regions.primary
  name          = "${local.naming_convention.vpn_gateway}-${local.region_shortnames[var.regions.primary]}-prd-onprem-01"
  router_config = local.vpn_onprem_primary_config.router_config
  peer_gateways = {
    for k, v in local.onprem_peer_gateways.primary : k => { external = v }
  }
  tunnels = local.vpn_onprem_primary_config.tunnels
}