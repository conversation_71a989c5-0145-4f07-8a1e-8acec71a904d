locals {
  outputs_bucket = "gcst-euu-prd-outputs-01"

  # TFVARS
  input_tfvars_bootstrap = jsondecode(data.google_storage_bucket_object_content.tfvars_bootstrap.content)
  automation   = local.input_tfvars_bootstrap["automation"]
  custom_roles = local.input_tfvars_bootstrap["custom_roles"]

  # TFVARS globals
  input_tfvars_globals = jsondecode(data.google_storage_bucket_object_content.tfvars_globals.content)
  billing_account   = local.input_tfvars_globals["billing_account"]
  organization      = local.input_tfvars_globals["organization"]
  organization_id   = "************"
  prefix            = local.input_tfvars_globals["prefix"]
  locations         = local.input_tfvars_globals["locations"]
  naming_convention = local.input_tfvars_globals["naming_convention"]
}

data "google_storage_bucket_object_content" "tfvars_bootstrap" {
  bucket = local.outputs_bucket
  name   = "tfvars/0-bootstrap.auto.tfvars.json"
}

data "google_storage_bucket_object_content" "tfvars_globals" {
  bucket = local.outputs_bucket
  name   = "tfvars/0-globals.auto.tfvars.json"
}