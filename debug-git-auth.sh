#!/bin/bash

# ============================================================================
# Azure DevOps Git Authentication Debug Script
# ============================================================================
# This script helps debug git authentication issues with Azure DevOps
# when accessing repositories across different projects.
#
# Usage:
#   1. Set your PAT token: export AZURE_PAT="your_pat_token_here"
#   2. Run the script: ./debug-git-auth.sh
#
# Or run with PAT token inline:
#   AZURE_PAT="your_pat_token_here" ./debug-git-auth.sh
# ============================================================================

set -euo pipefail

# Configuration
ORG="ADOS-OTPHU-01"
SOURCE_PROJECT="OTPHU-COE-TEMPLATESPEC"
TARGET_PROJECT="OTPHU-CDO-ADOS-TOOLS"
REPO_NAME="pipelinetemplates"
BRANCH="v8"
USERNAME="ADOS-OTPHU-01"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PAT token is provided
if [[ -z "${AZURE_PAT:-}" ]]; then
    log_error "AZURE_PAT environment variable is not set!"
    echo ""
    echo "Usage:"
    echo "  export AZURE_PAT=\"your_pat_token_here\""
    echo "  $0"
    echo ""
    echo "Or:"
    echo "  AZURE_PAT=\"your_pat_token_here\" $0"
    exit 1
fi

log_info "Starting Azure DevOps Git Authentication Debug"
echo "============================================================================"
log_info "Configuration:"
echo "  Organization: $ORG"
echo "  Source Project: $SOURCE_PROJECT"
echo "  Target Project: $TARGET_PROJECT"
echo "  Repository: $REPO_NAME"
echo "  Branch: $BRANCH"
echo "  Username: $USERNAME"
echo "  PAT Token Length: ${#AZURE_PAT} characters"
echo "  PAT Token Starts: ${AZURE_PAT:0:8}..."
echo ""

# Test 1: List repositories in target project
log_info "Test 1: Listing repositories in target project..."
REPO_LIST_URL="https://dev.azure.com/$ORG/$TARGET_PROJECT/_apis/git/repositories?api-version=6.0"
echo "URL: $REPO_LIST_URL"

REPO_LIST=$(curl -s -u "$USERNAME:$AZURE_PAT" "$REPO_LIST_URL")
if echo "$REPO_LIST" | jq -e '.value' > /dev/null 2>&1; then
    REPO_COUNT=$(echo "$REPO_LIST" | jq -r '.count // 0')
    log_success "Successfully connected to Azure DevOps API"
    log_info "Found $REPO_COUNT repositories"
    
    if [[ "$REPO_COUNT" -gt 0 ]]; then
        echo "Available repositories:"
        echo "$REPO_LIST" | jq -r '.value[] | "  - " + .name' | head -20
    else
        log_warning "No repositories found - PAT token may lack 'Code (read)' permission"
    fi
else
    log_error "Failed to connect to Azure DevOps API"
    echo "Response: $REPO_LIST"
    exit 1
fi

echo ""

# Test 2: Check specific repository access
log_info "Test 2: Testing access to specific repository '$REPO_NAME'..."
REPO_URL="https://dev.azure.com/$ORG/$TARGET_PROJECT/_apis/git/repositories/$REPO_NAME?api-version=6.0"
echo "URL: $REPO_URL"

HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -u "$USERNAME:$AZURE_PAT" "$REPO_URL")
echo "HTTP Status: $HTTP_STATUS"

if [[ "$HTTP_STATUS" == "200" ]]; then
    log_success "PAT token has access to '$REPO_NAME' repository"
elif [[ "$HTTP_STATUS" == "401" ]]; then
    log_error "401 Unauthorized - PAT token lacks permission to access '$REPO_NAME'"
    log_info "Required permissions: 'Code (read)' and access to $TARGET_PROJECT project"
elif [[ "$HTTP_STATUS" == "404" ]]; then
    log_error "404 Not Found - repository '$REPO_NAME' does not exist"
    log_info "Searching for repositories with 'template' or 'pipeline' in name..."
    
    if [[ "$REPO_COUNT" -gt 0 ]]; then
        MATCHING_REPOS=$(echo "$REPO_LIST" | jq -r '.value[]? | select(.name | test("template|pipeline"; "i")) | .name')
        if [[ -n "$MATCHING_REPOS" ]]; then
            echo "Found matching repositories:"
            echo "$MATCHING_REPOS" | sed 's/^/  - /'
        else
            log_warning "No repositories found with 'template' or 'pipeline' in name"
        fi
    fi
else
    log_error "Unexpected HTTP status: $HTTP_STATUS"
fi

echo ""

# Test 3: Test git configuration
log_info "Test 3: Testing git configuration..."
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
log_info "Working in temporary directory: $TEMP_DIR"

# Setup git config
log_info "Setting up git authentication..."
git config --global url."https://$USERNAME:$<EMAIL>/$ORG/$TARGET_PROJECT/_git/".insteadOf "https://dev.azure.com/$ORG/$TARGET_PROJECT/_git/"

# Show git config
echo "Git configuration:"
git config --global --list | grep -E "(url\.|credential)" || echo "  No relevant git config found"

echo ""

# Test 4: Attempt git clone
log_info "Test 4: Attempting git clone..."
CLONE_URL="https://dev.azure.com/$ORG/$TARGET_PROJECT/_git/$REPO_NAME/"
echo "Clone URL: $CLONE_URL"
echo "Branch: $BRANCH"

if git clone --depth 1 --branch "$BRANCH" "$CLONE_URL" "$REPO_NAME"; then
    log_success "Git clone successful!"
    echo "Repository contents:"
    ls -la "$REPO_NAME/" | head -10
    
    # Check for checkov policies
    if [[ -d "$REPO_NAME/resources/checkovpolicies" ]]; then
        log_success "Found checkovpolicies directory"
        ls -la "$REPO_NAME/resources/checkovpolicies/" | head -5
    else
        log_warning "checkovpolicies directory not found"
        echo "Available directories in resources/:"
        ls -la "$REPO_NAME/resources/" 2>/dev/null || echo "  resources/ directory not found"
    fi
else
    log_error "Git clone failed!"
    
    # Try alternative method
    log_info "Trying alternative authentication method..."
    git config --global credential.helper store
    echo "https://$USERNAME:$<EMAIL>" > ~/.git-credentials
    
    if git clone --depth 1 --branch "$BRANCH" "$CLONE_URL" "$REPO_NAME-alt"; then
        log_success "Alternative method successful!"
    else
        log_error "Both authentication methods failed!"
    fi
fi

echo ""

# Cleanup
log_info "Cleaning up..."
cd /
rm -rf "$TEMP_DIR"
git config --global --unset url."https://$USERNAME:$<EMAIL>/$ORG/$TARGET_PROJECT/_git/".insteadOf || true
rm -f ~/.git-credentials

echo ""
log_info "Debug script completed!"
echo "============================================================================"
