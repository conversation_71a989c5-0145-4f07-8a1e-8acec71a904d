2025-10-10T13:07:07.9478197Z ##[section]Starting: Checkov scan
2025-10-10T13:07:07.9481522Z ==============================================================================
2025-10-10T13:07:07.9481639Z Task         : Bash
2025-10-10T13:07:07.9481693Z Description  : Run a Bash script on macOS, Linux, or Windows
2025-10-10T13:07:07.9481781Z Version      : 3.259.0
2025-10-10T13:07:07.9481836Z Author       : Microsoft Corporation
2025-10-10T13:07:07.9481913Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/bash
2025-10-10T13:07:07.9482013Z ==============================================================================
2025-10-10T13:07:10.0220222Z Generating script.
2025-10-10T13:07:10.0229616Z ========================== Starting Command Output ===========================
2025-10-10T13:07:10.0236574Z [command]/usr/bin/bash /home/<USER>/azp/_work/_temp/000049f2-b51c-43a3-884c-8e76d2d13871.sh
2025-10-10T13:07:10.1171508Z ADOS-OTPHU-19/celia-test project is not in the exemptions.
2025-10-10T13:07:10.1176456Z extra params:  -d /home/<USER>/azp/_work/1/source_repo/terraform/celia --var-file /home/<USER>/azp/_work/1/config_repo/applications/celia/TST/celia.tfvars  --skip-check CKV_TF_1 --download-external-modules true 
2025-10-10T13:07:34.2639620Z 
2025-10-10T13:07:34.2640352Z        _               _              
2025-10-10T13:07:34.2640740Z    ___| |__   ___  ___| | _______   __
2025-10-10T13:07:34.2642012Z   / __| '_ \ / _ \/ __| |/ / _ \ \ / /
2025-10-10T13:07:34.2642330Z  | (__| | | |  __/ (__|   < (_) \ V / 
2025-10-10T13:07:34.2642675Z   \___|_| |_|\___|\___|_|\_\___/ \_/  
2025-10-10T13:07:34.2642973Z                                       
2025-10-10T13:07:34.2643277Z By Prisma Cloud | version: 3.2.1 
2025-10-10T13:07:34.2643424Z 
2025-10-10T13:07:34.2743843Z terraform scan results:
2025-10-10T13:07:34.2744102Z 
2025-10-10T13:07:34.2744382Z Passed checks: 106, Failed checks: 21, Skipped checks: 64
2025-10-10T13:07:34.2744546Z 
2025-10-10T13:07:34.2744829Z Check: CKV_AZURE_163: "Enable vulnerability scanning for container images."
2025-10-10T13:07:34.2745235Z 	PASSED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2746026Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2746643Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2747319Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-163
2025-10-10T13:07:34.2747725Z Check: CKV_AZURE_138: "Ensures that ACR disables anonymous pulling of images"
2025-10-10T13:07:34.2748118Z 	PASSED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2749102Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2749769Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2750628Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-acr-disables-anonymous-image-pulling
2025-10-10T13:07:34.2751065Z Check: CKV_AZURE_139: "Ensure ACR set to disable public networking"
2025-10-10T13:07:34.2751443Z 	PASSED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2752010Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2752567Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2753138Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-azure-acr-is-set-to-disable-public-networking
2025-10-10T13:07:34.2753777Z Check: CKV_AZURE_137: "Ensure ACR admin account is disabled"
2025-10-10T13:07:34.2754076Z 	PASSED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2754593Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2755402Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2755973Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-137
2025-10-10T13:07:34.2756361Z Check: CKV_AZURE_163: "Enable vulnerability scanning for container images."
2025-10-10T13:07:34.2756714Z 	PASSED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2757248Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2757790Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2758327Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-163
2025-10-10T13:07:34.2758718Z Check: CKV_AZURE_138: "Ensures that ACR disables anonymous pulling of images"
2025-10-10T13:07:34.2759101Z 	PASSED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2759688Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2760330Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2761032Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-acr-disables-anonymous-image-pulling
2025-10-10T13:07:34.2761442Z Check: CKV_AZURE_139: "Ensure ACR set to disable public networking"
2025-10-10T13:07:34.2761804Z 	PASSED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2762285Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2762867Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2763450Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-azure-acr-is-set-to-disable-public-networking
2025-10-10T13:07:34.2764098Z Check: CKV_AZURE_137: "Ensure ACR admin account is disabled"
2025-10-10T13:07:34.2764487Z 	PASSED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.2765116Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2765810Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.2766473Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-137
2025-10-10T13:07:34.2766897Z Check: CKV_AZURE_163: "Enable vulnerability scanning for container images."
2025-10-10T13:07:34.2767287Z 	PASSED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2767890Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2768305Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.2768882Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-163
2025-10-10T13:07:34.2769411Z Check: CKV_AZURE_138: "Ensures that ACR disables anonymous pulling of images"
2025-10-10T13:07:34.2769786Z 	PASSED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2770384Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2770790Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.2771440Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-acr-disables-anonymous-image-pulling
2025-10-10T13:07:34.2771902Z Check: CKV_AZURE_139: "Ensure ACR set to disable public networking"
2025-10-10T13:07:34.2772272Z 	PASSED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2772871Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2773274Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.2773926Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-azure-acr-is-set-to-disable-public-networking
2025-10-10T13:07:34.2774395Z Check: CKV_AZURE_137: "Ensure ACR admin account is disabled"
2025-10-10T13:07:34.2774757Z 	PASSED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2775354Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2775756Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.2776309Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-137
2025-10-10T13:07:34.2776748Z Check: CKV_AZURE_163: "Enable vulnerability scanning for container images."
2025-10-10T13:07:34.2777129Z 	PASSED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2777736Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2778144Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.2778721Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-163
2025-10-10T13:07:34.2779163Z Check: CKV_AZURE_138: "Ensures that ACR disables anonymous pulling of images"
2025-10-10T13:07:34.2779541Z 	PASSED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2780139Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2780596Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.2781231Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-acr-disables-anonymous-image-pulling
2025-10-10T13:07:34.2781689Z Check: CKV_AZURE_139: "Ensure ACR set to disable public networking"
2025-10-10T13:07:34.2782060Z 	PASSED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2782642Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2783038Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.2783704Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-azure-acr-is-set-to-disable-public-networking
2025-10-10T13:07:34.2784152Z Check: CKV_AZURE_137: "Ensure ACR admin account is disabled"
2025-10-10T13:07:34.2784533Z 	PASSED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.2785130Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.2785569Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.2786139Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-137
2025-10-10T13:07:34.2786554Z Check: CKV_AZURE_6: "Ensure AKS has an API Server Authorized IP Ranges enabled"
2025-10-10T13:07:34.2786942Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2787551Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2787953Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2788563Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-3
2025-10-10T13:07:34.2789022Z Check: CKV_AZURE_169: "Ensure Azure Kubernetes Cluster (AKS) nodes use scale sets"
2025-10-10T13:07:34.2789399Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2790012Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2790415Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2791001Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-169
2025-10-10T13:07:34.2791457Z Check: CKV_AZURE_172: "Ensure autorotation of Secrets Store CSI Driver secrets for AKS clusters"
2025-10-10T13:07:34.2791857Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2792464Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2792870Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2793446Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-172
2025-10-10T13:07:34.2793858Z Check: CKV_AZURE_8: "Ensure Kubernetes Dashboard is disabled"
2025-10-10T13:07:34.2794223Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2794984Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2795364Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2795958Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-5
2025-10-10T13:07:34.2796381Z Check: CKV_AZURE_5: "Ensure RBAC is enabled on AKS clusters"
2025-10-10T13:07:34.2796757Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2797449Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2797851Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2798449Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-2
2025-10-10T13:07:34.2799260Z Check: CKV_AZURE_116: "Ensure that AKS uses Azure Policies Add-on"
2025-10-10T13:07:34.2799646Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2800265Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2800679Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2801319Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-uses-azure-policies-add-on
2025-10-10T13:07:34.2801784Z Check: CKV_AZURE_143: "Ensure AKS cluster nodes do not have public IP addresses"
2025-10-10T13:07:34.2802171Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2802857Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2803272Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2803847Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azure-143
2025-10-10T13:07:34.2804259Z Check: CKV_AZURE_141: "Ensure AKS local admin account is disabled"
2025-10-10T13:07:34.2804628Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2805230Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2805646Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2806318Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-kubernetes-service-aks-local-admin-account-is-disabled
2025-10-10T13:07:34.2806799Z Check: CKV_AZURE_115: "Ensure that AKS enables private clusters"
2025-10-10T13:07:34.2807159Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2807766Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2808171Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2808801Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-enables-private-clusters
2025-10-10T13:07:34.2809276Z Check: CKV_AZURE_4: "Ensure AKS logging to Azure Monitoring is Configured"
2025-10-10T13:07:34.2809648Z 	PASSED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2810269Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2810673Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.2811260Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-1
2025-10-10T13:07:34.2811695Z Check: CKV_AZURE_6: "Ensure AKS has an API Server Authorized IP Ranges enabled"
2025-10-10T13:07:34.2812079Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2812697Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2813115Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2813706Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-3
2025-10-10T13:07:34.2814207Z Check: CKV_AZURE_169: "Ensure Azure Kubernetes Cluster (AKS) nodes use scale sets"
2025-10-10T13:07:34.2814595Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2815209Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2815621Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2816212Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-169
2025-10-10T13:07:34.2816668Z Check: CKV_AZURE_172: "Ensure autorotation of Secrets Store CSI Driver secrets for AKS clusters"
2025-10-10T13:07:34.2817070Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2817683Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2818092Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2818669Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-172
2025-10-10T13:07:34.2819144Z Check: CKV_AZURE_8: "Ensure Kubernetes Dashboard is disabled"
2025-10-10T13:07:34.2819504Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2820131Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2820531Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2821120Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-5
2025-10-10T13:07:34.2821542Z Check: CKV_AZURE_5: "Ensure RBAC is enabled on AKS clusters"
2025-10-10T13:07:34.2821899Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2822524Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2822926Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2823512Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-2
2025-10-10T13:07:34.2823943Z Check: CKV_AZURE_116: "Ensure that AKS uses Azure Policies Add-on"
2025-10-10T13:07:34.2824305Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2824928Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2825330Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2825967Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-uses-azure-policies-add-on
2025-10-10T13:07:34.2826442Z Check: CKV_AZURE_143: "Ensure AKS cluster nodes do not have public IP addresses"
2025-10-10T13:07:34.2826825Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2827453Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2827805Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2828267Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azure-143
2025-10-10T13:07:34.2828596Z Check: CKV_AZURE_141: "Ensure AKS local admin account is disabled"
2025-10-10T13:07:34.2828865Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2829365Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2829698Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2830382Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/ensure-azure-kubernetes-service-aks-local-admin-account-is-disabled
2025-10-10T13:07:34.2830793Z Check: CKV_AZURE_115: "Ensure that AKS enables private clusters"
2025-10-10T13:07:34.2831071Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2831586Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2831947Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2832496Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-enables-private-clusters
2025-10-10T13:07:34.2832893Z Check: CKV_AZURE_4: "Ensure AKS logging to Azure Monitoring is Configured"
2025-10-10T13:07:34.2833229Z 	PASSED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.2833816Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.2834183Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.2835823Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-1
2025-10-10T13:07:34.2836254Z Check: CKV_AZURE_218: "Ensure Application Gateway defines secure protocols for in transit communication"
2025-10-10T13:07:34.2836601Z 	PASSED for resource: module.shared_infra.module.appgw01.azurerm_application_gateway.apgw
2025-10-10T13:07:34.2837169Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-application-gateway/v3.1.0/main.tf:2-219
2025-10-10T13:07:34.2837520Z 	Calling File: /../modules/shared/appgw.tf:185-242
2025-10-10T13:07:34.2838089Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-218
2025-10-10T13:07:34.2838555Z Check: CKV_AZURE_122: "Ensure that Application Gateway uses WAF in "Detection" or "Prevention" modes"
2025-10-10T13:07:34.2838995Z 	PASSED for resource: module.shared_infra.module.appgw_wafp.azurerm_web_application_firewall_policy.wafp
2025-10-10T13:07:34.2839653Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-waf/v1.0.0/main.tf:2-137
2025-10-10T13:07:34.2840070Z 	Calling File: /../modules/shared/appgw.tf:153-183
2025-10-10T13:07:34.2840787Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-application-gateway-uses-waf-in-detection-or-prevention-modes
2025-10-10T13:07:34.2841276Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2841704Z 	PASSED for resource: module.aps.module.aks.module.acre.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2842391Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2843087Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2843810Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2844261Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2844692Z 	PASSED for resource: module.aps.module.aks.module.acre.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2845372Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2846076Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2847006Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2847482Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2847898Z 	PASSED for resource: module.flowx.module.aks.module.acre.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2848578Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2849284Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2849990Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2850458Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2850870Z 	PASSED for resource: module.flowx.module.aks.module.acre.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2851636Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2852340Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2853071Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2853544Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2853948Z 	PASSED for resource: module.flowx.module.acr.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2854633Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2858618Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2859351Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2859794Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2860194Z 	PASSED for resource: module.flowx.module.acr.module.keyvaultsecret01.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2860872Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2861557Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:65-74
2025-10-10T13:07:34.2862308Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2862781Z Check: CKV_AZURE_189: "Ensure that Azure Key Vault disables public network access"
2025-10-10T13:07:34.2863170Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2863820Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2864237Z 	Calling File: /../modules/shared/appgw.tf:112-129
2025-10-10T13:07:34.2864848Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-189
2025-10-10T13:07:34.2865285Z Check: CKV_AZURE_109: "Ensure that key vault allows firewall rules settings"
2025-10-10T13:07:34.2865807Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2866447Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2866865Z 	Calling File: /../modules/shared/appgw.tf:112-129
2025-10-10T13:07:34.2867547Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-key-vault-allows-firewall-rules-settings
2025-10-10T13:07:34.2868012Z Check: CKV_AZURE_110: "Ensure that key vault enables purge protection"
2025-10-10T13:07:34.2868404Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2869041Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2869471Z 	Calling File: /../modules/shared/appgw.tf:112-129
2025-10-10T13:07:34.2870129Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-purge-protection
2025-10-10T13:07:34.2870579Z Check: CKV_AZURE_111: "Ensure that key vault enables soft delete"
2025-10-10T13:07:34.2871011Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2871668Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2872083Z 	Calling File: /../modules/shared/appgw.tf:112-129
2025-10-10T13:07:34.2872733Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-soft-delete
2025-10-10T13:07:34.2873168Z Check: CKV_AZURE_42: "Ensure the key vault is recoverable"
2025-10-10T13:07:34.2873534Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2874190Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2874618Z 	Calling File: /../modules/shared/appgw.tf:112-129
2025-10-10T13:07:34.2875373Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-the-key-vault-is-recoverable
2025-10-10T13:07:34.2875830Z Check: CKV_AZURE_40: "Ensure that the expiration date is set on all keys"
2025-10-10T13:07:34.2876218Z 	PASSED for resource: module.shared_infra.module.encryption_key.azurerm_key_vault_key.kvauk
2025-10-10T13:07:34.2876900Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-key/main.tf:1-31
2025-10-10T13:07:34.2877426Z 	Calling File: /../modules/shared/key-vault.tf:6-21
2025-10-10T13:07:34.2878064Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/set-an-expiration-date-on-all-keys
2025-10-10T13:07:34.2878507Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2878925Z 	PASSED for resource: module.aps.module.postgres_admin_password_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2879603Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2880044Z 	Calling File: /../modules/aps/postgresql.tf:39-45
2025-10-10T13:07:34.2880686Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2881144Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2881542Z 	PASSED for resource: module.aps.module.postgres_admin_password_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2882287Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2882726Z 	Calling File: /../modules/aps/postgresql.tf:39-45
2025-10-10T13:07:34.2883377Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2883849Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2884257Z 	PASSED for resource: module.flowx.module.postgres_admin_password_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2884937Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2885363Z 	Calling File: /../modules/flowx/postgresql.tf:39-45
2025-10-10T13:07:34.2885994Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2886457Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2886861Z 	PASSED for resource: module.flowx.module.postgres_admin_password_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2887622Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2888052Z 	Calling File: /../modules/flowx/postgresql.tf:39-45
2025-10-10T13:07:34.2888568Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2888940Z Check: CKV_AZURE_41: "Ensure that the expiration date is set on all secrets"
2025-10-10T13:07:34.2889260Z 	PASSED for resource: module.shared_infra.module.management_vm_sshkey_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2889872Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2890332Z 	Calling File: /../modules/shared/management-vm.tf:27-35
2025-10-10T13:07:34.2890869Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-secrets-policies/set-an-expiration-date-on-all-secrets
2025-10-10T13:07:34.2891255Z Check: CKV_AZURE_114: "Ensure that key vault secrets have "content_type" set"
2025-10-10T13:07:34.2891605Z 	PASSED for resource: module.shared_infra.module.management_vm_sshkey_secret.azurerm_key_vault_secret.kvaus
2025-10-10T13:07:34.2892183Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-secret/main.tf:5-15
2025-10-10T13:07:34.2892633Z 	Calling File: /../modules/shared/management-vm.tf:27-35
2025-10-10T13:07:34.2893227Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-secrets-have-content-type-set
2025-10-10T13:07:34.2893661Z Check: CKV_AZURE_189: "Ensure that Azure Key Vault disables public network access"
2025-10-10T13:07:34.2894018Z 	PASSED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2894621Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2895140Z 	Calling File: /../modules/aps/key-vault.tf:1-22
2025-10-10T13:07:34.2895748Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-189
2025-10-10T13:07:34.2896183Z Check: CKV_AZURE_109: "Ensure that key vault allows firewall rules settings"
2025-10-10T13:07:34.2896570Z 	PASSED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2897196Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2897865Z 	Calling File: /../modules/aps/key-vault.tf:1-22
2025-10-10T13:07:34.2898498Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-key-vault-allows-firewall-rules-settings
2025-10-10T13:07:34.2898905Z Check: CKV_AZURE_111: "Ensure that key vault enables soft delete"
2025-10-10T13:07:34.2899218Z 	PASSED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2899730Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2900117Z 	Calling File: /../modules/aps/key-vault.tf:1-22
2025-10-10T13:07:34.2900665Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-soft-delete
2025-10-10T13:07:34.2901030Z Check: CKV_AZURE_42: "Ensure the key vault is recoverable"
2025-10-10T13:07:34.2901428Z 	PASSED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2901893Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2904216Z 	Calling File: /../modules/aps/key-vault.tf:1-22
2025-10-10T13:07:34.2904811Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-the-key-vault-is-recoverable
2025-10-10T13:07:34.2905208Z Check: CKV_AZURE_189: "Ensure that Azure Key Vault disables public network access"
2025-10-10T13:07:34.2905558Z 	PASSED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2906132Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2906614Z 	Calling File: /../modules/flowx/key-vault.tf:1-22
2025-10-10T13:07:34.2907171Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-189
2025-10-10T13:07:34.2907556Z Check: CKV_AZURE_109: "Ensure that key vault allows firewall rules settings"
2025-10-10T13:07:34.2907911Z 	PASSED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2908492Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2908973Z 	Calling File: /../modules/flowx/key-vault.tf:1-22
2025-10-10T13:07:34.2909558Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-key-vault-allows-firewall-rules-settings
2025-10-10T13:07:34.2909986Z Check: CKV_AZURE_111: "Ensure that key vault enables soft delete"
2025-10-10T13:07:34.2910335Z 	PASSED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2910916Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2911396Z 	Calling File: /../modules/flowx/key-vault.tf:1-22
2025-10-10T13:07:34.2911958Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-soft-delete
2025-10-10T13:07:34.2912361Z Check: CKV_AZURE_42: "Ensure the key vault is recoverable"
2025-10-10T13:07:34.2912696Z 	PASSED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.2913262Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.2913749Z 	Calling File: /../modules/flowx/key-vault.tf:1-22
2025-10-10T13:07:34.2914315Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-the-key-vault-is-recoverable
2025-10-10T13:07:34.2915033Z Check: CKV_AZURE_2: "Ensure Azure managed disk has encryption enabled"
2025-10-10T13:07:34.2915394Z 	PASSED for resource: module.shared_infra.module.management_vm_data_disk.azurerm_managed_disk.mdsk
2025-10-10T13:07:34.2916014Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk/v1.3.0/manageddisk/main.tf:1-19
2025-10-10T13:07:34.2916550Z 	Calling File: /../modules/shared/management-vm.tf:37-48
2025-10-10T13:07:34.2917089Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azr-general-1
2025-10-10T13:07:34.2917493Z Check: CKV_AZURE_77: "Ensure that UDP Services are restricted from the Internet "
2025-10-10T13:07:34.2917861Z 	PASSED for resource: module.shared_infra.module.network_security_group.azurerm_network_security_group.nesg
2025-10-10T13:07:34.2918453Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg/v1.3.0/main.tf:1-6
2025-10-10T13:07:34.2918861Z 	Calling File: /../modules/shared/network.tf:6-11
2025-10-10T13:07:34.2919505Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-udp-services-are-restricted-from-the-internet
2025-10-10T13:07:34.2920103Z Check: CKV_AZURE_160: "Ensure that HTTP (port 80) access is restricted from the internet"
2025-10-10T13:07:34.2920504Z 	PASSED for resource: module.shared_infra.module.network_security_group.azurerm_network_security_group.nesg
2025-10-10T13:07:34.2921533Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg/v1.3.0/main.tf:1-6
2025-10-10T13:07:34.2921932Z 	Calling File: /../modules/shared/network.tf:6-11
2025-10-10T13:07:34.2922989Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-azure-http-port-80-access-from-the-internet-is-restricted
2025-10-10T13:07:34.2923585Z Check: CKV_AZURE_10: "Ensure that SSH access is restricted from the internet"
2025-10-10T13:07:34.2923965Z 	PASSED for resource: module.shared_infra.module.network_security_group.azurerm_network_security_group.nesg
2025-10-10T13:07:34.2924495Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg/v1.3.0/main.tf:1-6
2025-10-10T13:07:34.2924857Z 	Calling File: /../modules/shared/network.tf:6-11
2025-10-10T13:07:34.2925349Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azr-networking-3
2025-10-10T13:07:34.2925706Z Check: CKV_AZURE_9: "Ensure that RDP access is restricted from the internet"
2025-10-10T13:07:34.2926056Z 	PASSED for resource: module.shared_infra.module.network_security_group.azurerm_network_security_group.nesg
2025-10-10T13:07:34.2926604Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg/v1.3.0/main.tf:1-6
2025-10-10T13:07:34.2926947Z 	Calling File: /../modules/shared/network.tf:6-11
2025-10-10T13:07:34.2927443Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azr-networking-2
2025-10-10T13:07:34.2927831Z Check: CKV_AZURE_179: "Ensure VM agent is installed"
2025-10-10T13:07:34.2929294Z 	PASSED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.2929945Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.2930436Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.2930981Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-179
2025-10-10T13:07:34.2931346Z Check: CKV_AZURE_92: "Ensure that Virtual Machines use managed disks"
2025-10-10T13:07:34.2964558Z 	PASSED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.2965764Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.2966244Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.2966791Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-virtual-machines-use-managed-disks
2025-10-10T13:07:34.2967177Z Check: CKV_AZURE_178: "Ensure linux VM enables SSH with keys for secure communication"
2025-10-10T13:07:34.2967560Z 	PASSED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.2968196Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.2968685Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.2969261Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-178
2025-10-10T13:07:34.2969676Z Check: CKV_AZURE_190: "Ensure that Storage blobs restrict public access"
2025-10-10T13:07:34.2970202Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2970864Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2971391Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.2971977Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-190
2025-10-10T13:07:34.2972444Z Check: CKV_AZURE_44: "Ensure Storage Account is using the latest version of TLS encryption"
2025-10-10T13:07:34.2972858Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2973524Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2974060Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.2974696Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-storage-policies/bc-azr-storage-2
2025-10-10T13:07:34.2975165Z Check: CKV_AZURE_35: "Ensure default network access rule for Storage Accounts is set to deny"
2025-10-10T13:07:34.2975597Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2976259Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2976774Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.2977499Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/set-default-network-access-rule-for-storage-accounts-to-deny
2025-10-10T13:07:34.2978089Z Check: CKV_AZURE_3: "Ensure that 'enable_https_traffic_only' is enabled"
2025-10-10T13:07:34.2978500Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2979162Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2979708Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.2980314Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-3
2025-10-10T13:07:34.2980916Z Check: CKV_AZURE_36: "Ensure 'Trusted Microsoft Services' is enabled for Storage Account access"
2025-10-10T13:07:34.2981342Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2981982Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2982702Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.2983402Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/enable-trusted-microsoft-services-for-storage-account-access
2025-10-10T13:07:34.2983865Z Check: CKV_AZURE_190: "Ensure that Storage blobs restrict public access"
2025-10-10T13:07:34.2984194Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2984719Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2985252Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.2985886Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-190
2025-10-10T13:07:34.2986368Z Check: CKV_AZURE_44: "Ensure Storage Account is using the latest version of TLS encryption"
2025-10-10T13:07:34.2986799Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2987444Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2988131Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.2988781Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-storage-policies/bc-azr-storage-2
2025-10-10T13:07:34.2989265Z Check: CKV_AZURE_35: "Ensure default network access rule for Storage Accounts is set to deny"
2025-10-10T13:07:34.2989713Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2990402Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2990972Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.2991711Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/set-default-network-access-rule-for-storage-accounts-to-deny
2025-10-10T13:07:34.2992365Z Check: CKV_AZURE_3: "Ensure that 'enable_https_traffic_only' is enabled"
2025-10-10T13:07:34.2992785Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2993475Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2994057Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.2994692Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-3
2025-10-10T13:07:34.2995819Z Check: CKV_AZURE_36: "Ensure 'Trusted Microsoft Services' is enabled for Storage Account access"
2025-10-10T13:07:34.2996219Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.2996871Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.2997465Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.2998216Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/enable-trusted-microsoft-services-for-storage-account-access
2025-10-10T13:07:34.2998759Z Check: CKV_AZURE_118: "Ensure that Network Interfaces disable IP forwarding"
2025-10-10T13:07:34.2999198Z 	PASSED for resource: module.shared_infra.module.management_vm.module.vnic.azurerm_network_interface.vnic
2025-10-10T13:07:34.2999913Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-vnic/v1.4.0/main.tf:1-13
2025-10-10T13:07:34.3000811Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:101-110
2025-10-10T13:07:34.3001639Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-network-interfaces-disable-ip-forwarding
2025-10-10T13:07:34.3002169Z Check: CKV_AZURE_39: "Ensure that no custom subscription owner roles are created"
2025-10-10T13:07:34.3002590Z 	PASSED for resource: module.devops.azurerm_role_definition.platform_start_stopper
2025-10-10T13:07:34.3003096Z 	File: /../modules/azure-devops/start-stop.tf:4-35
2025-10-10T13:07:34.3003530Z 	Calling File: /azure-devops.tf:1-14
2025-10-10T13:07:34.3004219Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/do-not-create-custom-subscription-owner-roles
2025-10-10T13:07:34.3004735Z Check: CKV2_AZURE_41: "Ensure storage account is configured with SAS expiration policy"
2025-10-10T13:07:34.3005164Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3005869Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3006670Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-41
2025-10-10T13:07:34.3007150Z Check: CKV2_AZURE_41: "Ensure storage account is configured with SAS expiration policy"
2025-10-10T13:07:34.3007578Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3008265Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3008991Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-41
2025-10-10T13:07:34.3009564Z Check: CKV_AZURE_119: "Ensure that Network Interfaces don't use public IPs"
2025-10-10T13:07:34.3010028Z 	PASSED for resource: module.shared_infra.module.management_vm.module.vnic.azurerm_network_interface.vnic
2025-10-10T13:07:34.3010710Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-vnic/v1.4.0/main.tf:1-13
2025-10-10T13:07:34.3011507Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-network-interfaces-dont-use-public-ips
2025-10-10T13:07:34.3012041Z Check: CKV2_AZURE_47: "Ensure storage account is configured without blob anonymous access"
2025-10-10T13:07:34.3012474Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3013176Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3013882Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-47
2025-10-10T13:07:34.3014372Z Check: CKV2_AZURE_47: "Ensure storage account is configured without blob anonymous access"
2025-10-10T13:07:34.3014805Z 	PASSED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3015494Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3016219Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-47
2025-10-10T13:07:34.3016706Z Check: CKV2_AZURE_8: "Ensure the storage container storing the activity logs is not publicly accessible"
2025-10-10T13:07:34.3017176Z 	PASSED for resource: module.aps.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3017899Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3018901Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/ensure-the-storage-container-storing-the-activity-logs-is-not-publicly-accessible
2025-10-10T13:07:34.3019433Z Check: CKV2_CUSTOM_ACR: "Container registries general security"
2025-10-10T13:07:34.3019846Z 	PASSED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3020532Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3020938Z Check: CKV2_CUSTOM_ACR: "Container registries general security"
2025-10-10T13:07:34.3021253Z 	PASSED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3021768Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3022132Z Check: CKV2_CUSTOM_ACR: "Container registries general security"
2025-10-10T13:07:34.3022513Z 	PASSED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3023125Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3023673Z Check: CKV2_CUSTOM_ACR: "Container registries general security"
2025-10-10T13:07:34.3024028Z 	PASSED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3024640Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3025077Z Check: CKV2_CUSTOM_LAW: "Log Analytics workspace general security"
2025-10-10T13:07:34.3025434Z 	PASSED for resource: module.shared_infra.module.log_analytics_workspace.azurerm_log_analytics_workspace.laws
2025-10-10T13:07:34.3026142Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-loganalytics/v4.0.0/la-workspace/main.tf:2-16
2025-10-10T13:07:34.3026595Z Check: CKV2_CUSTOM_KV: "Key vaults shoud have deletion protection enabled"
2025-10-10T13:07:34.3027009Z 	PASSED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3027692Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3028165Z Check: CKV2_AZURE_39: "Ensure Azure VM is not configured with public IP and serial console access"
2025-10-10T13:07:34.3028635Z 	PASSED for resource: module.shared_infra.module.management_vm.module.vnic.azurerm_network_interface.vnic
2025-10-10T13:07:34.3029297Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-vnic/v1.4.0/main.tf:1-13
2025-10-10T13:07:34.3030001Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azure-2-39
2025-10-10T13:07:34.3030474Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3030873Z 	PASSED for resource: module.aps.module.aks_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3031502Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3032193Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3032655Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3033057Z 	PASSED for resource: module.aps.module.common_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3033709Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3034375Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3035725Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3036161Z 	PASSED for resource: module.aps.module.postgres_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3036824Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3037503Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3037954Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3038352Z 	PASSED for resource: module.flowx.module.aks_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3038982Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3039647Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3040117Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3040516Z 	PASSED for resource: module.flowx.module.common_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3041285Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3041985Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3042443Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3042851Z 	PASSED for resource: module.flowx.module.postgres_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3043486Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3044187Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3044639Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3045067Z 	PASSED for resource: module.shared_infra.module.appgw_pe_subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3045728Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3046395Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3046872Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3047279Z 	PASSED for resource: module.shared_infra.module.appgwsubnet.azurerm_subnet.snet
2025-10-10T13:07:34.3047938Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3048628Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3049063Z Check: CKV2_AZURE_31: "Ensure VNET subnet is configured with a Network Security Group (NSG)"
2025-10-10T13:07:34.3049459Z 	PASSED for resource: module.shared_infra.module.subnet.azurerm_subnet.snet
2025-10-10T13:07:34.3050081Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet/v1.4.1/main.tf:6-27
2025-10-10T13:07:34.3050780Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-31
2025-10-10T13:07:34.3051213Z Check: CKV_AZURE_167: "Ensure a retention policy is set to cleanup untagged manifests."
2025-10-10T13:07:34.3051638Z 	FAILED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3052300Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3053138Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3053854Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-167
2025-10-10T13:07:34.3054067Z 
2025-10-10T13:07:34.3054373Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3054664Z 		3  |   // Checkov
2025-10-10T13:07:34.3055194Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3055863Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3056349Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3056747Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3057087Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3057445Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3057861Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3058158Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3058470Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3058817Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3059179Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3059492Z 		15 | 
2025-10-10T13:07:34.3059757Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3060052Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3060373Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3060665Z 		19 | 
2025-10-10T13:07:34.3060935Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3061235Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3061526Z 		22 |     content {
2025-10-10T13:07:34.3061808Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3062124Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3062426Z 		25 |     }
2025-10-10T13:07:34.3062667Z 		26 |   }
2025-10-10T13:07:34.3062913Z 		27 | 
2025-10-10T13:07:34.3063183Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3063448Z 		29 | 
2025-10-10T13:07:34.3063717Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3064011Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3064297Z 		32 |     content {
2025-10-10T13:07:34.3064605Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3065042Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3065347Z 		35 |     }
2025-10-10T13:07:34.3065586Z 		36 |   }
2025-10-10T13:07:34.3065834Z 		37 | 
2025-10-10T13:07:34.3066067Z 		38 | }
2025-10-10T13:07:34.3066168Z 
2025-10-10T13:07:34.3066496Z Check: CKV_AZURE_167: "Ensure a retention policy is set to cleanup untagged manifests."
2025-10-10T13:07:34.3066921Z 	FAILED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3067568Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3068290Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3069012Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-167
2025-10-10T13:07:34.3069232Z 
2025-10-10T13:07:34.3069521Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3069965Z 		3  |   // Checkov
2025-10-10T13:07:34.3070496Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3071168Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3071670Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3072054Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3072390Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3072743Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3073063Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3073369Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3073651Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3074001Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3074361Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3074665Z 		15 | 
2025-10-10T13:07:34.3075143Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3075553Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3075861Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3076158Z 		19 | 
2025-10-10T13:07:34.3076419Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3076727Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3077011Z 		22 |     content {
2025-10-10T13:07:34.3077305Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3077617Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3077894Z 		25 |     }
2025-10-10T13:07:34.3078128Z 		26 |   }
2025-10-10T13:07:34.3078362Z 		27 | 
2025-10-10T13:07:34.3078633Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3078909Z 		29 | 
2025-10-10T13:07:34.3079181Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3080195Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3080486Z 		32 |     content {
2025-10-10T13:07:34.3080803Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3081157Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3081458Z 		35 |     }
2025-10-10T13:07:34.3081705Z 		36 |   }
2025-10-10T13:07:34.3081951Z 		37 | 
2025-10-10T13:07:34.3082188Z 		38 | }
2025-10-10T13:07:34.3082287Z 
2025-10-10T13:07:34.3082603Z Check: CKV_AZURE_167: "Ensure a retention policy is set to cleanup untagged manifests."
2025-10-10T13:07:34.3083028Z 	FAILED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3083714Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3084129Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.3084796Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-167
2025-10-10T13:07:34.3085029Z 
2025-10-10T13:07:34.3085316Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3085611Z 		3  |   // Checkov
2025-10-10T13:07:34.3086141Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3086827Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3087335Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3087738Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3088089Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3088588Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3088943Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3089237Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3089546Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3089876Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3090241Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3090562Z 		15 | 
2025-10-10T13:07:34.3090836Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3091144Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3091452Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3091747Z 		19 | 
2025-10-10T13:07:34.3092013Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3092314Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3092597Z 		22 |     content {
2025-10-10T13:07:34.3092898Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3093216Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3093504Z 		25 |     }
2025-10-10T13:07:34.3093750Z 		26 |   }
2025-10-10T13:07:34.3094076Z 		27 | 
2025-10-10T13:07:34.3094356Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3094617Z 		29 | 
2025-10-10T13:07:34.3094876Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3095192Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3095476Z 		32 |     content {
2025-10-10T13:07:34.3095778Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3096137Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3096431Z 		35 |     }
2025-10-10T13:07:34.3096658Z 		36 |   }
2025-10-10T13:07:34.3096899Z 		37 | 
2025-10-10T13:07:34.3097130Z 		38 | }
2025-10-10T13:07:34.3097230Z 
2025-10-10T13:07:34.3097552Z Check: CKV_AZURE_166: "Ensure container image quarantine, scan, and mark images verified"
2025-10-10T13:07:34.3097957Z 	FAILED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3098613Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3099024Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.3099619Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-166
2025-10-10T13:07:34.3099838Z 
2025-10-10T13:07:34.3100138Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3100425Z 		3  |   // Checkov
2025-10-10T13:07:34.3100930Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3101584Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3102066Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3102463Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3102790Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3103136Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3103474Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3103772Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3104065Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3104411Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3104774Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3105086Z 		15 | 
2025-10-10T13:07:34.3105353Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3105740Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3106058Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3106341Z 		19 | 
2025-10-10T13:07:34.3106594Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3106913Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3107196Z 		22 |     content {
2025-10-10T13:07:34.3107488Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3107796Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3108043Z 		25 |     }
2025-10-10T13:07:34.3108288Z 		26 |   }
2025-10-10T13:07:34.3108522Z 		27 | 
2025-10-10T13:07:34.3108790Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3109060Z 		29 | 
2025-10-10T13:07:34.3109318Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3109616Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3109915Z 		32 |     content {
2025-10-10T13:07:34.3110211Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3110567Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3110874Z 		35 |     }
2025-10-10T13:07:34.3111105Z 		36 |   }
2025-10-10T13:07:34.3111349Z 		37 | 
2025-10-10T13:07:34.3111660Z 		38 | }
2025-10-10T13:07:34.3111749Z 
2025-10-10T13:07:34.3112087Z Check: CKV_AZURE_167: "Ensure a retention policy is set to cleanup untagged manifests."
2025-10-10T13:07:34.3112503Z 	FAILED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3113166Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3113583Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.3114185Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-167
2025-10-10T13:07:34.3114417Z 
2025-10-10T13:07:34.3114692Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3115171Z 		3  |   // Checkov
2025-10-10T13:07:34.3115706Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3116363Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3116843Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3117243Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3117572Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3117927Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3118252Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3118557Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3118851Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3119200Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3119559Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3119857Z 		15 | 
2025-10-10T13:07:34.3120134Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3120467Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3120776Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3121059Z 		19 | 
2025-10-10T13:07:34.3121315Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3121602Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3121892Z 		22 |     content {
2025-10-10T13:07:34.3122161Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3122471Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3122760Z 		25 |     }
2025-10-10T13:07:34.3122994Z 		26 |   }
2025-10-10T13:07:34.3123224Z 		27 | 
2025-10-10T13:07:34.3123622Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3123885Z 		29 | 
2025-10-10T13:07:34.3124144Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3124445Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3124739Z 		32 |     content {
2025-10-10T13:07:34.3125042Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3125401Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3125694Z 		35 |     }
2025-10-10T13:07:34.3125916Z 		36 |   }
2025-10-10T13:07:34.3126102Z 		37 | 
2025-10-10T13:07:34.3126323Z 		38 | }
2025-10-10T13:07:34.3126424Z 
2025-10-10T13:07:34.3126729Z Check: CKV_AZURE_166: "Ensure container image quarantine, scan, and mark images verified"
2025-10-10T13:07:34.3127139Z 	FAILED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3127779Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3128194Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.3128977Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-166
2025-10-10T13:07:34.3129295Z 
2025-10-10T13:07:34.3129574Z 		2  | resource "azurerm_container_registry" "acre" {
2025-10-10T13:07:34.3129855Z 		3  |   // Checkov
2025-10-10T13:07:34.3130378Z 		4  |   #checkov:skip=CKV_AZURE_165:The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3131017Z 		5  |   #checkov:skip=CKV_AZURE_164:The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3131492Z 		6  |   #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3131886Z 		7  |   count                         = var.acr_enable ? 1 : 0
2025-10-10T13:07:34.3132218Z 		8  |   name                          = "${local.resource_shortname}${local.nc_no_hyphen}"
2025-10-10T13:07:34.3132568Z 		9  |   resource_group_name           = var.resource_group_name
2025-10-10T13:07:34.3132888Z 		10 |   location                      = var.conventions.region
2025-10-10T13:07:34.3133191Z 		11 |   sku                           = "Premium"
2025-10-10T13:07:34.3133480Z 		12 |   public_network_access_enabled = false
2025-10-10T13:07:34.3133808Z 		13 |   zone_redundancy_enabled       = var.zone_redundancy_enabled
2025-10-10T13:07:34.3134171Z 		14 |   retention_policy_in_days      = var.retention_policy_in_days
2025-10-10T13:07:34.3134464Z 		15 | 
2025-10-10T13:07:34.3134747Z 		16 |   export_policy_enabled     = false
2025-10-10T13:07:34.3135034Z 		17 |   tags                      = local.tags
2025-10-10T13:07:34.3135327Z 		18 |   quarantine_policy_enabled = var.enable_quarantine
2025-10-10T13:07:34.3135617Z 		19 | 
2025-10-10T13:07:34.3135866Z 		20 |   dynamic "identity" {
2025-10-10T13:07:34.3136152Z 		21 |     for_each = var.identity == null ? [] : [1]
2025-10-10T13:07:34.3136447Z 		22 |     content {
2025-10-10T13:07:34.3136719Z 		23 |       type         = var.identity.type
2025-10-10T13:07:34.3137016Z 		24 |       identity_ids = var.identity.identity_ids
2025-10-10T13:07:34.3137307Z 		25 |     }
2025-10-10T13:07:34.3137546Z 		26 |   }
2025-10-10T13:07:34.3137793Z 		27 | 
2025-10-10T13:07:34.3138055Z 		28 |   //dynamic "georeplications"
2025-10-10T13:07:34.3138309Z 		29 | 
2025-10-10T13:07:34.3138571Z 		30 |   dynamic "encryption" {
2025-10-10T13:07:34.3138869Z 		31 |     for_each = var.encryption == null ? [] : [1]
2025-10-10T13:07:34.3139155Z 		32 |     content {
2025-10-10T13:07:34.3139480Z 		33 |       key_vault_key_id   = var.encryption.key_vault_key_id
2025-10-10T13:07:34.3139841Z 		34 |       identity_client_id = var.encryption.identity_client_id
2025-10-10T13:07:34.3140137Z 		35 |     }
2025-10-10T13:07:34.3140389Z 		36 |   }
2025-10-10T13:07:34.3140616Z 		37 | 
2025-10-10T13:07:34.3140860Z 		38 | }
2025-10-10T13:07:34.3140951Z 
2025-10-10T13:07:34.3141379Z Check: CKV2_CUSTOM_AKS_SYS_NODEPOOL: "Ensure only specific AKS VM families are allowed"
2025-10-10T13:07:34.3141796Z 	FAILED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3142451Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3142893Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3143249Z 	Guide: Follow the link to get more info https://confluence.otpbank.hu/x/7VNmVw
2025-10-10T13:07:34.3143412Z 
2025-10-10T13:07:34.3143751Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3144175Z Check: CKV2_CUSTOM_AKS_SYS_NODEPOOL: "Ensure only specific AKS VM families are allowed"
2025-10-10T13:07:34.3144584Z 	FAILED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3145231Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3145649Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3146004Z 	Guide: Follow the link to get more info https://confluence.otpbank.hu/x/7VNmVw
2025-10-10T13:07:34.3146233Z 
2025-10-10T13:07:34.3146538Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3146960Z Check: CKV2_CUSTOM_AKS_CLUSTER_NODE_POOL: "Ensure only specific AKS Node Pool VM families are allowed"
2025-10-10T13:07:34.3147413Z 	FAILED for resource: module.aps.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3148088Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3148511Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3148884Z 	Guide: Follow the link to get more info https://confluence.otpbank.hu/x/7VNmVw
2025-10-10T13:07:34.3149045Z 
2025-10-10T13:07:34.3149379Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3149804Z Check: CKV2_CUSTOM_AKS_CLUSTER_NODE_POOL: "Ensure only specific AKS Node Pool VM families are allowed"
2025-10-10T13:07:34.3150251Z 	FAILED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3150927Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3151356Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3151724Z 	Guide: Follow the link to get more info https://confluence.otpbank.hu/x/7VNmVw
2025-10-10T13:07:34.3151893Z 
2025-10-10T13:07:34.3152227Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3152864Z Check: CKV_AZURE_135: "Ensure Application Gateway WAF prevents message lookup in Log4j2. See CVE-2021-44228 aka log4jshell"
2025-10-10T13:07:34.3153343Z 	FAILED for resource: module.shared_infra.module.appgw_wafp.azurerm_web_application_firewall_policy.wafp
2025-10-10T13:07:34.3154028Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-waf/v1.0.0/main.tf:2-137
2025-10-10T13:07:34.3154458Z 	Calling File: /../modules/shared/appgw.tf:153-183
2025-10-10T13:07:34.3155385Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-application-gateway-waf-prevents-message-lookup-in-log4j2
2025-10-10T13:07:34.3155659Z 
2025-10-10T13:07:34.3156005Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3156397Z Check: CKV_AZURE_110: "Ensure that key vault enables purge protection"
2025-10-10T13:07:34.3156720Z 	FAILED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3157354Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3157991Z 	Calling File: /../modules/aps/key-vault.tf:1-22
2025-10-10T13:07:34.3158667Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-purge-protection
2025-10-10T13:07:34.3158922Z 
2025-10-10T13:07:34.3159193Z 		1  | resource "azurerm_key_vault" "kvau" {
2025-10-10T13:07:34.3159474Z 		2  |   lifecycle {
2025-10-10T13:07:34.3159731Z 		3  |     ignore_changes = [
2025-10-10T13:07:34.3160006Z 		4  |       contact
2025-10-10T13:07:34.3160237Z 		5  |     ]
2025-10-10T13:07:34.3160466Z 		6  |   }
2025-10-10T13:07:34.3160713Z 		7  | 
2025-10-10T13:07:34.3160953Z 		8  |   //Checkov
2025-10-10T13:07:34.3161263Z 		9  |   #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
2025-10-10T13:07:34.3161696Z 		10 |   #checkov:skip=CKV2_AZURE_32: We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3162034Z 		11 | 
2025-10-10T13:07:34.3162276Z 		12 |   # General
2025-10-10T13:07:34.3162554Z 		13 |   name                = local.kvauname
2025-10-10T13:07:34.3162859Z 		14 |   resource_group_name = var.resource_group_name
2025-10-10T13:07:34.3163279Z 		15 |   location            = var.conventions.region
2025-10-10T13:07:34.3163619Z 		16 |   tenant_id           = data.azurerm_client_config.current.tenant_id
2025-10-10T13:07:34.3163946Z 		17 |   sku_name            = var.sku_name
2025-10-10T13:07:34.3164240Z 		18 |   tags                = local.tags
2025-10-10T13:07:34.3164487Z 		19 | 
2025-10-10T13:07:34.3164732Z 		20 |   # Config
2025-10-10T13:07:34.3165060Z 		21 |   //Turn on data protection to vault
2025-10-10T13:07:34.3165575Z 		22 |   //Soft-delete option must be enabled on Azure Key Vault. Soft delete retention time is 90 days 
2025-10-10T13:07:34.3165985Z 		23 |   soft_delete_retention_days = var.soft_delete_retention_days
2025-10-10T13:07:34.3166327Z 		24 |   //Role Based Access Control must be enabled
2025-10-10T13:07:34.3166668Z 		25 |   enable_rbac_authorization = var.rbac_authorization_enabled
2025-10-10T13:07:34.3167153Z 		26 |   //Purge protection must be enabled only in pre-prod and prod
2025-10-10T13:07:34.3167606Z 		27 |   purge_protection_enabled = lower(var.conventions.environment) == "ppr" || lower(var.conventions.environment) == "prd" ? true : var.purge_protection_enabled
2025-10-10T13:07:34.3168022Z 		28 |   //Disk Encryption
2025-10-10T13:07:34.3168336Z 		29 |   enabled_for_disk_encryption = var.disk_encryption_enabled
2025-10-10T13:07:34.3168659Z 		30 |   //Deployments
2025-10-10T13:07:34.3168969Z 		31 |   enabled_for_deployment          = var.deployment_enabled
2025-10-10T13:07:34.3169324Z 		32 |   enabled_for_template_deployment = var.template_deployment_enabled
2025-10-10T13:07:34.3169634Z 		33 | 
2025-10-10T13:07:34.3169885Z 		34 |   # Access
2025-10-10T13:07:34.3170143Z 		35 |   //Public Endpoint
2025-10-10T13:07:34.3170472Z 		36 |   public_network_access_enabled = var.public_network_access_enabled
2025-10-10T13:07:34.3170784Z 		37 |   // ACLs
2025-10-10T13:07:34.3171035Z 		38 |   network_acls {
2025-10-10T13:07:34.3171323Z 		39 |     bypass         = "AzureServices"
2025-10-10T13:07:34.3171605Z 		40 |     default_action = "Deny"
2025-10-10T13:07:34.3171916Z 		41 |     ip_rules       = var.conventions.firewall_whitelist
2025-10-10T13:07:34.3172217Z 		42 |   }
2025-10-10T13:07:34.3172450Z 		43 | 
2025-10-10T13:07:34.3172680Z 		44 | }
2025-10-10T13:07:34.3172779Z 
2025-10-10T13:07:34.3173067Z Check: CKV_AZURE_110: "Ensure that key vault enables purge protection"
2025-10-10T13:07:34.3173456Z 	FAILED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3174121Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3174648Z 	Calling File: /../modules/flowx/key-vault.tf:1-22
2025-10-10T13:07:34.3175321Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-enables-purge-protection
2025-10-10T13:07:34.3175672Z 
2025-10-10T13:07:34.3175945Z 		1  | resource "azurerm_key_vault" "kvau" {
2025-10-10T13:07:34.3176238Z 		2  |   lifecycle {
2025-10-10T13:07:34.3176498Z 		3  |     ignore_changes = [
2025-10-10T13:07:34.3176756Z 		4  |       contact
2025-10-10T13:07:34.3176996Z 		5  |     ]
2025-10-10T13:07:34.3177238Z 		6  |   }
2025-10-10T13:07:34.3177475Z 		7  | 
2025-10-10T13:07:34.3177725Z 		8  |   //Checkov
2025-10-10T13:07:34.3178042Z 		9  |   #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
2025-10-10T13:07:34.3178466Z 		10 |   #checkov:skip=CKV2_AZURE_32: We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3178815Z 		11 | 
2025-10-10T13:07:34.3179055Z 		12 |   # General
2025-10-10T13:07:34.3179333Z 		13 |   name                = local.kvauname
2025-10-10T13:07:34.3179637Z 		14 |   resource_group_name = var.resource_group_name
2025-10-10T13:07:34.3179960Z 		15 |   location            = var.conventions.region
2025-10-10T13:07:34.3180300Z 		16 |   tenant_id           = data.azurerm_client_config.current.tenant_id
2025-10-10T13:07:34.3180689Z 		17 |   sku_name            = var.sku_name
2025-10-10T13:07:34.3180982Z 		18 |   tags                = local.tags
2025-10-10T13:07:34.3181238Z 		19 | 
2025-10-10T13:07:34.3181478Z 		20 |   # Config
2025-10-10T13:07:34.3181752Z 		21 |   //Turn on data protection to vault
2025-10-10T13:07:34.3182290Z 		22 |   //Soft-delete option must be enabled on Azure Key Vault. Soft delete retention time is 90 days 
2025-10-10T13:07:34.3182685Z 		23 |   soft_delete_retention_days = var.soft_delete_retention_days
2025-10-10T13:07:34.3183032Z 		24 |   //Role Based Access Control must be enabled
2025-10-10T13:07:34.3183365Z 		25 |   enable_rbac_authorization = var.rbac_authorization_enabled
2025-10-10T13:07:34.3183811Z 		26 |   //Purge protection must be enabled only in pre-prod and prod
2025-10-10T13:07:34.3184277Z 		27 |   purge_protection_enabled = lower(var.conventions.environment) == "ppr" || lower(var.conventions.environment) == "prd" ? true : var.purge_protection_enabled
2025-10-10T13:07:34.3184679Z 		28 |   //Disk Encryption
2025-10-10T13:07:34.3185007Z 		29 |   enabled_for_disk_encryption = var.disk_encryption_enabled
2025-10-10T13:07:34.3185315Z 		30 |   //Deployments
2025-10-10T13:07:34.3185625Z 		31 |   enabled_for_deployment          = var.deployment_enabled
2025-10-10T13:07:34.3185988Z 		32 |   enabled_for_template_deployment = var.template_deployment_enabled
2025-10-10T13:07:34.3186307Z 		33 | 
2025-10-10T13:07:34.3186565Z 		34 |   # Access
2025-10-10T13:07:34.3186836Z 		35 |   //Public Endpoint
2025-10-10T13:07:34.3187158Z 		36 |   public_network_access_enabled = var.public_network_access_enabled
2025-10-10T13:07:34.3187435Z 		37 |   // ACLs
2025-10-10T13:07:34.3187689Z 		38 |   network_acls {
2025-10-10T13:07:34.3187951Z 		39 |     bypass         = "AzureServices"
2025-10-10T13:07:34.3188235Z 		40 |     default_action = "Deny"
2025-10-10T13:07:34.3188609Z 		41 |     ip_rules       = var.conventions.firewall_whitelist
2025-10-10T13:07:34.3188914Z 		42 |   }
2025-10-10T13:07:34.3189234Z 		43 | 
2025-10-10T13:07:34.3189478Z 		44 | }
2025-10-10T13:07:34.3189580Z 
2025-10-10T13:07:34.3190087Z Check: CKV_AZURE_136: "Ensure that PostgreSQL Flexible server enables geo-redundant backups"
2025-10-10T13:07:34.3190552Z 	FAILED for resource: module.aps.module.postgresql_server.azurerm_postgresql_flexible_server.fpss
2025-10-10T13:07:34.3191264Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-postgresql/v2.2.0/flexibleserver/main.tf:13-88
2025-10-10T13:07:34.3191705Z 	Calling File: /../modules/aps/postgresql.tf:47-93
2025-10-10T13:07:34.3192428Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-azure-postgresql-flexible-server-enables-geo-redundant-backups
2025-10-10T13:07:34.3192812Z 
2025-10-10T13:07:34.3193140Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3193702Z Check: CKV_AZURE_136: "Ensure that PostgreSQL Flexible server enables geo-redundant backups"
2025-10-10T13:07:34.3194138Z 	FAILED for resource: module.flowx.module.postgresql_server.azurerm_postgresql_flexible_server.fpss
2025-10-10T13:07:34.3675450Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-postgresql/v2.2.0/flexibleserver/main.tf:13-88
2025-10-10T13:07:34.3676422Z 	Calling File: /../modules/flowx/postgresql.tf:47-93
2025-10-10T13:07:34.3677478Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-azure-postgresql-flexible-server-enables-geo-redundant-backups
2025-10-10T13:07:34.3677831Z 
2025-10-10T13:07:34.3678237Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3678754Z Check: CKV2_CUSTOM_AZURE_VM: "Ensure only specific VM families are allowed"
2025-10-10T13:07:34.3679269Z 	FAILED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.3680526Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.3681204Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.3681675Z 	Guide: Follow the link to get more info https://confluence.otpbank.hu/x/7VNmVw
2025-10-10T13:07:34.3681891Z 
2025-10-10T13:07:34.3682295Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3682836Z Check: CKV_AZURE_33: "Ensure Storage logging is enabled for Queue service for read, write and delete requests"
2025-10-10T13:07:34.3683375Z 	FAILED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3684222Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3684892Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.3685771Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/enable-requests-on-storage-logging-for-queue-service
2025-10-10T13:07:34.3686107Z 
2025-10-10T13:07:34.3686510Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3687058Z Check: CKV_AZURE_33: "Ensure Storage logging is enabled for Queue service for read, write and delete requests"
2025-10-10T13:07:34.3687601Z 	FAILED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3688445Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3689127Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.3689990Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/enable-requests-on-storage-logging-for-queue-service
2025-10-10T13:07:34.3690315Z 
2025-10-10T13:07:34.3690716Z 		Code lines for this resource are too many. Please use IDE of your choice to review the file.
2025-10-10T13:07:34.3691260Z Check: CKV2_AZURE_8: "Ensure the storage container storing the activity logs is not publicly accessible"
2025-10-10T13:07:34.3691795Z 	FAILED for resource: module.flowx.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3692510Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3693358Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/ensure-the-storage-container-storing-the-activity-logs-is-not-publicly-accessible
2025-10-10T13:07:34.3693776Z 
2025-10-10T13:07:34.3694059Z 		4  | resource "azurerm_storage_container" "container" {
2025-10-10T13:07:34.3694357Z 		5  |   //Checkov
2025-10-10T13:07:34.3694683Z 		6  |   #checkov:skip=CKV_AZURE_34: Public access is desabled by default
2025-10-10T13:07:34.3695067Z 		7  |   #checkov:skip=CKV2_AZURE_21: Logging is ensured by policy
2025-10-10T13:07:34.3695432Z 		8  |   for_each = var.stac_containers != null ? var.stac_containers : {}
2025-10-10T13:07:34.3695727Z 		9  | 
2025-10-10T13:07:34.3696124Z 		10 |   depends_on = [module.privateendpoint01, time_sleep.wait-rbac]
2025-10-10T13:07:34.3696431Z 		11 | 
2025-10-10T13:07:34.3696692Z 		12 |   name                  = each.value.name
2025-10-10T13:07:34.3697019Z 		13 |   storage_account_name  = azurerm_storage_account.stac.name
2025-10-10T13:07:34.3697384Z 		14 |   container_access_type = each.value.public_access_level
2025-10-10T13:07:34.3697778Z 		15 |   metadata              = each.value.metadata != null ? { for k, v in each.value.metadata : lower(k) => v } : null
2025-10-10T13:07:34.3698106Z 		16 |   
2025-10-10T13:07:34.3698427Z 		17 |   default_encryption_scope          = each.value.encryption_scope_default
2025-10-10T13:07:34.3698816Z 		18 |   encryption_scope_override_enabled = each.value.encryption_scope_override_deny
2025-10-10T13:07:34.3699231Z 		19 | }
2025-10-10T13:07:34.3699324Z 
2025-10-10T13:07:34.3699628Z Check: CKV2_CUSTOM_KV: "Key vaults shoud have deletion protection enabled"
2025-10-10T13:07:34.3700023Z 	FAILED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3700681Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3700930Z 
2025-10-10T13:07:34.3701204Z 		1  | resource "azurerm_key_vault" "kvau" {
2025-10-10T13:07:34.3701500Z 		2  |   lifecycle {
2025-10-10T13:07:34.3701764Z 		3  |     ignore_changes = [
2025-10-10T13:07:34.3702009Z 		4  |       contact
2025-10-10T13:07:34.3702196Z 		5  |     ]
2025-10-10T13:07:34.3702405Z 		6  |   }
2025-10-10T13:07:34.3702618Z 		7  | 
2025-10-10T13:07:34.3702860Z 		8  |   //Checkov
2025-10-10T13:07:34.3703171Z 		9  |   #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
2025-10-10T13:07:34.3703583Z 		10 |   #checkov:skip=CKV2_AZURE_32: We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3703924Z 		11 | 
2025-10-10T13:07:34.3704166Z 		12 |   # General
2025-10-10T13:07:34.3704436Z 		13 |   name                = local.kvauname
2025-10-10T13:07:34.3704753Z 		14 |   resource_group_name = var.resource_group_name
2025-10-10T13:07:34.3705064Z 		15 |   location            = var.conventions.region
2025-10-10T13:07:34.3705409Z 		16 |   tenant_id           = data.azurerm_client_config.current.tenant_id
2025-10-10T13:07:34.3705723Z 		17 |   sku_name            = var.sku_name
2025-10-10T13:07:34.3705998Z 		18 |   tags                = local.tags
2025-10-10T13:07:34.3706262Z 		19 | 
2025-10-10T13:07:34.3706503Z 		20 |   # Config
2025-10-10T13:07:34.3706779Z 		21 |   //Turn on data protection to vault
2025-10-10T13:07:34.3707275Z 		22 |   //Soft-delete option must be enabled on Azure Key Vault. Soft delete retention time is 90 days 
2025-10-10T13:07:34.3707669Z 		23 |   soft_delete_retention_days = var.soft_delete_retention_days
2025-10-10T13:07:34.3708017Z 		24 |   //Role Based Access Control must be enabled
2025-10-10T13:07:34.3708347Z 		25 |   enable_rbac_authorization = var.rbac_authorization_enabled
2025-10-10T13:07:34.3708820Z 		26 |   //Purge protection must be enabled only in pre-prod and prod
2025-10-10T13:07:34.3709277Z 		27 |   purge_protection_enabled = lower(var.conventions.environment) == "ppr" || lower(var.conventions.environment) == "prd" ? true : var.purge_protection_enabled
2025-10-10T13:07:34.3709683Z 		28 |   //Disk Encryption
2025-10-10T13:07:34.3710004Z 		29 |   enabled_for_disk_encryption = var.disk_encryption_enabled
2025-10-10T13:07:34.3710411Z 		30 |   //Deployments
2025-10-10T13:07:34.3710711Z 		31 |   enabled_for_deployment          = var.deployment_enabled
2025-10-10T13:07:34.3711079Z 		32 |   enabled_for_template_deployment = var.template_deployment_enabled
2025-10-10T13:07:34.3711383Z 		33 | 
2025-10-10T13:07:34.3711625Z 		34 |   # Access
2025-10-10T13:07:34.3711888Z 		35 |   //Public Endpoint
2025-10-10T13:07:34.3712209Z 		36 |   public_network_access_enabled = var.public_network_access_enabled
2025-10-10T13:07:34.3712535Z 		37 |   // ACLs
2025-10-10T13:07:34.3712791Z 		38 |   network_acls {
2025-10-10T13:07:34.3713058Z 		39 |     bypass         = "AzureServices"
2025-10-10T13:07:34.3713340Z 		40 |     default_action = "Deny"
2025-10-10T13:07:34.3713645Z 		41 |     ip_rules       = var.conventions.firewall_whitelist
2025-10-10T13:07:34.3713924Z 		42 |   }
2025-10-10T13:07:34.3714175Z 		43 | 
2025-10-10T13:07:34.3714409Z 		44 | }
2025-10-10T13:07:34.3714506Z 
2025-10-10T13:07:34.3714808Z Check: CKV2_CUSTOM_KV: "Key vaults shoud have deletion protection enabled"
2025-10-10T13:07:34.3715588Z 	FAILED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3716286Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3716840Z 
2025-10-10T13:07:34.3717117Z 		1  | resource "azurerm_key_vault" "kvau" {
2025-10-10T13:07:34.3717388Z 		2  |   lifecycle {
2025-10-10T13:07:34.3717650Z 		3  |     ignore_changes = [
2025-10-10T13:07:34.3717915Z 		4  |       contact
2025-10-10T13:07:34.3718152Z 		5  |     ]
2025-10-10T13:07:34.3718382Z 		6  |   }
2025-10-10T13:07:34.3718620Z 		7  | 
2025-10-10T13:07:34.3718853Z 		8  |   //Checkov
2025-10-10T13:07:34.3719178Z 		9  |   #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
2025-10-10T13:07:34.3719601Z 		10 |   #checkov:skip=CKV2_AZURE_32: We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3719938Z 		11 | 
2025-10-10T13:07:34.3720196Z 		12 |   # General
2025-10-10T13:07:34.3720462Z 		13 |   name                = local.kvauname
2025-10-10T13:07:34.3720773Z 		14 |   resource_group_name = var.resource_group_name
2025-10-10T13:07:34.3721104Z 		15 |   location            = var.conventions.region
2025-10-10T13:07:34.3721449Z 		16 |   tenant_id           = data.azurerm_client_config.current.tenant_id
2025-10-10T13:07:34.3721781Z 		17 |   sku_name            = var.sku_name
2025-10-10T13:07:34.3722065Z 		18 |   tags                = local.tags
2025-10-10T13:07:34.3722315Z 		19 | 
2025-10-10T13:07:34.3722567Z 		20 |   # Config
2025-10-10T13:07:34.3722842Z 		21 |   //Turn on data protection to vault
2025-10-10T13:07:34.3723389Z 		22 |   //Soft-delete option must be enabled on Azure Key Vault. Soft delete retention time is 90 days 
2025-10-10T13:07:34.3723789Z 		23 |   soft_delete_retention_days = var.soft_delete_retention_days
2025-10-10T13:07:34.3724135Z 		24 |   //Role Based Access Control must be enabled
2025-10-10T13:07:34.3724479Z 		25 |   enable_rbac_authorization = var.rbac_authorization_enabled
2025-10-10T13:07:34.3724960Z 		26 |   //Purge protection must be enabled only in pre-prod and prod
2025-10-10T13:07:34.3725415Z 		27 |   purge_protection_enabled = lower(var.conventions.environment) == "ppr" || lower(var.conventions.environment) == "prd" ? true : var.purge_protection_enabled
2025-10-10T13:07:34.3725834Z 		28 |   //Disk Encryption
2025-10-10T13:07:34.3726145Z 		29 |   enabled_for_disk_encryption = var.disk_encryption_enabled
2025-10-10T13:07:34.3726468Z 		30 |   //Deployments
2025-10-10T13:07:34.3726771Z 		31 |   enabled_for_deployment          = var.deployment_enabled
2025-10-10T13:07:34.3727134Z 		32 |   enabled_for_template_deployment = var.template_deployment_enabled
2025-10-10T13:07:34.3727434Z 		33 | 
2025-10-10T13:07:34.3727675Z 		34 |   # Access
2025-10-10T13:07:34.3727930Z 		35 |   //Public Endpoint
2025-10-10T13:07:34.3728271Z 		36 |   public_network_access_enabled = var.public_network_access_enabled
2025-10-10T13:07:34.3728676Z 		37 |   // ACLs
2025-10-10T13:07:34.3728938Z 		38 |   network_acls {
2025-10-10T13:07:34.3729228Z 		39 |     bypass         = "AzureServices"
2025-10-10T13:07:34.3729510Z 		40 |     default_action = "Deny"
2025-10-10T13:07:34.3729840Z 		41 |     ip_rules       = var.conventions.firewall_whitelist
2025-10-10T13:07:34.3730129Z 		42 |   }
2025-10-10T13:07:34.3730366Z 		43 | 
2025-10-10T13:07:34.3730623Z 		44 | }
2025-10-10T13:07:34.3730762Z 
2025-10-10T13:07:34.3731066Z Check: CKV_AZURE_164: "Ensures that ACR uses signed/trusted images"
2025-10-10T13:07:34.3731471Z 	SKIPPED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3732070Z 	Suppress comment: The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3732739Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3733342Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3734004Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-164
2025-10-10T13:07:34.3734732Z Check: CKV_AZURE_165: "Ensure geo-replicated container registries to match multi-region container deployments."
2025-10-10T13:07:34.3735171Z 	SKIPPED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3735726Z 	Suppress comment: The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3736362Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3737061Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3737770Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-165
2025-10-10T13:07:34.3738238Z Check: CKV_AZURE_166: "Ensure container image quarantine, scan, and mark images verified"
2025-10-10T13:07:34.3738668Z 	SKIPPED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3739083Z 	Suppress comment:  The image scan/quarantine is not used until the remediation developed
2025-10-10T13:07:34.3739710Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3740415Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3741101Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-166
2025-10-10T13:07:34.3741547Z Check: CKV_AZURE_233: "Ensure Azure Container Registry (ACR) is zone redundant"
2025-10-10T13:07:34.3741944Z 	SKIPPED for resource: module.aps.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3742373Z 	Suppress comment:  Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3743025Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3743715Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3744383Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-233
2025-10-10T13:07:34.3744801Z Check: CKV_AZURE_164: "Ensures that ACR uses signed/trusted images"
2025-10-10T13:07:34.3745211Z 	SKIPPED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3745780Z 	Suppress comment: The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3746505Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3747198Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3747902Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-164
2025-10-10T13:07:34.3748537Z Check: CKV_AZURE_165: "Ensure geo-replicated container registries to match multi-region container deployments."
2025-10-10T13:07:34.3748970Z 	SKIPPED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3749527Z 	Suppress comment: The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3750145Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3750854Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3751569Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-165
2025-10-10T13:07:34.3752143Z Check: CKV_AZURE_166: "Ensure container image quarantine, scan, and mark images verified"
2025-10-10T13:07:34.3752567Z 	SKIPPED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3752987Z 	Suppress comment:  The image scan/quarantine is not used until the remediation developed
2025-10-10T13:07:34.3753641Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3754344Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3755190Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-166
2025-10-10T13:07:34.3755636Z Check: CKV_AZURE_233: "Ensure Azure Container Registry (ACR) is zone redundant"
2025-10-10T13:07:34.3756055Z 	SKIPPED for resource: module.flowx.module.aks.module.acre.azurerm_container_registry.acre
2025-10-10T13:07:34.3756502Z 	Suppress comment:  Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3757154Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3757863Z 	Calling File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:47-71
2025-10-10T13:07:34.3758552Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-233
2025-10-10T13:07:34.3758997Z Check: CKV_AZURE_164: "Ensures that ACR uses signed/trusted images"
2025-10-10T13:07:34.3759380Z 	SKIPPED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3759950Z 	Suppress comment: The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3760603Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3761015Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.3761615Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-164
2025-10-10T13:07:34.3762251Z Check: CKV_AZURE_165: "Ensure geo-replicated container registries to match multi-region container deployments."
2025-10-10T13:07:34.3762676Z 	SKIPPED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3763222Z 	Suppress comment: The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3763889Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3764301Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.3764892Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-165
2025-10-10T13:07:34.3765327Z Check: CKV_AZURE_233: "Ensure Azure Container Registry (ACR) is zone redundant"
2025-10-10T13:07:34.3765724Z 	SKIPPED for resource: module.aps.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3766134Z 	Suppress comment:  Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3766767Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3767165Z 	Calling File: /../modules/aps/acr.tf:1-31
2025-10-10T13:07:34.3767743Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-233
2025-10-10T13:07:34.3768174Z Check: CKV_AZURE_164: "Ensures that ACR uses signed/trusted images"
2025-10-10T13:07:34.3768635Z 	SKIPPED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3769215Z 	Suppress comment: The image signing enforcment is in the backlog for development (Jira: CCE-2232)
2025-10-10T13:07:34.3769847Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3770257Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.3770857Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-164
2025-10-10T13:07:34.3771500Z Check: CKV_AZURE_165: "Ensure geo-replicated container registries to match multi-region container deployments."
2025-10-10T13:07:34.3771944Z 	SKIPPED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3772477Z 	Suppress comment: The geo replication is out of the scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3773105Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3773495Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.3774090Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-165
2025-10-10T13:07:34.3774547Z Check: CKV_AZURE_233: "Ensure Azure Container Registry (ACR) is zone redundant"
2025-10-10T13:07:34.3774939Z 	SKIPPED for resource: module.flowx.module.acr.azurerm_container_registry.acre[0]
2025-10-10T13:07:34.3775375Z 	Suppress comment:  Currently zone redundancy is not a requirement for ACR and it is not supported from module
2025-10-10T13:07:34.3776031Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr/v1.5.0/main.tf:2-38
2025-10-10T13:07:34.3776449Z 	Calling File: /../modules/flowx/acr.tf:1-33
2025-10-10T13:07:34.3777053Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-233
2025-10-10T13:07:34.3777493Z Check: CKV_AZURE_171: "Ensure AKS cluster upgrade channel is chosen"
2025-10-10T13:07:34.3777890Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3778287Z 	Suppress comment: No automatic upgrades to keep the production systems intact
2025-10-10T13:07:34.3778917Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3779346Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3779956Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-171
2025-10-10T13:07:34.3780484Z Check: CKV_AZURE_7: "Ensure AKS cluster has Network Policy configured"
2025-10-10T13:07:34.3780864Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3781374Z 	Suppress comment: Network Profile require Azure CNI - for kubenet, not needed
2025-10-10T13:07:34.3782030Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3782418Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3783035Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-4
2025-10-10T13:07:34.3783476Z Check: CKV_AZURE_170: "Ensure that AKS use the Paid Sku for its SLA"
2025-10-10T13:07:34.3783863Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3784228Z 	Suppress comment: Paid SKU only enforced for production systems
2025-10-10T13:07:34.3784844Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3785277Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3785935Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-170
2025-10-10T13:07:34.3786395Z Check: CKV_AZURE_232: "Ensure that only critical system pods run on system nodes"
2025-10-10T13:07:34.3786781Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3787186Z 	Suppress comment:  There is no support for second node pool yet hence we need to skip this
2025-10-10T13:07:34.3787841Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3788260Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3788868Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-232
2025-10-10T13:07:34.3789379Z Check: CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources"
2025-10-10T13:07:34.3789816Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3790381Z 	Suppress comment: Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
2025-10-10T13:07:34.3791036Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3791468Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3792067Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-227
2025-10-10T13:07:34.3792496Z Check: CKV_AZURE_226: "Ensure ephemeral disks are used for OS disks"
2025-10-10T13:07:34.3792888Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3793401Z 	Suppress comment: Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
2025-10-10T13:07:34.3794038Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3794445Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3795360Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-226
2025-10-10T13:07:34.3795851Z Check: CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods."
2025-10-10T13:07:34.3796267Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3796672Z 	Suppress comment: The 50 pod node size constraint not needed for light workloads
2025-10-10T13:07:34.3797307Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3797833Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3798446Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-168
2025-10-10T13:07:34.3798877Z Check: CKV_AZURE_117: "Ensure that AKS uses disk encryption set"
2025-10-10T13:07:34.3799267Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3799925Z 	Suppress comment: The disk_encryption_set_id is part of the CMK decision. On hold until it decided - Using node based encryption, may override this
2025-10-10T13:07:34.3800638Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3801047Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3801695Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-uses-disk-encryption-set
2025-10-10T13:07:34.3802174Z Check: CKV_AZURE_171: "Ensure AKS cluster upgrade channel is chosen"
2025-10-10T13:07:34.3802563Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3803032Z 	Suppress comment: No automatic upgrades to keep the production systems intact
2025-10-10T13:07:34.3803665Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3804076Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3804693Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/azr-networking-171
2025-10-10T13:07:34.3805148Z Check: CKV_AZURE_7: "Ensure AKS cluster has Network Policy configured"
2025-10-10T13:07:34.3805545Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3806056Z 	Suppress comment: Network Profile require Azure CNI - for kubenet, not needed
2025-10-10T13:07:34.3806693Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3807125Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3807754Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azr-kubernetes-4
2025-10-10T13:07:34.3808203Z Check: CKV_AZURE_170: "Ensure that AKS use the Paid Sku for its SLA"
2025-10-10T13:07:34.3808589Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3808962Z 	Suppress comment: Paid SKU only enforced for production systems
2025-10-10T13:07:34.3809595Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3810016Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3810621Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-170
2025-10-10T13:07:34.3811067Z Check: CKV_AZURE_232: "Ensure that only critical system pods run on system nodes"
2025-10-10T13:07:34.3811466Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3811884Z 	Suppress comment:  There is no support for second node pool yet hence we need to skip this
2025-10-10T13:07:34.3812535Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3812962Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3813559Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-232
2025-10-10T13:07:34.3814034Z Check: CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources"
2025-10-10T13:07:34.3814599Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3815153Z 	Suppress comment: Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
2025-10-10T13:07:34.3815809Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3816236Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3816854Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-227
2025-10-10T13:07:34.3817294Z Check: CKV_AZURE_226: "Ensure ephemeral disks are used for OS disks"
2025-10-10T13:07:34.3817678Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3818247Z 	Suppress comment: Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
2025-10-10T13:07:34.3818898Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3819335Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3819936Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-226
2025-10-10T13:07:34.3820468Z Check: CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods."
2025-10-10T13:07:34.3820906Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3821301Z 	Suppress comment: The 50 pod node size constraint not needed for light workloads
2025-10-10T13:07:34.3821946Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3822374Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3822995Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-168
2025-10-10T13:07:34.3823400Z Check: CKV_AZURE_117: "Ensure that AKS uses disk encryption set"
2025-10-10T13:07:34.3823780Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3824447Z 	Suppress comment: The disk_encryption_set_id is part of the CMK decision. On hold until it decided - Using node based encryption, may override this
2025-10-10T13:07:34.3825155Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3825562Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3826229Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/ensure-that-aks-uses-disk-encryption-set
2025-10-10T13:07:34.3826755Z Check: CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources"
2025-10-10T13:07:34.3827229Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3827770Z 	Suppress comment: Missing AzureRM v4 support - fixed in later checkov release
2025-10-10T13:07:34.3828417Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3828860Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3829464Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-227
2025-10-10T13:07:34.3829948Z Check: CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods."
2025-10-10T13:07:34.3830390Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3830941Z 	Suppress comment: False positive - the requirement is meet
2025-10-10T13:07:34.3831585Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3832105Z 	Calling File: /../modules/aps/aks.tf:40-113
2025-10-10T13:07:34.3832725Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-168
2025-10-10T13:07:34.3833232Z Check: CKV_AZURE_227: "Ensure that the AKS cluster encrypt temp disks, caches, and data flows between Compute and Storage resources"
2025-10-10T13:07:34.3833708Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3834253Z 	Suppress comment: Missing AzureRM v4 support - fixed in later checkov release
2025-10-10T13:07:34.3835062Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3835497Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3836105Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/bc-azure-227
2025-10-10T13:07:34.3836600Z Check: CKV_AZURE_168: "Ensure Azure Kubernetes Cluster (AKS) nodes should use a minimum number of 50 pods."
2025-10-10T13:07:34.3837117Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster_node_pool.aks_nodepool
2025-10-10T13:07:34.3837631Z 	Suppress comment: False positive - the requirement is meet
2025-10-10T13:07:34.3838263Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/02-aks_nodepool.tf:1-136
2025-10-10T13:07:34.3838695Z 	Calling File: /../modules/flowx/aks.tf:40-116
2025-10-10T13:07:34.3839319Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-kubernetes-policies/azr-kubernetes-168
2025-10-10T13:07:34.3839794Z Check: CKV_AZURE_217: "Ensure Azure Application gateways listener that allow connection requests over HTTP"
2025-10-10T13:07:34.3840254Z 	SKIPPED for resource: module.shared_infra.module.appgw01.azurerm_application_gateway.apgw
2025-10-10T13:07:34.3840633Z 	Suppress comment:  We redirect HTTP to HTTPS listener
2025-10-10T13:07:34.3841268Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-application-gateway/v3.1.0/main.tf:2-219
2025-10-10T13:07:34.3841704Z 	Calling File: /../modules/shared/appgw.tf:185-242
2025-10-10T13:07:34.3842313Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-217
2025-10-10T13:07:34.3842743Z Check: CKV_AZURE_112: "Ensure that key vault key is backed by HSM"
2025-10-10T13:07:34.3843113Z 	SKIPPED for resource: module.shared_infra.module.encryption_key.azurerm_key_vault_key.kvauk
2025-10-10T13:07:34.3843506Z 	Suppress comment:  HSM is not allowed on standard vault
2025-10-10T13:07:34.3844142Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault-key/main.tf:1-31
2025-10-10T13:07:34.3844690Z 	Calling File: /../modules/shared/key-vault.tf:6-21
2025-10-10T13:07:34.3845364Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-key-vault-key-is-backed-by-hsm
2025-10-10T13:07:34.3846081Z Check: CKV_AZURE_93: "Ensure that managed disks use a specific set of disk encryption sets for the customer-managed key encryption"
2025-10-10T13:07:34.3846573Z 	SKIPPED for resource: module.shared_infra.module.management_vm_data_disk.azurerm_managed_disk.mdsk
2025-10-10T13:07:34.3847058Z 	Suppress comment:  We don't use Customer Managed Keys  
2025-10-10T13:07:34.3847712Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk/v1.3.0/manageddisk/main.tf:1-19
2025-10-10T13:07:34.3848273Z 	Calling File: /../modules/shared/management-vm.tf:37-48
2025-10-10T13:07:34.3849106Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-that-managed-disks-use-a-specific-set-of-disk-encryption-sets-for-the-customer-managed-key-encryption
2025-10-10T13:07:34.3849772Z Check: CKV_AZURE_1: "Ensure Azure Instance does not use basic authentication(Use SSH Key Instead)"
2025-10-10T13:07:34.3850219Z 	SKIPPED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.3850721Z 	Suppress comment: Based on SBB OPS team decision (21. June 2023) other teams may need to have the possibility to be able to login with basic auth into linux vm
2025-10-10T13:07:34.3851461Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.3852021Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.3852651Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/bc-azr-networking-1
2025-10-10T13:07:34.3853098Z Check: CKV_AZURE_149: "Ensure that Virtual machine does not enable password authentication"
2025-10-10T13:07:34.3853517Z 	SKIPPED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.3854051Z 	Suppress comment: Based on SBB OPS team decision (21. June 2023) other teams may need to have the possibility to be able to login with basic auth into linux vm
2025-10-10T13:07:34.3854769Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.3855321Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.3856057Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-azure-virtual-machine-does-not-enable-password-authentication
2025-10-10T13:07:34.3856556Z Check: CKV_AZURE_50: "Ensure Virtual Machine Extensions are not Installed"
2025-10-10T13:07:34.3856977Z 	SKIPPED for resource: module.shared_infra.module.management_vm.azurerm_linux_virtual_machine.vm
2025-10-10T13:07:34.3857455Z 	Suppress comment: Intentionally left the possibility open to use extensions in the future eg Network Watcher, Monitoring stuff etc
2025-10-10T13:07:34.3858147Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rhel-vm/v4.1.0/01-main.tf:1-82
2025-10-10T13:07:34.3858696Z 	Calling File: /../modules/shared/management-vm.tf:50-107
2025-10-10T13:07:34.3859319Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azr-general-14
2025-10-10T13:07:34.3859748Z Check: CKV_AZURE_206: "Ensure that Storage Accounts use replication"
2025-10-10T13:07:34.3860145Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3860688Z 	Suppress comment: The geo replication is out of scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3861345Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3861890Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.3862507Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-206
2025-10-10T13:07:34.3862961Z Check: CKV_AZURE_59: "Ensure that Storage accounts disallow public access"
2025-10-10T13:07:34.3863346Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3863737Z 	Suppress comment: Need to enable public access with IP whitelist
2025-10-10T13:07:34.3864389Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3864942Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.3865653Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-storage-accounts-disallow-public-access
2025-10-10T13:07:34.3866215Z Check: CKV_AZURE_206: "Ensure that Storage Accounts use replication"
2025-10-10T13:07:34.3866620Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3867168Z 	Suppress comment: The geo replication is out of scope currently (Jira: CCE-2249)
2025-10-10T13:07:34.3867836Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3868390Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.3869011Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/azr-general-206
2025-10-10T13:07:34.3869462Z Check: CKV_AZURE_59: "Ensure that Storage accounts disallow public access"
2025-10-10T13:07:34.3869880Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3870273Z 	Suppress comment: Need to enable public access with IP whitelist
2025-10-10T13:07:34.3870890Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3871487Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.3872200Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-storage-accounts-disallow-public-access
2025-10-10T13:07:34.3872856Z Check: CKV_AZURE_34: "Ensure that 'Public access level' is set to Private for blob containers"
2025-10-10T13:07:34.3873283Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3873672Z 	Suppress comment:  Public access is desabled by default
2025-10-10T13:07:34.3874323Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3875021Z 	Calling File: /../modules/aps/storage-account.tf:8-49
2025-10-10T13:07:34.3875741Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/set-public-access-level-to-private-for-blob-containers
2025-10-10T13:07:34.3876390Z Check: CKV_AZURE_34: "Ensure that 'Public access level' is set to Private for blob containers"
2025-10-10T13:07:34.3876836Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3877229Z 	Suppress comment:  Public access is desabled by default
2025-10-10T13:07:34.3877867Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3878417Z 	Calling File: /../modules/flowx/storage-account.tf:8-64
2025-10-10T13:07:34.3879229Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/set-public-access-level-to-private-for-blob-containers
2025-10-10T13:07:34.3879741Z Check: CKV2_AZURE_40: "Ensure storage account is not configured with Shared Key authorization"
2025-10-10T13:07:34.3880163Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3880677Z 	Suppress comment:  Azure Storage supports Azure AD authorization for requests to Blob and Queue storage only, so shared access key authorization cannot be totally removed.
2025-10-10T13:07:34.3881607Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3882305Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-40
2025-10-10T13:07:34.3882756Z Check: CKV2_AZURE_40: "Ensure storage account is not configured with Shared Key authorization"
2025-10-10T13:07:34.3883334Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3883802Z 	Suppress comment:  Azure Storage supports Azure AD authorization for requests to Blob and Queue storage only, so shared access key authorization cannot be totally removed.
2025-10-10T13:07:34.3884547Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3885223Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-iam-policies/bc-azure-2-40
2025-10-10T13:07:34.3885649Z Check: CKV2_AZURE_29: "Ensure AKS cluster has Azure CNI networking enabled"
2025-10-10T13:07:34.3886050Z 	SKIPPED for resource: module.aps.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3886570Z 	Suppress comment: Project decision - using kubenet instead of Azure CNI
2025-10-10T13:07:34.3887201Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3887879Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-29
2025-10-10T13:07:34.3888397Z Check: CKV2_AZURE_29: "Ensure AKS cluster has Azure CNI networking enabled"
2025-10-10T13:07:34.3888795Z 	SKIPPED for resource: module.flowx.module.aks.azurerm_kubernetes_cluster.aksc
2025-10-10T13:07:34.3889305Z 	Suppress comment: Project decision - using kubenet instead of Azure CNI
2025-10-10T13:07:34.3889940Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks/v3.0.0/01-main.tf:86-356
2025-10-10T13:07:34.3890629Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-29
2025-10-10T13:07:34.3891200Z Check: CKV2_AZURE_38: "Ensure soft-delete is enabled on Azure storage account"
2025-10-10T13:07:34.3891614Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3892000Z 	Suppress comment: Fails even if soft delete is enabled
2025-10-10T13:07:34.3892627Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3893324Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-38
2025-10-10T13:07:34.3893890Z Check: CKV2_AZURE_38: "Ensure soft-delete is enabled on Azure storage account"
2025-10-10T13:07:34.3894302Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3894692Z 	Suppress comment: Fails even if soft delete is enabled
2025-10-10T13:07:34.3895319Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3896016Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-38
2025-10-10T13:07:34.3896470Z Check: CKV2_AZURE_33: "Ensure storage account is configured with private endpoint"
2025-10-10T13:07:34.3896884Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3897332Z 	Suppress comment: Bug in Checkov, issue already created: https://github.com/bridgecrewio/checkov/issues/4638
2025-10-10T13:07:34.3898031Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3898741Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-33
2025-10-10T13:07:34.3899192Z Check: CKV2_AZURE_33: "Ensure storage account is configured with private endpoint"
2025-10-10T13:07:34.3899608Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3900137Z 	Suppress comment: Bug in Checkov, issue already created: https://github.com/bridgecrewio/checkov/issues/4638
2025-10-10T13:07:34.3900778Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3901490Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-33
2025-10-10T13:07:34.3901949Z Check: CKV2_AZURE_21: "Ensure Storage logging is enabled for Blob service for read requests"
2025-10-10T13:07:34.3902376Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3902764Z 	Suppress comment:  Logging is ensured by policy
2025-10-10T13:07:34.3903392Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3904229Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/ensure-storage-logging-is-enabled-for-blob-service-for-read-requests
2025-10-10T13:07:34.3904763Z Check: CKV2_AZURE_21: "Ensure Storage logging is enabled for Blob service for read requests"
2025-10-10T13:07:34.3905253Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_container.container
2025-10-10T13:07:34.3905648Z 	Suppress comment:  Logging is ensured by policy
2025-10-10T13:07:34.3906274Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/3-container.tf:4-19
2025-10-10T13:07:34.3907107Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-logging-policies/ensure-storage-logging-is-enabled-for-blob-service-for-read-requests
2025-10-10T13:07:34.3907633Z Check: CKV2_AZURE_1: "Ensure storage for critical data are encrypted with Customer Managed Key"
2025-10-10T13:07:34.3908047Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3908447Z 	Suppress comment: Postponed until CMK team is available
2025-10-10T13:07:34.3909085Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3909923Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-storage-for-critical-data-are-encrypted-with-customer-managed-key
2025-10-10T13:07:34.3910462Z Check: CKV2_AZURE_1: "Ensure storage for critical data are encrypted with Customer Managed Key"
2025-10-10T13:07:34.3910892Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3911281Z 	Suppress comment: Postponed until CMK team is available
2025-10-10T13:07:34.3911904Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3912752Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/ensure-storage-for-critical-data-are-encrypted-with-customer-managed-key
2025-10-10T13:07:34.3913231Z Check: CKV2_AZURE_32: "Ensure private endpoint is configured to key vault"
2025-10-10T13:07:34.3913615Z 	SKIPPED for resource: module.aps.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3914028Z 	Suppress comment:  We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3914689Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3915565Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-32
2025-10-10T13:07:34.3916002Z Check: CKV2_AZURE_32: "Ensure private endpoint is configured to key vault"
2025-10-10T13:07:34.3916390Z 	SKIPPED for resource: module.flowx.module.key_vault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3916908Z 	Suppress comment:  We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3917576Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.5.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3918283Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-32
2025-10-10T13:07:34.3918734Z Check: CKV2_AZURE_32: "Ensure private endpoint is configured to key vault"
2025-10-10T13:07:34.3919136Z 	SKIPPED for resource: module.shared_infra.module.appgwvault.azurerm_key_vault.kvau
2025-10-10T13:07:34.3919553Z 	Suppress comment:  We use separate private endpoints with each resource and not a global one.
2025-10-10T13:07:34.3920216Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault/v1.4.0/key-vault/main.tf:1-44
2025-10-10T13:07:34.3920917Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-general-policies/bc-azure-2-32
2025-10-10T13:07:34.3921349Z Check: CKV_AZURE_120: "Ensure that Application Gateway enables WAF"
2025-10-10T13:07:34.3921762Z 	SKIPPED for resource: module.shared_infra.module.appgw01.azurerm_application_gateway.apgw
2025-10-10T13:07:34.3922225Z 	Suppress comment:  WAF enabled using a WAF policy, hardcoded
2025-10-10T13:07:34.3922876Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-application-gateway/v3.1.0/main.tf:2-219
2025-10-10T13:07:34.3923660Z 	Guide: https://docs.prismacloud.io/en/enterprise-edition/policy-reference/azure-policies/azure-networking-policies/ensure-that-application-gateway-enables-waf
2025-10-10T13:07:34.3924168Z Check: CKV2_CUSTOM_STA: "Ensure storage account has inftrastructure encryption enabled"
2025-10-10T13:07:34.3924585Z 	SKIPPED for resource: module.aps.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3924980Z 	Suppress comment:  v7 pipeline issue workaround
2025-10-10T13:07:34.3925632Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3926109Z Check: CKV2_CUSTOM_STA: "Ensure storage account has inftrastructure encryption enabled"
2025-10-10T13:07:34.3926546Z 	SKIPPED for resource: module.flowx.module.storage_account.azurerm_storage_account.stac
2025-10-10T13:07:34.3926917Z 	Suppress comment:  v7 pipeline issue workaround
2025-10-10T13:07:34.3927556Z 	File: /.external_modules/dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount/v3.2.0/1-main.tf:2-308
2025-10-10T13:07:34.3927797Z 
2025-10-10T13:07:35.2098500Z Some checks failed.
2025-10-10T13:07:35.2099222Z ##[error]Checkov checks failed.
2025-10-10T13:07:35.2107378Z 
2025-10-10T13:07:35.2154223Z ##[section]Finishing: Checkov scan
