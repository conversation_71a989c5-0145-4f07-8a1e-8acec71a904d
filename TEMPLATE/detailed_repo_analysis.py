#!/usr/bin/env python3
"""
Detailed analysis showing which repos use which variables.
"""

import re
from pathlib import Path
from collections import defaultdict

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        pass
        
    return variables

def main():
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Track which repos use which variables
    var_to_repos = defaultdict(set)
    repo_to_vars = defaultdict(set)
    
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            
            for var_name in variables.keys():
                var_to_repos[var_name].add(terraform_dir.name)
                repo_to_vars[terraform_dir.name].add(var_name)
    
    # Key variables to analyze
    key_vars = [
        'subsidiary',
        'vnet_name', 'vnet_rgrp_name',
        'pe_vnet_name', 'pe_vnet_rgrp_name',
        'rtbl_name', 'rtbl_rgrp_name',
        'kvau_rgrp_name', 'kv_rgrp_name',
        'vnet_vnet_name', 'vnet_vnet_rgrp_name',
        'prep_subnet_vnet_name', 'prep_subnet_vnet_rgrp_name',
        'mhsm_umid_name', 'mhsm_umid_rgrp_name', 'mhsm_key',
        'ingress_nginx_ip', 'appgw_private_ip',
        'aad_admin_name', 'synapse_role_assignment_principal_id'
    ]
    
    output_file = 'REPO_USAGE_ANALYSIS.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("REPOSITORY USAGE ANALYSIS\n")
        f.write("=" * 100 + "\n\n")
        
        for var_name in key_vars:
            if var_name not in var_to_repos:
                continue
                
            repos = sorted(var_to_repos[var_name])
            f.write(f"\n{var_name} ({len(repos)} repos):\n")
            f.write("-" * 100 + "\n")
            for repo in repos:
                # Extract module name from repo name
                module_name = repo.replace('terraform-azurerm-', '')
                f.write(f"  - {module_name}\n")
        
        # Analyze patterns
        f.write("\n\n" + "=" * 100 + "\n")
        f.write("PATTERN ANALYSIS\n")
        f.write("=" * 100 + "\n\n")
        
        # Which repos use subsidiary?
        f.write("Repos using 'subsidiary' (30 repos):\n")
        f.write("-" * 100 + "\n")
        if 'subsidiary' in var_to_repos:
            for repo in sorted(var_to_repos['subsidiary']):
                module_name = repo.replace('terraform-azurerm-', '')
                f.write(f"  - {module_name}\n")
        
        # Which repos use HSM variables?
        f.write("\n\nRepos using HSM variables (mhsm_*):\n")
        f.write("-" * 100 + "\n")
        hsm_repos = set()
        for var in ['mhsm_umid_name', 'mhsm_umid_rgrp_name', 'mhsm_key']:
            if var in var_to_repos:
                hsm_repos.update(var_to_repos[var])
        for repo in sorted(hsm_repos):
            module_name = repo.replace('terraform-azurerm-', '')
            f.write(f"  - {module_name}\n")
        
        # Which repos use ingress_nginx_ip?
        f.write("\n\nRepos using 'ingress_nginx_ip' (AKS specific):\n")
        f.write("-" * 100 + "\n")
        if 'ingress_nginx_ip' in var_to_repos:
            for repo in sorted(var_to_repos['ingress_nginx_ip']):
                module_name = repo.replace('terraform-azurerm-', '')
                f.write(f"  - {module_name}\n")
        
        # Which repos use appgw variables?
        f.write("\n\nRepos using Application Gateway variables:\n")
        f.write("-" * 100 + "\n")
        appgw_repos = set()
        for var in ['appgw_vnet_name', 'appgw_vnet_rgrp_name', 'appgw_snet_name', 'appgw_private_ip']:
            if var in var_to_repos:
                appgw_repos.update(var_to_repos[var])
        for repo in sorted(appgw_repos):
            module_name = repo.replace('terraform-azurerm-', '')
            f.write(f"  - {module_name}\n")
        
        # Which repos use database/synapse variables?
        f.write("\n\nRepos using Database/Synapse variables:\n")
        f.write("-" * 100 + "\n")
        db_repos = set()
        for var in ['aad_admin_name', 'synapse_role_assignment_principal_id']:
            if var in var_to_repos:
                db_repos.update(var_to_repos[var])
        for repo in sorted(db_repos):
            module_name = repo.replace('terraform-azurerm-', '')
            f.write(f"  - {module_name}\n")
    
    print(f"Repository usage analysis saved to: {output_file}")

if __name__ == '__main__':
    main()

