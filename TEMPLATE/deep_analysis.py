#!/usr/bin/env python3
"""
Deep analysis of tfvars variables with mapping to composer variables.
"""

import re
from pathlib import Path
from collections import defaultdict
import yaml

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Match variable assignments
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return variables

def load_composer_variables():
    """Load all variables defined in composer system."""
    composer_vars = set()

    # Load from defaults.yaml (Azure-specific)
    defaults_file = Path('tooling/env/azure/defaults.yaml')
    if defaults_file.exists():
        with open(defaults_file, 'r') as f:
            data = yaml.safe_load(f)
            if 'variables' in data:
                for var in data['variables']:
                    if 'name' in var:
                        composer_vars.add(var['name'])

    # Load from composer.yaml (computed variables, Azure-specific)
    composer_file = Path('tooling/env/azure/composer.yaml')
    if composer_file.exists():
        with open(composer_file, 'r') as f:
            content = f.read()
            # Extract variable names from "- name: xxx" pattern
            for match in re.finditer(r'^\s*-\s*name:\s*(\w+)', content, re.MULTILINE):
                composer_vars.add(match.group(1))
    
    # Load from all override files (Azure-specific)
    override_dirs = [
        'tooling/env/azure/overrides/environment',
        'tooling/env/azure/overrides/region',
        'tooling/env/azure/overrides/project',
        'tooling/env/azure/overrides/combined'
    ]
    
    for override_dir in override_dirs:
        override_path = Path(override_dir)
        if override_path.exists():
            for yaml_file in override_path.glob('*.yaml'):
                with open(yaml_file, 'r') as f:
                    data = yaml.safe_load(f)
                    if data and 'variables' in data:
                        for var in data['variables']:
                            if 'name' in var:
                                composer_vars.add(var['name'])
    
    return composer_vars

def create_variable_mapping():
    """Create mapping between tfvars variables and composer variables."""
    
    # Define known mappings (semantic equivalence)
    mappings = {
        # Direct mappings (same name)
        'cloud': 'cloud',
        'environment': 'environment',
        'region': 'region',
        'project': 'project',
        
        # Semantic mappings (different names, same meaning)
        'subsidiary': None,  # Not in composer yet
        
        # Network - VNet
        'vnet_name': None,  # Could be computed from pattern
        'vnet_rgrp_name': None,  # Could be computed from pattern
        'pe_vnet_name': 'vnet_name',  # Usually same as vnet_name
        'pe_vnet_rgrp_name': 'vnet_rgrp_name',  # Usually same as vnet_rgrp_name
        'vnet_vnet_name': 'vnet_name',  # Alias
        'vnet_vnet_rgrp_name': 'vnet_rgrp_name',  # Alias
        'appgw_vnet_name': 'vnet_name',  # Usually same as vnet_name
        'appgw_vnet_rgrp_name': 'vnet_rgrp_name',  # Usually same as vnet_rgrp_name
        'primary_vnet_name': None,  # Multi-region specific
        'primary_vnet_rgrp_name': None,  # Multi-region specific
        'secondary_vnet_name': None,  # Multi-region specific
        'secondary_vnet_rgrp_name': None,  # Multi-region specific
        
        # Network - Subnet
        'snet_name': 'subnet_name',  # Different naming convention
        'subnet_name': 'subnet_name',  # Direct match
        'pe_snet_name': 'subnet_name',  # Usually same as subnet_name
        'vnet_snet_name': 'subnet_name',  # Alias
        'appgw_snet_name': None,  # AppGW specific subnet
        'prep_subnet_name': 'subnet_name',  # Usually same as subnet_name
        'prep_subnet_vnet_name': 'vnet_name',  # Alias
        'prep_subnet_vnet_rgrp_name': 'vnet_rgrp_name',  # Alias
        'prep_storage_subnet_name': 'subnet_name',  # Usually same
        'prep_storage_subnet_vnet_name': 'vnet_name',  # Alias
        'prep_storage_subnet_vnet_rgrp_name': 'vnet_rgrp_name',  # Alias
        'first_subnet': None,  # Multi-subnet specific
        'second_subnets': None,  # Multi-subnet specific
        'snet_prefix': None,  # CIDR specific
        'address_prefix': None,  # CIDR specific
        'primary_subnet_prefix': None,  # Multi-region specific
        'secondary_subnet_prefix': None,  # Multi-region specific
        'usernodepool_subnet_address_prefix': None,  # AKS specific
        
        # Network - Route Table
        'rtbl_name': None,  # Could be computed
        'rtbl_rgrp_name': None,  # Could be computed
        'rtbl_rg_name': 'rtbl_rgrp_name',  # Alias
        
        # Key Vault
        'kvau_name': 'key_vault_name',  # Different naming
        'kvau_rgrp_name': None,  # Could be computed
        'kv_name': 'key_vault_name',  # Alias
        'kv_rgrp_name': 'kvau_rgrp_name',  # Alias
        'keyvault_name': 'key_vault_name',  # Alias
        'keyvault_id': None,  # Could be computed
        
        # Application Gateway
        'appgw_private_ip': None,  # Project specific
        'apgw_public_ip_id': None,  # Project specific
        
        # Load Balancer
        'lb_private_ip': None,  # Project specific
        'lb_backend_ips': None,  # Project specific
        'lb_frontend_ip1': None,  # Project specific
        
        # HSM/Encryption
        'mhsm_umid_name': None,  # HSM specific
        'mhsm_umid_rgrp_name': None,  # HSM specific
        'mhsm_key': None,  # HSM specific
        
        # Database/Synapse
        'aad_admin_name': None,  # Project specific
        'synapse_role_assignment_principal_id': None,  # Project specific
        
        # Stream Analytics
        'strj_streaming_units': None,  # Project specific
        'strc_capacity': None,  # Project specific
        
        # Public IP
        'puip_name': None,  # Rare
        'puip_rgrp_name': None,  # Rare
        'enable_public_ip': None,  # Project specific
        
        # Remote State
        'remote_state_stac_name': None,  # Rare
        'remote_state_cont_name': None,  # Rare
        'remote_state_key': None,  # Rare
        'remote_state_rgrp_name': None,  # Rare
        
        # Other
        'resource_name_suffix': None,  # Could be computed
        'owner': None,  # Rare
        'enterprise_policy_location': None,  # Rare
        'primary_region': None,  # Multi-region specific
        'secondary_region': None,  # Multi-region specific
        'ingress_nginx_ip': None,  # AKS specific
    }
    
    return mappings

def analyze_usage_patterns():
    """Analyze how variables are used across repositories."""
    
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Track variable usage
    var_usage = defaultdict(lambda: {
        'count': 0,
        'repos': set(),
        'values': defaultdict(int)
    })
    
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            
            for var_name, var_value in variables.items():
                var_usage[var_name]['count'] += 1
                var_usage[var_name]['repos'].add(terraform_dir.name)
                var_usage[var_name]['values'][var_value] += 1
    
    return var_usage

def main():
    print("Loading composer variables...")
    composer_vars = load_composer_variables()
    
    print(f"Found {len(composer_vars)} variables in composer system")
    print(f"Composer variables: {sorted(composer_vars)}\n")
    
    print("Analyzing tfvars usage patterns...")
    var_usage = analyze_usage_patterns()
    
    print("Creating variable mapping...")
    mappings = create_variable_mapping()
    
    # Categorize variables
    categories = {
        'already_in_composer': [],  # Direct match in composer
        'can_be_computed': [],  # Can be computed from existing composer vars
        'needs_to_be_added': [],  # Should be added to composer
        'project_specific': [],  # Should be in project tfvars.template
        'rare_usage': [],  # Used in <5 repos
    }
    
    for var_name, usage in sorted(var_usage.items(), key=lambda x: x[1]['count'], reverse=True):
        count = usage['count']
        num_repos = len(usage['repos'])
        
        if var_name in composer_vars:
            categories['already_in_composer'].append((var_name, count, num_repos))
        elif var_name in mappings and mappings[var_name] in composer_vars:
            categories['already_in_composer'].append((var_name, count, num_repos))
        elif num_repos < 5:
            categories['rare_usage'].append((var_name, count, num_repos))
        elif var_name in mappings and mappings[var_name] is None:
            # Check if it's project-specific or can be computed
            if any(x in var_name for x in ['_ip', 'admin', 'principal', 'streaming', 'capacity', 'owner']):
                categories['project_specific'].append((var_name, count, num_repos))
            else:
                categories['can_be_computed'].append((var_name, count, num_repos))
        else:
            categories['needs_to_be_added'].append((var_name, count, num_repos))
    
    # Write report
    output_file = 'DEEP_ANALYSIS_REPORT.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("DEEP ANALYSIS: TFVARS VARIABLES vs COMPOSER SYSTEM\n")
        f.write("=" * 100 + "\n\n")
        
        f.write(f"Total unique variables in tfvars: {len(var_usage)}\n")
        f.write(f"Variables in composer system: {len(composer_vars)}\n\n")
        
        f.write("=" * 100 + "\n")
        f.write("CATEGORY 1: ALREADY IN COMPOSER (or has direct mapping)\n")
        f.write("=" * 100 + "\n")
        f.write(f"Count: {len(categories['already_in_composer'])}\n\n")
        for var_name, count, num_repos in categories['already_in_composer']:
            mapped_to = mappings.get(var_name, var_name)
            if mapped_to != var_name:
                f.write(f"  {var_name:40s} -> {mapped_to:30s} ({count:3d} uses, {num_repos:2d} repos)\n")
            else:
                f.write(f"  {var_name:40s} (direct match) ({count:3d} uses, {num_repos:2d} repos)\n")
        
        f.write("\n" + "=" * 100 + "\n")
        f.write("CATEGORY 2: CAN BE COMPUTED from existing composer variables\n")
        f.write("=" * 100 + "\n")
        f.write(f"Count: {len(categories['can_be_computed'])}\n\n")
        for var_name, count, num_repos in categories['can_be_computed']:
            f.write(f"  {var_name:40s} ({count:3d} uses, {num_repos:2d} repos)\n")
        
        f.write("\n" + "=" * 100 + "\n")
        f.write("CATEGORY 3: NEEDS TO BE ADDED to composer\n")
        f.write("=" * 100 + "\n")
        f.write(f"Count: {len(categories['needs_to_be_added'])}\n\n")
        for var_name, count, num_repos in categories['needs_to_be_added']:
            f.write(f"  {var_name:40s} ({count:3d} uses, {num_repos:2d} repos)\n")
        
        f.write("\n" + "=" * 100 + "\n")
        f.write("CATEGORY 4: PROJECT-SPECIFIC (should be in tfvars.template)\n")
        f.write("=" * 100 + "\n")
        f.write(f"Count: {len(categories['project_specific'])}\n\n")
        for var_name, count, num_repos in categories['project_specific']:
            f.write(f"  {var_name:40s} ({count:3d} uses, {num_repos:2d} repos)\n")
        
        f.write("\n" + "=" * 100 + "\n")
        f.write("CATEGORY 5: RARE USAGE (<5 repos, should be in tfvars.template)\n")
        f.write("=" * 100 + "\n")
        f.write(f"Count: {len(categories['rare_usage'])}\n\n")
        for var_name, count, num_repos in categories['rare_usage']:
            f.write(f"  {var_name:40s} ({count:3d} uses, {num_repos:2d} repos)\n")
    
    print(f"\nDeep analysis report saved to: {output_file}")
    
    # Print summary
    print("\n" + "=" * 100)
    print("SUMMARY")
    print("=" * 100)
    print(f"Already in composer:        {len(categories['already_in_composer']):3d} variables")
    print(f"Can be computed:            {len(categories['can_be_computed']):3d} variables")
    print(f"Needs to be added:          {len(categories['needs_to_be_added']):3d} variables")
    print(f"Project-specific:           {len(categories['project_specific']):3d} variables")
    print(f"Rare usage (<5 repos):      {len(categories['rare_usage']):3d} variables")
    print("=" * 100)

if __name__ == '__main__':
    main()

