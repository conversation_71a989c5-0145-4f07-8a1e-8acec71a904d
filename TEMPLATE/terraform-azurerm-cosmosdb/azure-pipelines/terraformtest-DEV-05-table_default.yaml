trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: DEV-demo01
  values:
  - DEV-demo01

- name: example
  type: string
  default: table/examples/05-table-default
  values:
  - table/examples/05-table-default
  
- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy
    - test

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 240

- name: no_proxy
  type: string
  default: ' '

- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v4
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    action: ${{ parameters.action }}
    environment: ${{ parameters.environment }}
    terraformProjectLocation: ${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    #terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
    skipCheckovScan: ${{ parameters.skip_checkov }}
    appCode: ${{ variables.appCode }}
    armServiceConnectionName: ${{ variables.armServiceConnectionName }}
    storageAccountResourceGroup: ${{ variables.storageAccountResourceGroup }}
    storageAccountName: ${{ variables.storageAccountName }}
    storageAccountContainerName: ${{ variables.storageAccountContainerName }}
    terraformVersion: '1.7.4'
