locals {
  index  = format("%02d", random_integer.index.result)
  suffix = "mlwt${local.index}"
}

#index generation for testing
resource "random_integer" "index" {
  min = 10
  max = 99
}

# Resource Group
module "rg01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.1"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "stac01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount?ref=v3.6.0"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id

  // Alerting is disabled only to reduce testing time
  builtin_metric_monitoring  = false
  resource_health_monitoring = false

  // Important configuration for ML workspace on the dependent storage account:
  blob_cors_rule = {
    allowed_origins = [
      "https://mlworkspace.azure.ai",
      "https://ml.azure.com",
      "https://*.ml.azure.com",
      "https://ai.azure.com",
      "https://*.ai.azure.com"
    ]
    allowed_headers    = ["*"]
    allowed_methods    = ["GET", "HEAD", "PUT", "DELETE", "OPTIONS", "POST", "PATCH"]
    exposed_headers    = ["*"]
    max_age_in_seconds = 1800
  }
}

# App Insights
module "appi01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-application-insights?ref=v1.2.0"
  resource_group_name  = module.rg01.rgrp.name
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  application_type     = "web"


  // To connect log analytics workspace to central AMPLS:
  // 1) add an additional provider with alias=logging
  // 2) Pass azurerm.loging provider to the module
  providers = {
    azurerm.log = azurerm.logging
  }

}

# Container registry - OPTIONAL

# module "acr01" {
#   #checkov:skip=CKV_TF_1:Not relevant in our environment
#   source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.7.1"
#   conventions          = module.conventions
#   resource_name_suffix = local.suffix
#   resource_group_name  = module.rg01.rgrp.name
#   subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
#   enable_quarantine    = true

#   zone_redundancy_enabled = true

#   generate_admin_token   = true
#   secret_expiration_date = time_offset.expirationdate.rfc3339 #Maximum expiration offset time is 180 days. Only in dev it can be 365.
#   key_vault_id           = data.azurerm_key_vault.default.id

#   // Alerting is disabled only to reduce testing time
#   builtin_metric_monitoring  = false
#   resource_health_monitoring = false
# }

# Machine Learning workspace
module "ml_workspace" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source = "../.."
  # source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-machine-learning?ref=v2.0.0"
  resource_name_suffix = local.suffix
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id

  key_vault_id            = data.azurerm_key_vault.default.id
  application_insights_id = module.appi01.appi.id
  storage_account_id      = module.stac01.stac.id
  # acr_id                  = module.acr01.acre.id

  # log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  # log_analytics_metrics      = ["AllMetrics"]
  # log_analytics_diag_logs    = ["AllLogs"]
  # resource_health_monitoring = true
  # builtin_metric_monitoring  = true

  create_prep = false #Private endpoint is a must for accessing this resource. Use false only for testing, default value is true.
  depends_on  = [module.rg01]
}
