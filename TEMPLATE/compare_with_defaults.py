#!/usr/bin/env python3
"""
Compare tfvars variables with defaults.yaml to find missing variables.
"""

import yaml
import re

def extract_variables_from_defaults_yaml(file_path):
    """Extract variable names from defaults.yaml."""
    variables = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            
        if 'variables' in data:
            for var in data['variables']:
                if 'name' in var:
                    variables.add(var['name'])
                    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return variables

def load_tfvars_variables(file_path):
    """Load variable names from the analysis file."""
    variables = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                var_name = line.strip()
                if var_name:
                    variables.add(var_name)
                    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return variables

def main():
    # Load variables from defaults.yaml
    defaults_vars = extract_variables_from_defaults_yaml('tooling/env/defaults.yaml')
    
    # Load variables from tfvars analysis
    tfvars_vars = load_tfvars_variables('tfvars_variables_list.txt')
    
    # Find missing variables
    missing_vars = tfvars_vars - defaults_vars
    
    # Find variables only in defaults (not used in tfvars)
    only_in_defaults = defaults_vars - tfvars_vars
    
    # Common variables
    common_vars = defaults_vars & tfvars_vars
    
    print("=" * 80)
    print("COMPARISON: TFVARS VARIABLES vs DEFAULTS.YAML")
    print("=" * 80)
    
    print(f"\nVariables in defaults.yaml: {len(defaults_vars)}")
    print(f"Variables in tfvars files: {len(tfvars_vars)}")
    print(f"Common variables: {len(common_vars)}")
    print(f"Missing from defaults.yaml: {len(missing_vars)}")
    print(f"Only in defaults.yaml (not used in tfvars): {len(only_in_defaults)}")
    
    print("\n" + "=" * 80)
    print("VARIABLES IN DEFAULTS.YAML")
    print("=" * 80)
    for var in sorted(defaults_vars):
        print(f"  - {var}")
    
    print("\n" + "=" * 80)
    print("COMMON VARIABLES (in both)")
    print("=" * 80)
    for var in sorted(common_vars):
        print(f"  ✓ {var}")
    
    print("\n" + "=" * 80)
    print("MISSING FROM DEFAULTS.YAML (used in tfvars but not in defaults.yaml)")
    print("=" * 80)
    
    # Load frequency data
    frequency_data = {}
    with open('tfvars_variables_analysis.txt', 'r', encoding='utf-8') as f:
        content = f.read()
        for line in content.split('\n'):
            match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*): (\d+) occurrences', line)
            if match:
                var_name = match.group(1)
                count = int(match.group(2))
                frequency_data[var_name] = count
    
    # Sort missing variables by frequency
    missing_sorted = sorted(missing_vars, key=lambda x: frequency_data.get(x, 0), reverse=True)
    
    for var in missing_sorted:
        freq = frequency_data.get(var, 0)
        print(f"  ✗ {var} ({freq} occurrences)")
    
    print("\n" + "=" * 80)
    print("ONLY IN DEFAULTS.YAML (not used in any tfvars)")
    print("=" * 80)
    for var in sorted(only_in_defaults):
        print(f"  ! {var}")
    
    # Save to file
    output_file = 'missing_variables_report.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("VARIABLES MISSING FROM DEFAULTS.YAML\n")
        f.write("=" * 80 + "\n")
        f.write(f"\nThese variables are used in tfvars files but not defined in defaults.yaml\n")
        f.write(f"Total missing: {len(missing_vars)}\n\n")
        
        f.write("Sorted by frequency (most common first):\n")
        f.write("-" * 80 + "\n\n")
        
        for var in missing_sorted:
            freq = frequency_data.get(var, 0)
            f.write(f"{var}: {freq} occurrences\n")
        
        f.write("\n\n" + "=" * 80 + "\n")
        f.write("CATEGORIZED MISSING VARIABLES\n")
        f.write("=" * 80 + "\n\n")
        
        # Categorize variables
        categories = {
            'Network - VNet': [],
            'Network - Subnet': [],
            'Network - Route Table': [],
            'Network - Private Endpoint': [],
            'Network - Application Gateway': [],
            'Network - Load Balancer': [],
            'Network - Other': [],
            'Key Vault': [],
            'HSM/Encryption': [],
            'Storage': [],
            'Database/Synapse': [],
            'AKS': [],
            'Stream Analytics': [],
            'Remote State': [],
            'Other': []
        }
        
        for var in missing_sorted:
            freq = frequency_data.get(var, 0)
            var_with_freq = f"{var} ({freq} occurrences)"
            
            if 'vnet' in var.lower() and 'subnet' not in var.lower():
                categories['Network - VNet'].append(var_with_freq)
            elif 'snet' in var.lower() or 'subnet' in var.lower():
                categories['Network - Subnet'].append(var_with_freq)
            elif 'rtbl' in var.lower() or 'route' in var.lower():
                categories['Network - Route Table'].append(var_with_freq)
            elif var.startswith('pe_') or 'prep' in var.lower():
                categories['Network - Private Endpoint'].append(var_with_freq)
            elif 'appgw' in var.lower() or 'apgw' in var.lower():
                categories['Network - Application Gateway'].append(var_with_freq)
            elif 'lb_' in var.lower():
                categories['Network - Load Balancer'].append(var_with_freq)
            elif 'ip' in var.lower() or 'address' in var.lower() or 'puip' in var.lower():
                categories['Network - Other'].append(var_with_freq)
            elif 'kv' in var.lower() or 'keyvault' in var.lower():
                categories['Key Vault'].append(var_with_freq)
            elif 'mhsm' in var.lower() or 'hsm' in var.lower():
                categories['HSM/Encryption'].append(var_with_freq)
            elif 'storage' in var.lower() or 'stac' in var.lower():
                categories['Storage'].append(var_with_freq)
            elif 'synapse' in var.lower() or 'aad_admin' in var.lower():
                categories['Database/Synapse'].append(var_with_freq)
            elif 'aks' in var.lower() or 'nodepool' in var.lower() or 'ingress' in var.lower():
                categories['AKS'].append(var_with_freq)
            elif 'str' in var.lower() and ('streaming' in var.lower() or 'capacity' in var.lower()):
                categories['Stream Analytics'].append(var_with_freq)
            elif 'remote_state' in var.lower():
                categories['Remote State'].append(var_with_freq)
            else:
                categories['Other'].append(var_with_freq)
        
        for category, vars_list in categories.items():
            if vars_list:
                f.write(f"\n{category}:\n")
                f.write("-" * 40 + "\n")
                for var in vars_list:
                    f.write(f"  - {var}\n")
    
    print(f"\n\nDetailed report saved to: {output_file}")

if __name__ == '__main__':
    main()

