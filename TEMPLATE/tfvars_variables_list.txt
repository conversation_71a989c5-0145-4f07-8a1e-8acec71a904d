project
cloud
environment
region
subsidiary
vnet_name
vnet_rgrp_name
snet_name
rtbl_name
rtbl_rgrp_name
pe_vnet_rgrp_name
pe_vnet_name
pe_snet_name
kvau_rgrp_name
kvau_name
kv_rgrp_name
kv_name
prep_subnet_vnet_rgrp_name
prep_subnet_name
prep_subnet_vnet_name
vnet_vnet_name
vnet_vnet_rgrp_name
vnet_snet_name
address_prefix
resource_name_suffix
appgw_vnet_rgrp_name
appgw_vnet_name
appgw_snet_name
subnet_name
mhsm_umid_name
mhsm_umid_rgrp_name
mhsm_key
ingress_nginx_ip
keyvault_id
strj_streaming_units
appgw_private_ip
synapse_role_assignment_principal_id
aad_admin_name
puip_rgrp_name
puip_name
strc_capacity
rtbl_rg_name
keyvault_name
snet_prefix
lb_frontend_ip1
lb_backend_ips
lb_private_ip
prep_storage_subnet_vnet_rgrp_name
prep_storage_subnet_name
prep_storage_subnet_vnet_name
enable_public_ip
first_subnet
second_subnets
apgw_public_ip_id
remote_state_rgrp_name
remote_state_cont_name
remote_state_stac_name
remote_state_key
usernodepool_subnet_address_prefix
primary_region
owner
secondary_vnet_rgrp_name
primary_subnet_prefix
primary_vnet_name
enterprise_policy_location
secondary_region
secondary_subnet_prefix
primary_vnet_rgrp_name
secondary_vnet_name
