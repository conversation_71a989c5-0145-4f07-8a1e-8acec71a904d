# Változók prioritási táblázata

## Gyo<PERSON>

| Prioritás | Változó neve | Előfordulás | Kategória | Javaslat |
|-----------|--------------|-------------|-----------|----------|
| **P0** | project | 308 | Core | ✅ Azonnal hozzáadandó |
| **P0** | environment | 294 | Core | ✅ Azonnal hozzáadandó |
| **P0** | region | 293 | Core | ✅ Azonnal hozzáadandó |
| **P0** | subsidiary | 176 | Core | ✅ Azonnal hozzáadandó |
| **P1** | vnet_name | 168 | Network | ✅ Azonnal hozzáadandó |
| **P1** | vnet_rgrp_name | 168 | Network | ✅ Azonnal hozzáadandó |
| **P1** | snet_name | 98 | Network | ✅ Azonnal hozzáadandó |
| **P1** | rtbl_name | 83 | Network | ✅ 1 héten belül |
| **P1** | rtbl_rgrp_name | 65 | Network | ✅ 1 héten belül |
| **P2** | pe_vnet_name | 64 | Network-PE | ✅ 1 héten belül |
| **P2** | pe_vnet_rgrp_name | 64 | Network-PE | ✅ 1 héten belül |
| **P2** | pe_snet_name | 64 | Network-PE | ✅ 1 héten belül |
| **P2** | kvau_name | 49 | KeyVault | ✅ 2 héten belül |
| **P2** | kvau_rgrp_name | 52 | KeyVault | ✅ 2 héten belül |
| **P3** | kv_name | 35 | KeyVault | ⚠️ Alias kvau_name-re |
| **P3** | kv_rgrp_name | 35 | KeyVault | ⚠️ Alias kvau_rgrp_name-re |
| **P3** | prep_subnet_name | 34 | Network-PE | ⚠️ Alias snet_name-re |
| **P3** | prep_subnet_vnet_name | 34 | Network-PE | ⚠️ Alias vnet_name-re |
| **P3** | prep_subnet_vnet_rgrp_name | 34 | Network-PE | ⚠️ Alias vnet_rgrp_name-re |
| **P3** | vnet_vnet_name | 32 | Network | ⚠️ Alias vnet_name-re |
| **P3** | vnet_vnet_rgrp_name | 32 | Network | ⚠️ Alias vnet_rgrp_name-re |
| **P3** | vnet_snet_name | 16 | Network | ⚠️ Alias snet_name-re |
| **P3** | address_prefix | 15 | Network | 🔧 Project override |
| **P3** | resource_name_suffix | 14 | Naming | 🔧 Default: "01" |
| **P4** | appgw_vnet_name | 12 | AppGW | 🔧 Speciális |
| **P4** | appgw_vnet_rgrp_name | 12 | AppGW | 🔧 Speciális |
| **P4** | appgw_snet_name | 12 | AppGW | 🔧 Speciális |
| **P4** | subnet_name | 11 | Network | ⚠️ Alias snet_name-re |
| **P4** | mhsm_umid_name | 9 | HSM | 🔧 Speciális |
| **P4** | mhsm_umid_rgrp_name | 9 | HSM | 🔧 Speciális |
| **P4** | mhsm_key | 9 | HSM | 🔧 Project override |
| **P4** | keyvault_id | 9 | KeyVault | 🔧 Computed value |
| **P4** | ingress_nginx_ip | 9 | AKS | 🔧 Project override |
| **P4** | strj_streaming_units | 9 | StreamAnalytics | 🔧 Project override |
| **P5** | appgw_private_ip | 7 | AppGW | 🔧 Project override |
| **P5** | synapse_role_assignment_principal_id | 6 | Synapse | 🔧 Project override |
| **P5** | aad_admin_name | 6 | Database | 🔧 Project override |
| **P5** | puip_name | 5 | Network | 🔧 Speciális |
| **P5** | puip_rgrp_name | 5 | Network | 🔧 Speciális |
| **P5** | strc_capacity | 5 | StreamAnalytics | 🔧 Project override |
| **P5** | rtbl_rg_name | 4 | Network | ⚠️ Alias rtbl_rgrp_name-re |
| **P5** | keyvault_name | 3 | KeyVault | ⚠️ Alias kvau_name-re |
| **P5** | snet_prefix | 3 | Network | 🔧 Project override |
| **P5** | lb_private_ip | 3 | LoadBalancer | 🔧 Project override |
| **P5** | lb_backend_ips | 3 | LoadBalancer | 🔧 Project override |
| **P5** | lb_frontend_ip1 | 3 | LoadBalancer | 🔧 Project override |
| **P5** | enable_public_ip | 3 | Network | 🔧 Default: false |
| **P5** | first_subnet | 3 | Network | 🔧 Speciális |
| **P5** | second_subnets | 3 | Network | 🔧 Speciális |
| **P5** | prep_storage_subnet_* | 3 | Network-PE | ⚠️ Alias-ok |

## Jelmagyarázat

- ✅ **Azonnal hozzáadandó**: Kritikus változók, amelyek a legtöbb modulban használatosak
- ⚠️ **Alias**: Más változó aliasa, ugyanazt az értéket kapja
- 🔧 **Project override**: Projekt-specifikus érték, override-olható
- 🔧 **Speciális**: Csak bizonyos use case-ekben használatos
- 🔧 **Computed value**: Más változókból számított érték
- 🔧 **Default**: Van default értéke, de override-olható

## Prioritási szintek

### P0 - Kritikus (azonnal)
- Alapvető konvenciók: project, environment, region, subsidiary
- **Időkeret**: Azonnal
- **Lefedettség**: 100% (minden modul használja)

### P1 - Magas prioritás (1 hét)
- Alapvető hálózati erőforrások: vnet, subnet, route table
- **Időkeret**: 1 hét
- **Lefedettség**: 60-100%

### P2 - Közepes prioritás (2 hét)
- Private Endpoint és Key Vault változók
- **Időkeret**: 2 hét
- **Lefedettség**: 30-60%

### P3 - Alacsony prioritás (3 hét)
- Alias-ok és alternatív elnevezések
- **Időkeret**: 3 hét
- **Lefedettség**: 10-30%

### P4 - Speciális (4 hét)
- Szolgáltatás-specifikus változók (AppGW, HSM, AKS)
- **Időkeret**: 4 hét
- **Lefedettség**: <10%

### P5 - Opcionális (később)
- Ritkán használt vagy nagyon specifikus változók
- **Időkeret**: Később, igény szerint
- **Lefedettség**: <5%

## Implementációs javaslat

### 1. fázis (azonnal) - P0 változók
```yaml
variables:
  - name: project
    value: ${{ parameters.project }}
  - name: environment
    value: ${{ parameters.environment }}
  - name: region
    value: ${{ parameters.region }}
  - name: subsidiary
    value: ${{ parameters.subsidiary }}
  - name: regionShort
    value: ${{ if(eq(parameters.region, 'westeurope'), 'weu', 'gwc') }}
```

### 2. fázis (1 hét) - P1 változók
```yaml
  - name: vnet_name
    value: "vnet-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"
  - name: vnet_rgrp_name
    value: "rgrp-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"
  - name: snet_name
    value: "snet-${{ variables.regionShort }}-pe01"
  - name: rtbl_name
    value: "rtbl-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-core"
  - name: rtbl_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
```

### 3. fázis (2 hét) - P2 változók
```yaml
  - name: pe_vnet_name
    value: ${{ variables.vnet_name }}
  - name: pe_vnet_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
  - name: pe_snet_name
    value: ${{ variables.snet_name }}
  - name: kvau_name
    value: "${{ parameters.project }}-${{ parameters.environment }}-shared01"
  - name: kvau_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
```

### 4. fázis (3 hét) - P3 alias-ok
```yaml
  # Aliases for backward compatibility
  - name: kv_name
    value: ${{ variables.kvau_name }}
  - name: kv_rgrp_name
    value: ${{ variables.kvau_rgrp_name }}
  - name: vnet_vnet_name
    value: ${{ variables.vnet_name }}
  - name: vnet_vnet_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
  - name: subnet_name
    value: ${{ variables.snet_name }}
```

## Várható hatás

### Lefedettség növekedés
- **Jelenlegi**: 2/69 változó (3%)
- **P0 után**: 6/69 változó (9%) - de 100% használati lefedettség
- **P1 után**: 11/69 változó (16%) - 80% használati lefedettség
- **P2 után**: 17/69 változó (25%) - 90% használati lefedettség
- **P3 után**: 28/69 változó (41%) - 95% használati lefedettség

### Karbantartási előnyök
- Kevesebb manuális tfvars fájl szerkesztés
- Konzisztens naming convention
- Könnyebb environment/region/project váltás
- Automatizált értékek generálása

