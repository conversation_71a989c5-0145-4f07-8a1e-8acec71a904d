{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "existingVNETName": {
            "type": "String"
        },
        "newSubnetName": {
            "type": "String"
        },
        "newSubnetAddressPrefix": {
            "type": "String"
        },
        "associatedRouteTable": {
            "type": "String"
        },
        "privateEndpointNetworkPolicies": {
            "type": "String"
        },
        "privateLinkServiceNetworkPolicies": {
            "type": "String"
        }
    },
    "resources": [
        {
            "type": "Microsoft.Network/virtualNetworks/subnets",
            "apiVersion": "2019-04-01",
            "name": "[format('{0}/{1}', parameters('existingVNETName'), parameters('newSubnetName'))]",
            "properties": {
                "addressPrefix": "[parameters('newSubnetAddressPrefix')]",
                "routeTable": {
                    "id": "[parameters('associatedRouteTable')]"
                },
                "serviceEndpoints": ${serviceEndpoints},
                "networkSecurityGroup": ${networkSecurityGroup},
                "delegations": ${delegations},
                "privateEndpointNetworkPolicies": "[parameters('privateEndpointNetworkPolicies')]",
                "privateLinkServiceNetworkPolicies": "[parameters('privateLinkServiceNetworkPolicies')]"
            }
        }
    ]
}