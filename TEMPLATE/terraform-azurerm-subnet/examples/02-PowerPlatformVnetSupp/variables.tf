#Conventions
variable "cloud" {}
variable "environment" {}
variable "primary_region" {}
variable "secondary_region" {}
variable "project" {}
variable "owner" {}

variable "primary_vnet_name" {}
variable "primary_vnet_rgrp_name" {}

variable "secondary_vnet_name" {}
variable "secondary_vnet_rgrp_name" {}

variable "primary_subnet_prefix" {}
variable "secondary_subnet_prefix" {}

variable "enterprise_policy_location" {}

