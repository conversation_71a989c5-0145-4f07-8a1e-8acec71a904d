module "primary_conventions" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"

  #Common
  cloud       = var.cloud
  environment = var.environment
  region      = var.primary_region

  #General
  project    = var.project
  department = "csdi"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner
    "Criticality"  = "false"
  }
}

#Additional conventions is needed because of the multi region requirement
module "secondary_conventions" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"

  #Common
  cloud       = var.cloud
  environment = var.environment
  region      = var.secondary_region

  #General
  project    = var.project
  department = "csdi"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner
    "Criticality"  = "false"
  }
}
