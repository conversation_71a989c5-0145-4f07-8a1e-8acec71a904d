{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"name": {"type": "String"}, "location": {"type": "String"}, "primary_vnet_id": {"type": "String"}, "primary_snet_name": {"type": "String"}, "failover_vnet_id": {"type": "String"}, "failover_snet_name": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.PowerPlatform/enterprisePolicies", "apiVersion": "2020-10-30-preview", "name": "[parameters('name')]", "location": "[parameters('location')]", "tags": {"Criticality": "false", "OwnerOU": "stratco"}, "kind": "NetworkInjection", "properties": {"healthStatus": "Undetermined", "networkInjection": {"virtualNetworks": [{"id": "[parameters('primary_vnet_id')]", "subnet": {"name": "[parameters('primary_snet_name')]"}}, {"id": "[parameters('failover_vnet_id')]", "subnet": {"name": "[parameters('failover_snet_name')]"}}]}}}]}