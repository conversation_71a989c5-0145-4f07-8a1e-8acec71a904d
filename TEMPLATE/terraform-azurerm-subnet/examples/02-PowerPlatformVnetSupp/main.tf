module "pp_rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.1"
  conventions          = module.primary_conventions
  resource_name_suffix = "ppvnetsupp"
}

#Create route table for Primary & Secondary subnets

module "pp_primary_rtbl01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rtbl?ref=v2.0.0"
  conventions          = module.primary_conventions
  resource_name_suffix = "ppprimary"
  resource_group_name  = module.pp_rg01.rgrp.name
}

module "pp_secondary_rtbl01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rtbl?ref=v2.0.0"
  conventions          = module.secondary_conventions
  resource_name_suffix = "ppsecondary"
  resource_group_name  = module.pp_rg01.rgrp.name
}

#Create network security groups for Primary & Secondary subnets

module "pp_primary_nesg_01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg?ref=v1.4.0"
  conventions          = module.primary_conventions
  resource_name_suffix = "ppprimary"
  resource_group_name  = module.pp_rg01.rgrp.name
}

module "pp_secondary_nesg_01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg?ref=v1.4.0"
  conventions          = module.secondary_conventions
  resource_name_suffix = "ppsecondary"
  resource_group_name  = module.pp_rg01.rgrp.name
}

#Create subnets in the Primary & Secondary VNet
module "primary-delegated-subnet" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source = "../.."
  #source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.5.0"
  conventions            = module.primary_conventions
  resource_name_suffix   = "ppvnetsupp_primary"
  vnet_name              = data.azurerm_virtual_network.primary_vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.primary_vnet.resource_group_name
  associated_route_table = module.pp_primary_rtbl01.rtbl.id
  nsg_needed             = true
  associated_nsg         = module.pp_primary_nesg_01.nesg.id
  delegation = [{
    name = "ppvnetsuppdelegation"
    service_delegation = {
      name    = "Microsoft.PowerPlatform/enterprisePolicies"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action"]
    }
  }]
  address_prefix = var.primary_subnet_prefix
}

module "secondary-delegated-subnet" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source = "../.."
  #source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.5.0"
  conventions            = module.secondary_conventions
  resource_name_suffix   = "ppvnetsupp_secondary"
  vnet_name              = data.azurerm_virtual_network.secondary_vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.secondary_vnet.resource_group_name
  associated_route_table = module.pp_secondary_rtbl01.rtbl.id
  nsg_needed             = true
  associated_nsg         = module.pp_secondary_nesg_01.nesg.id
  delegation = [{
    name = "ppvnetsuppdelegation"
    service_delegation = {
      name    = "Microsoft.PowerPlatform/enterprisePolicies"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action"]
    }
  }]
  address_prefix = var.secondary_subnet_prefix
}

//There is two tested and supported way to create the Enterprise policy resource. 

/*
Create enterprise policy from Terraform with AZAPI method. 
The caveat is that schema validation must be disabled because of an error on Microsoft's side with the validation.
If It's not disabled the Terraform deployment will break.
*/
resource "azapi_resource" "powerplatform_enterprise_policy" {
  type      = "Microsoft.PowerPlatform/enterprisePolicies@2020-10-30-preview"
  name      = "exampleazapienterprisepolicy"
  parent_id = module.pp_rg01.rgrp.id
  location  = var.enterprise_policy_location
  tags      = module.primary_conventions.tags
  body = jsonencode({
    properties = {
      healthStatus = "Undetermined"
      networkInjection = {
        virtualNetworks = [
          {
            id = data.azurerm_virtual_network.primary_vnet.id
            subnet = {
              name = module.primary-delegated-subnet.snet.name
            }
          },
          {
            id = data.azurerm_virtual_network.secondary_vnet.id
            subnet = {
              name = module.secondary-delegated-subnet.snet.name
            }
          }
        ]
      }
    },
    kind = "NetworkInjection"
  })
  schema_validation_enabled = false
}

/*
Create enterprise policy from Terraform with ARM resource group template deployment 
*/

resource "azurerm_resource_group_template_deployment" "deploy_enterprise_policy" {
  name                = "deploy_example_enterprise_policy"
  resource_group_name = module.pp_rg01.rgrp.name
  template_content    = file("${path.module}/arm_templates/enterprise_policy_template.json")

  parameters_content = jsonencode({
    name               = { value = "armrgtemplateexampleenterppolicy" }
    location           = { value = var.enterprise_policy_location }
    primary_vnet_id    = { value = data.azurerm_virtual_network.primary_vnet.id }
    primary_snet_name  = { value = module.primary-delegated-subnet.snet.name }
    failover_vnet_id   = { value = data.azurerm_virtual_network.secondary_vnet.id }
    failover_snet_name = { value = module.secondary-delegated-subnet.snet.name }
  })
  deployment_mode = "Incremental"
}