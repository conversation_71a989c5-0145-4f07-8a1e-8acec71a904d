#Conventions
cloud            = "azure"
environment      = "dev"
primary_region   = "westeurope"
secondary_region = "northeurope"
project          = "coe"
owner            = "Example Owner"

/*
 #VNets
primary_vnet_name      = "NameOfThePrimaryVNet"
primary_vnet_rgrp_name = "RGOfThePrimaryVNet"

secondary_vnet_name      = "NameOfTheSecondaryVNet"
secondary_vnet_rgrp_name = "RGOfTheSecondaryVNet" 
*/

#TEMP
primary_vnet_name      = "vnet-weu-dev-demo01-01"
primary_vnet_rgrp_name = "rgrp-weu-dev-demo01-01"

secondary_vnet_name      = "vnet-gwc-dev-demo01-01"
secondary_vnet_rgrp_name = "rgrp-gwc-dev-demo01-01"


#Subnets - 24 ranges are required
primary_subnet_prefix   = "10.94.0.0/24"
secondary_subnet_prefix = "10.95.0.0/24"

enterprise_policy_location = "europe"