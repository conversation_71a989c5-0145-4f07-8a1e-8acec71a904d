locals {
  # first_subnet   = "10.96.3.32/28"
  # second_subnets = ["10.96.3.64/28", "10.96.3.8/29"]

  #westeurope subnets
  //first_subnet   = "10.94.43.224/28"
  //second_subnets = ["************/29", "************/29"]

  first_subnet   = var.first_subnet
  second_subnets = var.second_subnets

  # subnet_tools version for testing purposes only, ↑ otherwise use the above version (without subnet_tools) ↑
  # subnet_lengths = [28, 28, 29]
  # first_subnet   = module.subnet_tools.next_subnets[0]
  # second_subnets = slice(module.subnet_tools.next_subnets, 1, 3) # startindex is inclusive, endindex is exclusive
}

# module "subnet_tools" {
#   #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
#   //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet-tools?ref=v1.2.5"
#   source                 = "../../../snet-tools"
#   vnet_name              = data.azurerm_virtual_network.vnet.name
#   vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
#   subnet_prefixes_length = local.subnet_lengths
# }

// Create route table
module "rtbl_example" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rtbl?ref=v2.0.0"
  conventions                   = module.conventions
  resource_name_suffix          = "rtbl01"
  resource_group_name           = data.azurerm_virtual_network.vnet.resource_group_name
  bgp_route_propagation_enabled = true
}

module "nesg_assoctst" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg?ref=v1.4.0"
  conventions          = module.conventions
  resource_name_suffix = "nsgassoctst"
  resource_group_name  = data.azurerm_virtual_network.vnet.resource_group_name
}

module "nesr_assoctst_01" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesr?ref=v1.4.0"
  conventions                 = module.conventions
  resource_name_suffix        = "nsgassoctst"
  resource_group_name         = data.azurerm_virtual_network.vnet.resource_group_name
  network_security_group_name = module.nesg_assoctst.nesg.name
  access                      = "Allow"
  direction                   = "Inbound"
  priority                    = 106
  protocol                    = "Tcp"
  source_port_range           = "*"
  destination_port_ranges     = ["9000", "9003", "1438", "1440", "1452"]
  source_address_prefix       = "*"
  destination_address_prefix  = "*"
}

module "subnet_tst1" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  //source                                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.5.0"
  source                                        = "../.."
  conventions                                   = module.conventions
  resource_name_suffix                          = "defaulttst1"
  vnet_name                                     = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name                                = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table                        = data.azurerm_route_table.rtbl.id
  private_link_service_network_policies_enabled = true
  private_endpoint_network_policies             = "Enabled"
  nsg_needed                                    = true
  associated_nsg                                = module.nesg_assoctst.nesg.id
  service_endpoints                             = ["Microsoft.KeyVault", "Microsoft.Storage"]
  delegation = [{
    name = "managedinstancedelegation"
    service_delegation = {
      name    = "Microsoft.Sql/managedInstances"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
    }
  }]
  address_prefix = local.first_subnet
}

module "subnet_tst2" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  count = length(local.second_subnets)

  //source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.5.0"
  source                 = "../.."
  conventions            = module.conventions
  resource_name_suffix   = "defaulttst${count.index + 2}"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table = module.rtbl_example.rtbl.id
  address_prefix         = local.second_subnets["${count.index}"]

  depends_on = [module.subnet_tst1]
}
