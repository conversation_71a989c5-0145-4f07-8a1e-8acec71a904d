## Name of the module
Azure Subnet Module

Shortname: snet

Terraform resource: azurerm_subnet

## Short description of the module
This Terraform module deploys an Azure Subnet

## Detailed description on Confluence
[Azure Subnet Module](https://confluence.otpbank.hu/x/JxBULg)

## Terraform version compatibility
Terraform v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.103.0

## Resources generated by the module
- SubNet

## Known bug
Occasionally the following error message is displayed during the terraform run:


Another operation on this or dependent resource is in progress.
Solution: re-run the current command
