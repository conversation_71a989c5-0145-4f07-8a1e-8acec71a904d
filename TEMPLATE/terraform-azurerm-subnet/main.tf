data "azurerm_virtual_network" "vnet_dest" {
  name                = var.vnet_name
  resource_group_name = var.vnet_rgrp_name
}

resource "azurerm_subnet" "snet" {
  name                 = local.snetname
  resource_group_name  = data.azurerm_virtual_network.vnet_dest.resource_group_name
  virtual_network_name = data.azurerm_virtual_network.vnet_dest.name
  address_prefixes     = [var.address_prefix]
  service_endpoints    = var.service_endpoints

  private_link_service_network_policies_enabled = var.private_link_service_network_policies_enabled
  private_endpoint_network_policies             = var.private_endpoint_network_policies

  dynamic "delegation" {
    for_each = var.delegation
    iterator = delegate
    content {
      name = delegate.value.name
      service_delegation {
        name    = delegate.value.service_delegation.name
        actions = delegate.value.service_delegation.actions
      }
    }
  }
}

resource "azurerm_subnet_route_table_association" "snet_rtbl_assoc" {

  count          = var.conventions.network_topology == "classic" ? 1 : 0
  subnet_id      = azurerm_subnet.snet.id
  route_table_id = var.associated_route_table
}

resource "azurerm_subnet_network_security_group_association" "snet_nesg_assoc" {
  count = var.nsg_needed ? 1 : 0

  subnet_id                 = azurerm_subnet.snet.id
  network_security_group_id = var.associated_nsg
}
