<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Subnet Module

Shortname: snet

Terraform resource: azurerm\_subnet

## Short description of the module
This Terraform module deploys an Azure Subnet

## Detailed description on Confluence
[Azure Subnet Module](https://confluence.otpbank.hu/x/JxBULg)

## Terraform version compatibility
Terraform v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.103.0

## Resources generated by the module
- SubNet

## Known bug
Occasionally the following error message is displayed during the terraform run:

Another operation on this or dependent resource is in progress.
Solution: re-run the current command

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 3.103.0)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 3.103.0)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.0.1"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for Resource Group creation

```hcl
locals {
  # first_subnet   = "**********/28"
  # second_subnets = ["**********/28", "*********/29"]

  #westeurope subnets
  //first_subnet   = "************/28"
  //second_subnets = ["************/29", "************/29"]

  first_subnet   = var.first_subnet
  second_subnets = var.second_subnets

  # subnet_tools version for testing purposes only, ↑ otherwise use the above version (without subnet_tools) ↑
  # subnet_lengths = [28, 28, 29]
  # first_subnet   = module.subnet_tools.next_subnets[0]
  # second_subnets = slice(module.subnet_tools.next_subnets, 1, 3) # startindex is inclusive, endindex is exclusive
}

# module "subnet_tools" {
#   #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
#   //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet-tools?ref=v1.2.5"
#   source                 = "../../../snet-tools"
#   vnet_name              = data.azurerm_virtual_network.vnet.name
#   vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
#   subnet_prefixes_length = local.subnet_lengths
# }

// Create route table
module "rtbl_example" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rtbl?ref=v1.4.0"
  conventions                   = module.conventions
  resource_name_suffix          = "rtbl01"
  resource_group_name           = data.azurerm_virtual_network.vnet.resource_group_name
  bgp_route_propagation_enabled = true
}

module "nesg_assoctst" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesg?ref=v1.3.0"
  conventions          = module.conventions
  resource_name_suffix = "nsgassoctst"
  resource_group_name  = data.azurerm_virtual_network.vnet.resource_group_name
}

module "nesr_assoctst_01" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-nesr?ref=v1.3.0"
  conventions                 = module.conventions
  resource_name_suffix        = "nsgassoctst"
  resource_group_name         = data.azurerm_virtual_network.vnet.resource_group_name
  network_security_group_name = module.nesg_assoctst.nesg.name
  access                      = "Allow"
  direction                   = "Inbound"
  priority                    = 106
  protocol                    = "Tcp"
  source_port_range           = "*"
  destination_port_ranges     = ["9000", "9003", "1438", "1440", "1452"]
  source_address_prefix       = "*"
  destination_address_prefix  = "*"
}

module "subnet_tst1" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  //source                                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.4.2"
  source                                        = "../.."
  conventions                                   = module.conventions
  resource_name_suffix                          = "defaulttst1"
  vnet_name                                     = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name                                = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table                        = data.azurerm_route_table.rtbl.id
  private_link_service_network_policies_enabled = true
  private_endpoint_network_policies             = "Enabled"
  nsg_needed                                    = true
  associated_nsg                                = module.nesg_assoctst.nesg.id
  service_endpoints                             = ["Microsoft.KeyVault", "Microsoft.Storage"]
  delegation = [{
    name = "managedinstancedelegation"
    service_delegation = {
      name    = "Microsoft.Sql/managedInstances"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
    }
  }]
  address_prefix = local.first_subnet
}

module "subnet_tst2" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  count = length(local.second_subnets)

  //source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.4.2"
  source                 = "../.."
  conventions            = module.conventions
  resource_name_suffix   = "defaulttst${count.index + 2}"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table = module.rtbl_example.rtbl.id
  address_prefix         = local.second_subnets["${count.index}"]

  depends_on = [module.subnet_tst1]
}

```



## Resources

The following resources are used by this module:

- [azurerm_subnet.snet](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/subnet) (resource)
- [azurerm_subnet_network_security_group_association.snet_nesg_assoc](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/subnet_network_security_group_association) (resource)
- [azurerm_subnet_route_table_association.snet_rtbl_assoc](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/subnet_route_table_association) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_address_prefix"></a> [address\_prefix](#input\_address\_prefix)

Description: (Required) The address prefix to use for the subnet.

Type: `string`

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: terraform-conventions module

Type: `any`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: Custom resource name suffix

Type: `string`

### <a name="input_vnet_name"></a> [vnet\_name](#input\_vnet\_name)

Description: (Required) The name of the virtual network in which to create the subnet.

Type: `string`

### <a name="input_vnet_rgrp_name"></a> [vnet\_rgrp\_name](#input\_vnet\_rgrp\_name)

Description: (Required) The resource group name of the resource groupt witch contains the virtual network in which to create the subnet.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_associated_nsg"></a> [associated\_nsg](#input\_associated\_nsg)

Description: (Optional) The Id of the network security group to associate with the subnet.

Type: `string`

Default: `null`

### <a name="input_associated_route_table"></a> [associated\_route\_table](#input\_associated\_route\_table)

Description: (Optional) The Id of the route table to associate with the subnet. This variable is compulsory if the network\_topology in conventions is classic.

Type: `string`

Default: `""`

### <a name="input_delegation"></a> [delegation](#input\_delegation)

Description: (Optional) One or more delegation blocks  
A delegation block supports the following:  
  name - (Required) A name for this delegation.  
  service\_delegation - (Required) A service\_delegation block. It supports the following:  
    name - (Required) The name of service to delegate to.  
    Possible values are: Microsoft.ApiManagement/service, Microsoft.AzureCosmosDB/clusters, Microsoft.BareMetal/AzureVMware, Microsoft.BareMetal/CrayServers, Microsoft.Batch/batchAccounts, Microsoft.ContainerInstance/containerGroups, Microsoft.ContainerService/managedClusters, Microsoft.Databricks/workspaces, Microsoft.DBforMySQL/flexibleServers, Microsoft.DBforMySQL/serversv2, Microsoft.DBforPostgreSQL/flexibleServers, Microsoft.DBforPostgreSQL/serversv2, Microsoft.DBforPostgreSQL/singleServers, Microsoft.HardwareSecurityModules/dedicatedHSMs, Microsoft.Kusto/clusters, Microsoft.Logic/integrationServiceEnvironments, Microsoft.LabServices/labplans, Microsoft.MachineLearningServices/workspaces, Microsoft.Netapp/volumes, Microsoft.Network/dnsResolvers, Microsoft.Network/managedResolvers, Microsoft.PowerPlatform/vnetaccesslinks, Microsoft.ServiceFabricMesh/networks, Microsoft.Sql/managedInstances, Microsoft.Sql/servers, Microsoft.StoragePool/diskPools, Microsoft.StreamAnalytics/streamingJobs, Microsoft.Synapse/workspaces, Microsoft.Web/hostingEnvironments, Microsoft.Web/serverFarms, Microsoft.Orbital/orbitalGateways, NGINX.NGINXPLUS/nginxDeployments and PaloAltoNetworks.Cloudngfw/firewalls.  
    actions - (Optional) A list of Actions which should be delegated.  
    This list is specific to the service to delegate to.  
    Possible values are Microsoft.Network/networkinterfaces/*, Microsoft.Network/publicIPAddresses/join/action, Microsoft.Network/publicIPAddresses/read, Microsoft.Network/virtualNetworks/read, Microsoft.Network/virtualNetworks/subnets/action, Microsoft.Network/virtualNetworks/subnets/join/action, Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action and Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action.

Type:

```hcl
list(object({
    name = string
    service_delegation = object({
      name    = string
      actions = optional(list(string))
    })
  }))
```

Default: `[]`

### <a name="input_nsg_needed"></a> [nsg\_needed](#input\_nsg\_needed)

Description: (Optional) Set to true if you want to associate a network security group with the subnet.

Type: `bool`

Default: `false`

### <a name="input_private_endpoint_network_policies"></a> [private\_endpoint\_network\_policies](#input\_private\_endpoint\_network\_policies)

Description: (Optional) Enable or Disable network policies for the private endpoint on the subnet. Possible values are Disabled, Enabled, NetworkSecurityGroupEnabled and RouteTableEnabled. Defaults to Enabled.

Type: `string`

Default: `"Enabled"`

### <a name="input_private_link_service_network_policies_enabled"></a> [private\_link\_service\_network\_policies\_enabled](#input\_private\_link\_service\_network\_policies\_enabled)

Description: (Optional) Enable or Disable network policies for the private link service on the subnet.   
Setting this to true will Enable the policy and setting this to false will Disable the policy.   
Defaults to true.  
In order to deploy a Private Link Service on a given subnet, you must set the private\_link\_service\_network\_policies\_enabled attribute to false.  
This setting is only applicable for the Private Link Service, for all other resources in the subnet access is controlled based on the Network Security Group which can be configured using the azurerm\_subnet\_network\_security\_group\_association resource.

Type: `bool`

Default: `true`

### <a name="input_service_endpoints"></a> [service\_endpoints](#input\_service\_endpoints)

Description: (Optional) The list of Service endpoints to associate with the subnet.  
Possible values include: Microsoft.AzureActiveDirectory, Microsoft.AzureCosmosDB, Microsoft.ContainerRegistry,  
Microsoft.EventHub, Microsoft.KeyVault, Microsoft.ServiceBus, Microsoft.Sql,  
Microsoft.Storage and Microsoft.Web.

Type: `list(string)`

Default: `[]`

## Outputs

The following outputs are exported:

### <a name="output_snet"></a> [snet](#output\_snet)

Description: Created SubNet

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->