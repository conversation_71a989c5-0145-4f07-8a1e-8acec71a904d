## Subnet module
### v1.5.0 - Oct 8, 2025 [current]
ENHANCEMENTS: 
- Update to pipelinetemplates v8, tooling v5.2
- Updated module versions
- Updated NGS checkov exception

### v1.4.2 - May 28, 2025
BUGFIXES:
- This update fixes versioning in the CHANGELOG

### v1.4.1 - January 24, 2025
ENHANCEMENTS: 
- Added example code for PowerPlatform VNet support feature with delegated subnets
- Updated pipelines to latest version

### v1.4.0 - August 14, 2024
FIXES:
- Fixed route table parameters in example
ENHANCEMENTS: 
- Update azurerm provider to 4.0
- Updated example to use demo01 environment

### v1.3.1 - August 14, 2024
ENHANCEMENTS: 
- Rename deprecated private_endpoint_network_policies_enabled parameter to private_endpoint_network_policies
- Raise the minimum version of azurerm provider to to 3.103

### v1.3.0 - April 19, 2024
FEATURES:
- Separated subnet module from network module for more flexible management
- Terraform test pipelines are added but cannot be used due to dependency issues therefore terratest is still used
- Added solution to test module in tst and prd environments and new execute pipeline. Jira ticket: CCE-6157
- Added .config file to enable new release pipeline to run
- Removed terraform version limitation

ENHANCEMENTS:
- Updated version of nested modules
- Removed old pipelines

### Versions from common network module

### v1.2.5 - April 2, 2024
ENHANCEMENTS:
    - Added destination_address_prefixes parameter to nesr submodule
    - Added source_address_prefixes parameter to nesr submodule
### v1.2.4 - January 17, 2024
BUG FIXES: 
- Subnet module corrected to manage route table creation and subnet creation within a deployment
### v1.2.3 - December 13, 2023 [deprecated]
FEATURES:
- Added option to use subnet module without compulsory route table association in case network_topology defined in conventions is not "classic"
- Updated examples in all submodules to care for new conventions module
BUG FIXES: 
- Removed "department" from examples at conventions variables
### v1.2.2 - December 4, 2023
ENHANCEMENTS:
- Raised minimum provider version at snet-tools, rute, rtbl, nesg to 3.50
- Updated module versions
- Updated tagging
- Removed local variables from main.tf to locals.tf
### v1.2.1 - November 20, 2023
BUG FIXES:
- PDNS registration time is limited to 10 minutes to avoid infinite loop issue
- If PDNS registration does not complete in 10 minutes the deployment will proceed and the registration issue need to be investiaged at Azure policies (Configure Private DNS virtual network link policy / Central Privatelink initiative)
### v1.2.0 - October 25, 2023
FEATURES:
- Added AMBA compliant metrics to PDNS - JIRA ticket: CCE-4466
ENHANCEMENTS:
- Raised provider azurerm versions up to 4.0.0 in all submodules
- Raised nesr module minimum azurerm version to [3.64.0](https://github.com/hashicorp/terraform-provider-azurerm/blob/main/CHANGELOG-v3.md#3640-july-06-2023), to improve validation of the name property and prevent creation of resources that are broken [#22336](https://github.com/hashicorp/terraform-provider-azurerm/issues/22336)
- Raised pdns module minimum azurerm version to [3.50.0](https://github.com/hashicorp/terraform-provider-azurerm/blob/main/CHANGELOG-v3.md#3500-march-30-2023), for virtual_network_id validation [#21129](https://github.com/hashicorp/terraform-provider-azurerm/issues/21129)
- Raised snet module minimum azurerm version to [3.58.0](https://github.com/hashicorp/terraform-provider-azurerm/blob/main/CHANGELOG-v3.md#3580-may-25-2023), for improved delegation support
- Raised vnic module minimum azurerm version to [3.50.0](https://github.com/hashicorp/terraform-provider-azurerm/blob/main/CHANGELOG-v3.md#3500-march-30-2023), for public_ip_address_id and subnet_id validation [#21129](https://github.com/hashicorp/terraform-provider-azurerm/issues/21129)
BUG FIXES:
- Added custom_dns_configs and private_dns_zone_configs to lifecycle ignore_changes in ordet to remove noise caused by DNS configuration handled by policy.

### v1.1.0 - October 10, 2023 
FEATURES:
- Added private-endpoint module custom_nic_enabled variable
- Added private-endpoint module ip_configuration variable to support setting static IP address allocation
- Added PR template for main branch
ENHANCEMENTS:
- Raised private-endpoint minimum azurerm version to [3.68.0](https://github.com/hashicorp/terraform-provider-azurerm/releases/tag/v3.68.0), in order to fix loading the subnet to lock from the API rather than the config during deletion [#22676](https://github.com/hashicorp/terraform-provider-azurerm/pull/22676)
BUG FIXES:
- Fixed TestNesg_default_BasicCreationTest failing on policy: Management port access from the Internet should be blocked
- Fixed TestSnet_default_BasicCreationTest failing on policy: Allowed locations
### v1.0.0 - September 22, 2023
- Module prepared for release v1.0.0
- Updated module versions
- Updated provider version
### v0.11.2 - September 09, 2023
- Private DNS Zone
    - Added alerting for RecordSetCapacityUtilization
### v0.11.1 - July 13, 2023
- Subnet
    - Revert naming
### v0.11.0 - July 12, 2023 [deprecated]
- Subnet
    - Revert from ARM template-based version to TF-based version
    - Update naming
- Private Endpoint
    - Fix terratest
### v0.10.0 - June 27, 2023
- Testing refactor for nesr, rute
- Update submodule versions to latest
### v0.9.0 - June 16, 2023
- Subnet
    - Remove tfcoremock completely from the module
    - Simplify the parameterisation
    - Add experimental snet-tools submodule to calculate free cidrs in vnet
### v0.8.2 - June 15, 2023
- Testing refactor for nesg, private-endpoint and rtbl modules
- Latest submodule versions are used
### v0.8.1 - June 14, 2023
- Subnet
    - Update readme
### v0.8.0 - June 14, 2023
- Subnet
    - Create subnet with Route table at the same time
    - Remove tfcoremock from module
    - Remove multiple subnet creation options
    - Remove snet_rtbl_assoc and snet_nesg_assoc from outputs
### v0.7.2 - June 14, 2023
- Bugfix: different default subscription handling PDNS module
### v0.7.1 - June 02, 2023
- Testing refactor for vnic submodule
### v0.7.0 - May 24, 2023
- Private DNS Module added
### v0.6.2 - May 15, 2023
- Subnet
    - Added snet_rtbl_assoc and snet_nesg_assoc to outputs
### v0.6.1 - April 17, 2023
- Route table, Subnet
    - Make route table & subnet names lowercase. Due to terraform bug, during association terraform mismanages the route tables due to casing mismatch, causing recreation of route table associations during every apply. The decision has ben made to make every route table lowercase.
### v0.6.0 - February 20, 2023
- Subnet
    - Enable or disable private endpoint network policies
    - Required route table association to create a subnet
    - Optional association of NSG with the created subnet
### v0.5.3 - March 22, 2023
- Private Endpoint
    - Added delay option
### v0.5.2 - March 14, 2023
- Remote PE functionality added
### v0.5.1 - March 4, 2023
- Subnet 
    - Add the ability to define subnets based on a list of cidr ranges
### v0.5.0 - February 20, 2023
- Created Readme for all modules
- Confulence page for network module & link to sub-module readmes
- Moved locals to main
- Created pipelines for module
- Updated convetions to v0.4.0

- Subnet
    - Private link service - enable network policies option
    - Added tests: default, subnet delegation, private link service
	
- Route table & route
    - Added tests: default
	
- Network security group & network security rule
    - Multi portrange support for source & destination port range(s)
    - Added tests: default with multi portrange

### v0.4.2 - January 19, 2023
- Removed data blocks, added Subnet Delegation
### v0.4.1 - January 12, 2023
- Updated submodules to conventions v0.3.0
### v0.4.0 - January 03, 2023
- Added Subnet, Route table, Route, Network Security Group, Network Security Rule
