variable "conventions" {
  description = "terraform-conventions module"
}

variable "resource_name_suffix" {
  type        = string
  description = "Custom resource name suffix"
}

variable "vnet_name" {
  type        = string
  description = "(Required) The name of the virtual network in which to create the subnet."
}

variable "vnet_rgrp_name" {
  type        = string
  description = "(Required) The resource group name of the resource groupt witch contains the virtual network in which to create the subnet."
}

variable "address_prefix" {
  type        = string
  description = "(Required) The address prefix to use for the subnet."
}

variable "service_endpoints" {
  type        = list(string)
  default     = []
  description = <<-EOT
    (Optional) The list of Service endpoints to associate with the subnet.
    Possible values include: Microsoft.AzureActiveDirectory, Microsoft.AzureCosmosDB, Microsoft.ContainerRegistry,
    Microsoft.EventHub, Microsoft.KeyVault, Microsoft.ServiceBus, Microsoft.Sql,
    Microsoft.Storage and Microsoft.Web.
    EOT
}

variable "delegation" {
  type = list(object({
    name = string
    service_delegation = object({
      name    = string
      actions = optional(list(string))
    })
  }))
  default     = []
  description = <<-EOT
  (Optional) One or more delegation blocks
  A delegation block supports the following:
    name - (Required) A name for this delegation.
    service_delegation - (Required) A service_delegation block. It supports the following:
      name - (Required) The name of service to delegate to.
      Possible values are: Microsoft.ApiManagement/service, Microsoft.AzureCosmosDB/clusters, Microsoft.BareMetal/AzureVMware, Microsoft.BareMetal/CrayServers, Microsoft.Batch/batchAccounts, Microsoft.ContainerInstance/containerGroups, Microsoft.ContainerService/managedClusters, Microsoft.Databricks/workspaces, Microsoft.DBforMySQL/flexibleServers, Microsoft.DBforMySQL/serversv2, Microsoft.DBforPostgreSQL/flexibleServers, Microsoft.DBforPostgreSQL/serversv2, Microsoft.DBforPostgreSQL/singleServers, Microsoft.HardwareSecurityModules/dedicatedHSMs, Microsoft.Kusto/clusters, Microsoft.Logic/integrationServiceEnvironments, Microsoft.LabServices/labplans, Microsoft.MachineLearningServices/workspaces, Microsoft.Netapp/volumes, Microsoft.Network/dnsResolvers, Microsoft.Network/managedResolvers, Microsoft.PowerPlatform/vnetaccesslinks, Microsoft.ServiceFabricMesh/networks, Microsoft.Sql/managedInstances, Microsoft.Sql/servers, Microsoft.StoragePool/diskPools, Microsoft.StreamAnalytics/streamingJobs, Microsoft.Synapse/workspaces, Microsoft.Web/hostingEnvironments, Microsoft.Web/serverFarms, Microsoft.Orbital/orbitalGateways, NGINX.NGINXPLUS/nginxDeployments and PaloAltoNetworks.Cloudngfw/firewalls.
      actions - (Optional) A list of Actions which should be delegated.
      This list is specific to the service to delegate to.
      Possible values are Microsoft.Network/networkinterfaces/*, Microsoft.Network/publicIPAddresses/join/action, Microsoft.Network/publicIPAddresses/read, Microsoft.Network/virtualNetworks/read, Microsoft.Network/virtualNetworks/subnets/action, Microsoft.Network/virtualNetworks/subnets/join/action, Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action and Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action.
  EOT
}

variable "private_link_service_network_policies_enabled" {
  type        = bool
  default     = true
  description = <<-EOT
    (Optional) Enable or Disable network policies for the private link service on the subnet. 
    Setting this to true will Enable the policy and setting this to false will Disable the policy. 
    Defaults to true.
    In order to deploy a Private Link Service on a given subnet, you must set the private_link_service_network_policies_enabled attribute to false.
    This setting is only applicable for the Private Link Service, for all other resources in the subnet access is controlled based on the Network Security Group which can be configured using the azurerm_subnet_network_security_group_association resource.
    EOT
}

variable "private_endpoint_network_policies" {
  type        = string
  default     = "Enabled"
  validation {
    condition     = contains(["Disabled", "Enabled", "NetworkSecurityGroupEnabled", "RouteTableEnabled"], var.private_endpoint_network_policies)
    error_message = "Provide authorized value: Disabled, Enabled, NetworkSecurityGroupEnabled and RouteTableEnabled"
  }
  description = <<-EOT
     (Optional) Enable or Disable network policies for the private endpoint on the subnet. Possible values are Disabled, Enabled, NetworkSecurityGroupEnabled and RouteTableEnabled. Defaults to Enabled. 
    EOT
}

variable "associated_route_table" {
  type        = string
  description = "(Optional) The Id of the route table to associate with the subnet. This variable is compulsory if the network_topology in conventions is classic."
  default     = ""
}

variable "nsg_needed" {
  type        = bool
  default     = false
  description = "(Optional) Set to true if you want to associate a network security group with the subnet."
}

variable "associated_nsg" {
  type        = string
  default     = null
  description = "(Optional) The Id of the network security group to associate with the subnet."
}

