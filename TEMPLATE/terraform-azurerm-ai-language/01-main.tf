data "azurerm_resource_group" "rg" {
  name = var.resource_group_name
}

resource "azapi_resource" "cognitive_account" {
  type      = "Microsoft.CognitiveServices/accounts@2024-10-01"
  name      = local.aiservice_name
  parent_id = data.azurerm_resource_group.rg.id
  location  = var.conventions.region
  tags      = local.tags

  body = {
    sku = {
      name = var.sku_name
    }
    kind = "TextAnalytics"
    properties = {
      customSubDomainName = var.custom_subdomain_name
      publicNetworkAccess = var.public_network_access_enabled ? "Enabled" : "Disabled"
      disableLocalAuth    = true
      networkAcls = {
        defaultAction       = "Deny"
        virtualNetworkRules = []
        ipRules             = []
      }
      encryption = {
        keySource = "Microsoft.KeyVault"
        keyVaultProperties = {
          identityClientId = var.customer_managed_key.identity_client_id
          keyVaultUri      = regex("^(https://[^/]+)", var.customer_managed_key.key_vault_key_id)[0]
          keyName          = regex("/keys/([^/]+)", var.customer_managed_key.key_vault_key_id)[0]
          keyVersion       = regex("/keys/[^/]+/([^/]+)$", var.customer_managed_key.key_vault_key_id)[0]
        }
      }
      # Add other properties as needed, e.g. capabilities, etc.
    }
    identity = {
      type                   = "SystemAssigned, UserAssigned"
      userAssignedIdentities = { for id in var.identity_ids : id => {} }
    }
  }

  response_export_values = ["id", "name", "properties"]
}

// Create Private EndPoint for Cognitive Services
module "privateendpoint01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-private-endpoint?ref=v1.3"
  conventions          = var.conventions
  resource_group_name  = var.resource_group_name
  location             = var.conventions.region
  resource_name_suffix = "${var.resource_name_suffix}-${local.resource_shortname}"
  subnet_id            = var.subnet_id
  resource_id          = azapi_resource.cognitive_account.id
  subresource_list     = ["account"]
}
