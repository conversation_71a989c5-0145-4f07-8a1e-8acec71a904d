locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "language"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

resource "azurerm_user_assigned_identity" "language" {
  location            = module.conventions.region
  name                = "umid-weu-${module.conventions.environment}-language-${local.index}"
  resource_group_name = module.rg01.rgrp.name
}

resource "azurerm_role_assignment" "umid_kv_enc" {
  scope                = data.azurerm_key_vault.default.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.language.principal_id
}

resource "time_sleep" "role_assignment_wait" {
  depends_on      = [azurerm_role_assignment.umid_kv_enc]
  create_duration = "120s"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "aisskey" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.7.1"
  conventions     = module.conventions
  key_name        = "testkey0${local.index}"
  key_vault_id    = data.azurerm_key_vault.default.id
  key_type        = "RSA"
  key_size        = 4096
  key_opts        = ["encrypt", "wrapKey", "unwrapKey", "decrypt", "sign"]
  expiration_date = time_offset.expirationdate.rfc3339
}

module "aita" {
  //source                               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-language?ref=v1.0.1"
  source                = "../.."
  conventions           = module.conventions
  resource_group_name   = module.rg01.rgrp.name
  resource_name_suffix  = local.suffix
  subnet_id             = data.azurerm_subnet.sn_privateendpoint.id
  sku_name              = "S"
  custom_subdomain_name = "aita${local.suffix}"

  builtin_metric_monitoring  = true
  resource_health_monitoring = true

  identity_ids = [azurerm_user_assigned_identity.language.id]
  customer_managed_key = {
    key_vault_key_id   = module.aisskey.azurerm_key_vault_key.id
    identity_client_id = azurerm_user_assigned_identity.language.client_id
  }
  depends_on = [time_sleep.role_assignment_wait]
}
