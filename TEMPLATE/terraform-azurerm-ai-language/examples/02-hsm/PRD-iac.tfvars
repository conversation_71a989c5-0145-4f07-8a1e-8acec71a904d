//Conventions
cloud       = "azure"
environment = "prd"
region      = "germanywestcentral"
project     = "iac"


//Subnet for private endpoint 
prep_subnet_name           = "snet-gwc-pe01"
prep_subnet_vnet_name      = "vnet-gwc-prd-iac-01"
prep_subnet_vnet_rgrp_name = "rgrp-gwc-prd-iac-01"

//MHSM
mhsm_umid_name      = "umid-gwc-prd-iac-01"
mhsm_umid_rgrp_name = "rgrp-gwc-prd-iac-01"
mhsm_key            = "https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/iacprd-hsm-01/3b5b774ece514c702238ed9f31ae7272"