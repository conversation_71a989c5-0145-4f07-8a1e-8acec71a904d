locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "language"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "aita" {
  //source                               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-language?ref=v1.0.1"
  source                = "../.."
  conventions           = module.conventions
  resource_group_name   = module.rg01.rgrp.name
  resource_name_suffix  = local.suffix
  subnet_id             = data.azurerm_subnet.sn_privateendpoint.id
  sku_name              = "S"
  custom_subdomain_name = "aita${local.suffix}"

  builtin_metric_monitoring  = true
  resource_health_monitoring = true

  identity_ids = [data.azurerm_user_assigned_identity.hsm.id]
  customer_managed_key = {
    key_vault_key_id   = var.mhsm_key
    identity_client_id = data.azurerm_user_assigned_identity.hsm.client_id
  }
}
