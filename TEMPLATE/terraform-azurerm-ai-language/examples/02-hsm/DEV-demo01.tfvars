//Conventions
cloud       = "azure"
environment = "dev"
region      = "germanywestcentral"
project     = "abb"

//Subnet for private endpoint 
prep_subnet_name           = "privateendpoints"
prep_subnet_vnet_name      = "vnet-gwc-dev-demo01-01"
prep_subnet_vnet_rgrp_name = "rgrp-gwc-dev-demo01-01"

//MHSM
mhsm_umid_name      = "umid-gwc-dev-demo-01"
mhsm_umid_rgrp_name = "rgrp-gwc-dev-demo01-01"
mhsm_key            = "https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/demo-hsm-01/1f37fc75970e04a320d0e6a49af0aae8"