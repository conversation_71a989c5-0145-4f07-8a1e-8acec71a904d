<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure AI Language (Text Analytics)

shortname: aita

azapi resource: azurerm\_cognitive\_account

## Short description of the module
This Terraform module deploys an Azure Cognitive Services Account with Text Analytics kind.

## Detailed description on Confluence
[Azure AI Language](https://confluence.otpbank.hu/x/mYd5Vg)

## Terraform version compatibility
Terraform >= v1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.110.0
provider registry.terraform.io/Azure/azapi >= 2.1.0
provider registry.terraform.io/hashicorp/time >= 0.9.1

## Release notes – changes in the current and previous versions
[ChangeLog.md](ChangeLog.md)

## Resources generated by the module
- Azure Cognitive Services Account

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azapi"></a> [azapi](#requirement\_azapi) (>= 2.1.0)

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 3.110.0)

- <a name="requirement_time"></a> [time](#requirement\_time) (>=0.9.1)

## Providers

The following providers are used by this module:

- <a name="provider_azapi"></a> [azapi](#provider\_azapi) (>= 2.1.0)

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 3.110.0)


## Example for Provider configuration

```hcl
#provider - All providers configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which are available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.17.0"
    }
    azapi = {
      source  = "Azure/azapi"
      version = "2.4.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.13.1"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.2.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for AI Speech Services

```hcl
locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "language"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

resource "azurerm_user_assigned_identity" "language" {
  location            = module.conventions.region
  name                = "umid-weu-${module.conventions.environment}-language-${local.index}"
  resource_group_name = module.rg01.rgrp.name
}

resource "azurerm_role_assignment" "umid_kv_enc" {
  scope                = data.azurerm_key_vault.default.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.language.principal_id
}

resource "time_sleep" "role_assignment_wait" {
  depends_on      = [azurerm_role_assignment.umid_kv_enc]
  create_duration = "120s"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "aisskey" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.7.1"
  conventions     = module.conventions
  key_name        = "testkey0${local.index}"
  key_vault_id    = data.azurerm_key_vault.default.id
  key_type        = "RSA"
  key_size        = 4096
  key_opts        = ["encrypt", "wrapKey", "unwrapKey", "decrypt", "sign"]
  expiration_date = time_offset.expirationdate.rfc3339
}

module "aita" {
  //source                               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-language?ref=v1.0.1"
  source                = "../.."
  conventions           = module.conventions
  resource_group_name   = module.rg01.rgrp.name
  resource_name_suffix  = local.suffix
  subnet_id             = data.azurerm_subnet.sn_privateendpoint.id
  sku_name              = "S"
  custom_subdomain_name = "aita${local.suffix}"

  builtin_metric_monitoring  = true
  resource_health_monitoring = true

  identity_ids = [azurerm_user_assigned_identity.language.id]
  customer_managed_key = {
    key_vault_key_id   = module.aisskey.azurerm_key_vault_key.id
    identity_client_id = azurerm_user_assigned_identity.language.client_id
  }
  depends_on = [time_sleep.role_assignment_wait]
}

```



## Resources

The following resources are used by this module:

- [azapi_resource.cognitive_account](https://registry.terraform.io/providers/Azure/azapi/latest/docs/resources/resource) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_customer_managed_key"></a> [customer\_managed\_key](#input\_customer\_managed\_key)

Description:   (Optional) A customer\_managed\_key block supports the following:  
    key\_vault\_key\_id - (Required) The ID of the Key Vault Key which should be used to Encrypt the data in this Cognitive Account.  
    user\_assigned\_identity\_id - (Required) The Client ID of the User Assigned Identity that has access to the key.

Type:

```hcl
object({
    key_vault_key_id   = string
    identity_client_id = string
  })
```

### <a name="input_identity_ids"></a> [identity\_ids](#input\_identity\_ids)

Description: (Required) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Cognitive Service. A SystemAssigned identity will always be created.

Type: `list(string)`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Specifies the name of the resource group in which to create the Speech Service.

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id)

Description: (Required) ID of the subnet where private endpoint should be created.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_alert_SuccessRate_threshold"></a> [alert\_SuccessRate\_threshold](#input\_alert\_SuccessRate\_threshold)

Description: (Optional) Threshold for Availability Rate alert rule.

Type: `number`

Default: `90`

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: (Optional) Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_cosa_tags"></a> [cosa\_tags](#input\_cosa\_tags)

Description: Tags to apply to the Cognitive Service

Type: `map(string)`

Default: `null`

### <a name="input_custom_subdomain_name"></a> [custom\_subdomain\_name](#input\_custom\_subdomain\_name)

Description: The subdomain name used for token-based authentication.

Type: `string`

Default: `null`

### <a name="input_log_analytics_diag_logs"></a> [log\_analytics\_diag\_logs](#input\_log\_analytics\_diag\_logs)

Description: (Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_metrics"></a> [log\_analytics\_metrics](#input\_log\_analytics\_metrics)

Description: (Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace

Type: `string`

Default: `null`

### <a name="input_public_network_access_enabled"></a> [public\_network\_access\_enabled](#input\_public\_network\_access\_enabled)

Description: (Optional) Whether the public network access is enabled? Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_sku_name"></a> [sku\_name](#input\_sku\_name)

Description: (Required) Specifies the SKU Name for this Cognitive Service Account. Possible values are F0, F1, S0, S, S1, S2, S3, S4, S5, S6, P0, P1, P2, E0 and DC0.

Type: `string`

Default: `""`

## Outputs

The following outputs are exported:

### <a name="output_aita"></a> [aita](#output\_aita)

Description: Azure Language

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->