//Diagnostic settings
module "cognitiveservices_logging" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  count                          = length(var.log_analytics_metrics) != 0 || length(var.log_analytics_diag_logs) != 0 ? 1 : 0
  source                         = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-diag?ref=v1.2"
  resource_name_suffix           = var.resource_name_suffix
  conventions                    = var.conventions
  diag_target_resource_id        = azapi_resource.cognitive_account.id
  diag_loganalytics_workspace_id = var.log_analytics_workspace_id
  diag_loganalytics_diag_logs    = var.log_analytics_diag_logs
  diag_loganalytics_metrics      = var.log_analytics_metrics
}

//----------------------------------------------------------------- Resource health alert
module "resource_health_cosa" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5"
  resource_health_alert = true
  alert_location        = var.resource_health_alert_location
  conventions           = var.conventions
  resource_group_name   = var.resource_group_name
  resource_name_suffix  = "cosa-resourcehealth-${var.resource_name_suffix}"

  scopes = [azapi_resource.cognitive_account.id]
}

// --------------------------------------------------------------- Default metric alerts
module "builtin_metrics_cosa" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count               = var.builtin_metric_monitoring ? 1 : 0
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//metric_alert?ref=v1.5"
  conventions         = var.conventions
  resource_group_name = var.resource_group_name
  metric_alerts = {
    "SuccessRate" = {
      resource_name_suffix = "cosa-SuccessRate-${var.resource_name_suffix}"
      scopes               = [azapi_resource.cognitive_account.id]
      description          = "Availability Rate" // The name is SuccessRate, the metric's name is Availability Rate. This is no typo here.
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.CognitiveServices/accounts"
          metric_name      = "SuccessRate"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_SuccessRate_threshold //default 90
        }
      ]
    }
  }
}
