variable "conventions" {
  description = "(Required) terraform-conventions module"

  validation {
    condition     = can(regex("^[-A-Za-z0-9]{2,}$", var.conventions.project))
    error_message = "Only alphanumeric characters and hyphens are allowed and should contain at least two characters."
  }
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix"

  validation {
    condition     = can(regex("^[-A-Za-z0-9]{2,}$", var.resource_name_suffix))
    error_message = "Only alphanumeric characters and hyphens are allowed and should contain at least two characters"
  }
}

variable "resource_group_name" {
  type        = string
  description = "(Required) Specifies the name of the resource group in which to create the Speech Service."
}

variable "sku_name" {
  type        = string
  default     = ""
  description = "(Required) Specifies the SKU Name for this Cognitive Service Account. Possible values are F0, F1, S0, S, S1, S2, S3, S4, S5, S6, P0, P1, P2, E0 and DC0."

  validation {
    condition     = contains(["F0", "S"], var.sku_name)
    error_message = "Provide authorized value: F0, S"
  }
}

variable "custom_subdomain_name" {
  type        = string
  description = "The subdomain name used for token-based authentication."
  default     = null
}

variable "identity_ids" {
  type        = list(string)
  description = "(Required) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Cognitive Service. A SystemAssigned identity will always be created."
}

variable "customer_managed_key" {
  type = object({
    key_vault_key_id   = string
    identity_client_id = string
  })
  description = <<EOT
  (Optional) A customer_managed_key block supports the following:
    key_vault_key_id - (Required) The ID of the Key Vault Key which should be used to Encrypt the data in this Cognitive Account.
    user_assigned_identity_id - (Required) The Client ID of the User Assigned Identity that has access to the key. 
  EOT
}

variable "subnet_id" {
  type        = string
  description = "(Required) ID of the subnet where private endpoint should be created."

}

variable "public_network_access_enabled" {
  type        = bool
  description = "(Optional) Whether the public network access is enabled? Defaults to false."
  default     = false
}

//Diagnostic settings variables
variable "log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace"
  type        = string
  default     = null
}

variable "log_analytics_diag_logs" {
  description = "(Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "log_analytics_metrics" {
  description = "(Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}


//Monitoring and alerts
variable "resource_health_monitoring" {
  description = "(Optional) Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null
}

variable "builtin_metric_monitoring" {
  description = "(Optional) Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "alert_SuccessRate_threshold" {
  description = "(Optional) Threshold for Availability Rate alert rule."
  type        = number
  default     = 90
}


//Tags
variable "cosa_tags" {
  description = "Tags to apply to the Cognitive Service"
  type        = map(string)
  default     = null
}
