## Name of the module
Azure AI Language (Text Analytics)

shortname: aita

aza<PERSON> resource: azurerm_cognitive_account

## Short description of the module
This Terraform module deploys an Azure Cognitive Services Account with Text Analytics kind.

## Detailed description on Confluence
[Azure AI Language](https://confluence.otpbank.hu/x/mYd5Vg)

## Terraform version compatibility
Terraform >= v1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.110.0
provider registry.terraform.io/Azure/azapi >= 2.1.0
provider registry.terraform.io/hashicorp/time >= 0.9.1

## Release notes – changes in the current and previous versions
[ChangeLog.md](ChangeLog.md)

## Resources generated by the module
- Azure Cognitive Services Account
