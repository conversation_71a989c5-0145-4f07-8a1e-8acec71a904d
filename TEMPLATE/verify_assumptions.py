#!/usr/bin/env python3
"""
Verify assumptions about variable usage and values.
"""

import re
from pathlib import Path
from collections import defaultdict

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        pass
        
    return variables

def main():
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Track variable values
    var_values = defaultdict(lambda: defaultdict(list))  # var_name -> value -> [files]
    
    # Track environment-specific values
    env_values = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))  # var_name -> env -> value -> [files]
    
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            
            # Extract environment from filename (DEV/TST/PRD)
            env = None
            if 'DEV-' in tfvars_file.name:
                env = 'DEV'
            elif 'TST-' in tfvars_file.name:
                env = 'TST'
            elif 'PRD-' in tfvars_file.name:
                env = 'PRD'
            
            for var_name, var_value in variables.items():
                file_ref = f"{terraform_dir.name}/{tfvars_file.relative_to(terraform_dir)}"
                var_values[var_name][var_value].append(file_ref)
                
                if env:
                    env_values[var_name][env][var_value].append(file_ref)
    
    output_file = 'VERIFICATION_REPORT.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("VERIFICATION REPORT - Checking Assumptions\n")
        f.write("=" * 100 + "\n\n")
        
        # Question 1: snet_name vs subnet_name
        f.write("=" * 100 + "\n")
        f.write("Q1: Are snet_name and subnet_name the same?\n")
        f.write("=" * 100 + "\n\n")
        
        if 'snet_name' in var_values:
            f.write(f"snet_name has {len(var_values['snet_name'])} unique values:\n")
            for value, files in sorted(var_values['snet_name'].items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"  '{value}' ({len(files)} files)\n")
                for file_ref in files[:3]:
                    f.write(f"    - {file_ref}\n")
                if len(files) > 3:
                    f.write(f"    ... and {len(files) - 3} more\n")
        
        f.write("\n")
        
        if 'subnet_name' in var_values:
            f.write(f"subnet_name has {len(var_values['subnet_name'])} unique values:\n")
            for value, files in sorted(var_values['subnet_name'].items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"  '{value}' ({len(files)} files)\n")
                for file_ref in files[:3]:
                    f.write(f"    - {file_ref}\n")
                if len(files) > 3:
                    f.write(f"    ... and {len(files) - 3} more\n")
        
        # Question 2: kv_name vs keyvault_name
        f.write("\n" + "=" * 100 + "\n")
        f.write("Q2: Are kv_name and keyvault_name the same?\n")
        f.write("=" * 100 + "\n\n")
        
        if 'kv_name' in var_values:
            f.write(f"kv_name has {len(var_values['kv_name'])} unique values:\n")
            for value, files in sorted(var_values['kv_name'].items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"  '{value}' ({len(files)} files)\n")
                for file_ref in files[:3]:
                    f.write(f"    - {file_ref}\n")
                if len(files) > 3:
                    f.write(f"    ... and {len(files) - 3} more\n")
        
        f.write("\n")
        
        if 'keyvault_name' in var_values:
            f.write(f"keyvault_name has {len(var_values['keyvault_name'])} unique values:\n")
            for value, files in sorted(var_values['keyvault_name'].items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"  '{value}' ({len(files)} files)\n")
                for file_ref in files[:3]:
                    f.write(f"    - {file_ref}\n")
                if len(files) > 3:
                    f.write(f"    ... and {len(files) - 3} more\n")
        
        # Question 3: subsidiary values
        f.write("\n" + "=" * 100 + "\n")
        f.write("Q3: Is subsidiary always 'otphq'?\n")
        f.write("=" * 100 + "\n\n")
        
        if 'subsidiary' in var_values:
            f.write(f"subsidiary has {len(var_values['subsidiary'])} unique values:\n")
            for value, files in sorted(var_values['subsidiary'].items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"  '{value}' ({len(files)} files)\n")
                for file_ref in files[:5]:
                    f.write(f"    - {file_ref}\n")
                if len(files) > 5:
                    f.write(f"    ... and {len(files) - 5} more\n")
        
        # Question 4: pe_vnet_name vs vnet_name
        f.write("\n" + "=" * 100 + "\n")
        f.write("Q4: Is pe_vnet_name always the same as vnet_name?\n")
        f.write("=" * 100 + "\n\n")
        
        # Compare files that have both variables
        f.write("Checking files that have BOTH pe_vnet_name and vnet_name:\n\n")
        
        for terraform_dir in terraform_dirs:
            examples_dir = terraform_dir / 'examples'
            
            if not examples_dir.exists():
                continue
                
            tfvars_files = list(examples_dir.rglob('*.tfvars'))
            
            for tfvars_file in tfvars_files:
                variables = extract_values_from_tfvars(tfvars_file)
                
                if 'pe_vnet_name' in variables and 'vnet_name' in variables:
                    pe_vnet = variables['pe_vnet_name']
                    vnet = variables['vnet_name']
                    match = "✓ SAME" if pe_vnet == vnet else "✗ DIFFERENT"
                    
                    file_ref = f"{terraform_dir.name}/{tfvars_file.relative_to(terraform_dir)}"
                    f.write(f"{match}: {file_ref}\n")
                    f.write(f"  pe_vnet_name = '{pe_vnet}'\n")
                    f.write(f"  vnet_name    = '{vnet}'\n\n")
        
        # Question 5: Environment-specific values
        f.write("\n" + "=" * 100 + "\n")
        f.write("Q5: Which variables have different values per environment?\n")
        f.write("=" * 100 + "\n\n")
        
        # Focus on variables that appear in multiple environments
        env_varying_vars = []
        
        for var_name, env_data in env_values.items():
            # Check if variable appears in multiple environments
            if len(env_data) >= 2:
                # Check if values differ across environments
                all_values = set()
                for env, values in env_data.items():
                    all_values.update(values.keys())
                
                if len(all_values) > 1:
                    env_varying_vars.append((var_name, env_data))
        
        f.write(f"Found {len(env_varying_vars)} variables with environment-specific values:\n\n")
        
        for var_name, env_data in sorted(env_varying_vars, key=lambda x: x[0]):
            f.write(f"\n{var_name}:\n")
            f.write("-" * 80 + "\n")
            
            for env in ['DEV', 'TST', 'PRD']:
                if env in env_data:
                    f.write(f"  {env}:\n")
                    for value, files in sorted(env_data[env].items(), key=lambda x: len(x[1]), reverse=True):
                        f.write(f"    '{value}' ({len(files)} files)\n")
                        for file_ref in files[:2]:
                            f.write(f"      - {file_ref}\n")
                        if len(files) > 2:
                            f.write(f"      ... and {len(files) - 2} more\n")
        
        # Special focus on ingress_nginx_ip
        f.write("\n\n" + "=" * 100 + "\n")
        f.write("SPECIAL FOCUS: ingress_nginx_ip (AKS)\n")
        f.write("=" * 100 + "\n\n")
        
        if 'ingress_nginx_ip' in env_values:
            for env in ['DEV', 'TST', 'PRD']:
                if env in env_values['ingress_nginx_ip']:
                    f.write(f"{env}:\n")
                    for value, files in env_values['ingress_nginx_ip'][env].items():
                        f.write(f"  '{value}'\n")
                        for file_ref in files:
                            f.write(f"    - {file_ref}\n")
                    f.write("\n")
    
    print(f"Verification report saved to: {output_file}")
    
    # Print summary
    print("\n" + "=" * 100)
    print("QUICK SUMMARY")
    print("=" * 100)
    
    if 'subsidiary' in var_values:
        print(f"\nQ3: subsidiary values: {list(var_values['subsidiary'].keys())}")
        if len(var_values['subsidiary']) == 1 and 'otphq' in var_values['subsidiary']:
            print("    ✓ YES, always 'otphq'")
        else:
            print("    ✗ NO, has multiple values!")
    
    print(f"\nQ5: Variables with environment-specific values: {len(env_varying_vars)}")
    if env_varying_vars:
        print("    Top 10:")
        for var_name, _ in sorted(env_varying_vars, key=lambda x: x[0])[:10]:
            print(f"      - {var_name}")

if __name__ == '__main__':
    main()

