parameters:
  # Common
  - name: environment
  - name: taskcondition
    default: succeeded()

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformLogLevel
    default: 'ERROR'
  - name: armServiceConnectionName    

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForPlan
  - name: terraformCLIOptionsForPlan
  
  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

  # Not exposed options
  - name: terraformDestroyPlan
    type: boolean
    default: false
  - name: tfvarsToUseFromPipelineVariables
    type: object
    default: {}

### Plan steps
steps:

  # Terraform plan
  - task: AzureCLI@2
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform plan
    ${{ else }}:
      displayName: Generate destroy plan
    name: plan
    condition: ${{ parameters.taskcondition }}
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      TF_DESTROY_PLAN: ${{ lower(parameters.terraformDestroyPlan) }}
      TERRAFORM_VARS_FILE: $(terraformVarsFile)
      ${{ each var in parameters.tfvarsToUseFromPipelineVariables }}:
        TF_VAR_${{ var.tfvarName }}: ${{ var.varName }}
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      scriptType: bash  
      workingDirectory: "$(Build.SourcesDirectory)/${{ parameters.terraformProjectLocation }}"
      scriptLocation: inlineScript
      inlineScript: |
        set -eu  # fail on error
        export TF_LOG=${{ parameters.terraformLogLevel}}
        TFVARS_PARAMS=""

        TF_EXTRA_ARGS=""
        if [[ -v TF_DESTROY_PLAN ]]; then
          if [[ "$TF_DESTROY_PLAN" == "true" ]]; then
            echo "##vso[task.logissue type=warning]The Terraform plan will be created with the '-destroy' option. This means that the plan will be a destruction plan for this entire Terraform-managed infrastructure!"
            TF_EXTRA_ARGS="-destroy"
          fi
        fi

        echo "*******************************************************"
        echo "Terraform inputs from tfvars files:"
        echo ""

        # Check if composer system generated a tfvars file
        if [[ -n "${TERRAFORM_VARS_FILE:-}" ]] && [[ -f "${TERRAFORM_VARS_FILE}" ]]; then
          # New composer system: use the generated tfvars file
          echo "Using composer-generated tfvars file:"
          echo "  File: ${TERRAFORM_VARS_FILE}"
          echo ""
          TFVARS_PARAMS="-var-file=${TERRAFORM_VARS_FILE}"
          echo "Content:"
          cat "${TERRAFORM_VARS_FILE}"
          echo ""
        else
          # Legacy system: use GLOBAL_TFVARS and LOCAL_TFVARS
          echo "Using legacy tfvars files:"
          GLOBAL_TFVARS="$(Pipeline.Workspace)/tooling_repo/env/${{ parameters.environment }}.tfvars"
          LOCAL_TFVARS="$(Build.SourcesDirectory)/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"

          if [[ -f "$GLOBAL_TFVARS" ]]; then
            TFVARS_PARAMS="-var-file=$GLOBAL_TFVARS"
            echo "  GLOBAL_TFVARS: $GLOBAL_TFVARS"
            echo ""
            echo "Content:"
            cat $GLOBAL_TFVARS
            echo ""
          else
            echo "##vso[task.logissue type=warning]The tfvars file at path $GLOBAL_TFVARS does not exist."
          fi

          if [[ -f "$LOCAL_TFVARS" ]]; then
            TFVARS_PARAMS="$TFVARS_PARAMS -var-file=$LOCAL_TFVARS"
            echo "  LOCAL_TFVARS: $LOCAL_TFVARS"
            echo ""
            echo "Content:"
            cat $LOCAL_TFVARS
            echo ""
          fi
        fi

        echo "*******************************************************"
        echo "Terraform inputs from pipeline variables:"
        printenv | grep "TF_VAR_" || true
        echo "*******************************************************"

        set +e
        terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} plan \
          -detailed-exitcode \
          $TFVARS_PARAMS \
          -out="$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" $TF_EXTRA_ARGS ${{ parameters.terraformCLIOptionsForPlan }}
        retVal=$?
        set -e
        if [ $retVal -eq 2 ]; then
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]true'
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -json "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" | jq '.' > $(Build.SourcesDirectory)/tfplan.json
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -no-color "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" > $(Build.SourcesDirectory)/tfplan.txt
            echo '##vso[task.uploadsummary]$(Build.SourcesDirectory)/tfplan.txt'
            echo "------------------------------------------------------------"
            # Store the name of resource group created in case the plan executed in the initialization stage
            if [ "${{ parameters.artifactname }}" == "tfplan" ] 
            then
              RGRP_NAME=`jq -r '.resource_changes[] | select(.type == "azurerm_resource_group" and .change.actions[0] == "create") | .change.after.name' $(Build.SourcesDirectory)/tfplan.json | grep -v null | uniq`
              echo "Resource group name stored in artifact: ${RGRP_NAME}"
              echo "${RGRP_NAME}" > $(Build.SourcesDirectory)/rgname.txt
            fi
            exit 0
        else
            if [ $retVal -eq 0 ]; then
              echo "##vso[task.logissue type=warning]Terraform plan detected that no changes are needed. Your current infrastructure matches the configuration, or nothing to destroy."
            fi
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]false'
        fi
        exit $retVal

  # Copy and Publish plan artifacts for deployment
  - ${{ if ne(parameters.planFilePath, '') }}:
    - task: CopyFiles@2
      displayName: Copy Terraform plan files to ArtifactStagingDirectory
      condition: ${{ parameters.taskcondition }}
      inputs:
        Contents: |
          $(Build.SourcesDirectory)/tfplan.json
          $(Build.SourcesDirectory)/${{ parameters.planFilePath }}
          $(Build.SourcesDirectory)/tfplan.txt
          $(Build.SourcesDirectory)/rgname.txt
        TargetFolder: $(Build.ArtifactStagingDirectory)

    - task: PublishPipelineArtifact@1
      displayName: "Publish Terraform plan as pipeline artifact"
      condition: ${{ parameters.taskcondition }}
      inputs:
        targetPath: $(Build.ArtifactStagingDirectory)
        artifact: ${{ parameters.artifactname }}
