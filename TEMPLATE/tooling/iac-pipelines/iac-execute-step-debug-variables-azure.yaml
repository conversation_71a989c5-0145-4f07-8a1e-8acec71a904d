# Debug step: Display all available Composer variables
# This step runs early in the pipeline to help with debugging
# All variables are available from composer.yaml

steps:
- task: Bash@3
  displayName: 'Composer Variables'
  inputs:
    targetType: 'inline'
    script: |
      set -e

      # Check if required composer input variables are defined
      REQUIRED_VARS_MISSING=false

      if [ -z "$(environment)" ] || [ "$(environment)" == "\$(environment)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ -z "$(region)" ] || [ "$(region)" == "\$(region)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ -z "$(project)" ] || [ "$(project)" == "\$(project)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ "$REQUIRED_VARS_MISSING" = true ]; then
        echo "Skipping composer variables debug output..."
        exit 0
      fi

      echo "========================================="
      echo "Composer Variables - Debug Output"
      echo "========================================="
      echo ""
      echo "--- All Composer Variables ---"
      echo "  aks_subnet_name:                    $(aks_subnet_name)"
      echo "  appCode:                            $(appCode)"
      echo "  appgw_private_ip:                   $(appgw_private_ip)"
      echo "  appgw_subnet_name:                  $(appgw_subnet_name)"
      echo "  armServiceConnectionName:           $(armServiceConnectionName)"
      echo "  cloud:                              $(cloud)"
      echo "  compute_subnet_name:                $(compute_subnet_name)"
      echo "  environment:                        $(environment)"
      echo "  ingress_nginx_ip:                   $(ingress_nginx_ip)"
      echo "  key_vault_name:                     $(key_vault_name)"
      echo "  key_vault_resource_group_name:      $(key_vault_resource_group_name)"
      echo "  keyVaultCommaSeparatedSecretNames:  $(keyVaultCommaSeparatedSecretNames)"
      echo "  keyVaultServiceConnectionName:      $(keyVaultServiceConnectionName)"
      echo "  private_endpoint_subnet_name:       $(private_endpoint_subnet_name)"
      echo "  project:                            $(project)"
      echo "  region:                             $(region)"
      echo "  region_full:                        $(region_full)"
      echo "  regionSuffix:                       $(regionSuffix)"
      echo "  resourceSuffix:                     $(resourceSuffix)"
      echo "  route_table_name:                   $(route_table_name)"
      echo "  route_table_resource_group_name:    $(route_table_resource_group_name)"
      echo "  storageAccountContainerName:        $(storageAccountContainerName)"
      echo "  storageAccountName:                 $(storageAccountName)"
      echo "  storageAccountResourceGroup:        $(storageAccountResourceGroup)"
      echo "  subnet_name:                        $(subnet_name)"
      echo "  subsidiary:                         $(subsidiary)"
      echo "  target:                             $(target)"
      echo "  terraformVersion:                   $(terraformVersion)"
      echo "  virtual_network_name:               $(virtual_network_name)"
      echo "  virtual_network_resource_group_name: $(virtual_network_resource_group_name)"
      echo ""
      echo "========================================="

