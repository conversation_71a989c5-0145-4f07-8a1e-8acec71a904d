parameters:
  # Common
  - name: taskcondition
    default: succeeded()

  # AWS Auth
  - name: awsServiceConnectionName
  - name: awsRegion

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformLogLevel
    default: 'INFO'

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForApply
  - name: terraformCLIOptionsForApply

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

### Apply steps
steps:
  # Download terraform plan
  - task: DownloadPipelineArtifact@2
    displayName: "Download Terraform plan pipeline artifact"
    condition: ${{ parameters.taskcondition }}
    inputs:
      download: current
      artifact: ${{ parameters.artifactname }}
      path: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"

  # Terraform apply
  - task: AWSShellScript@1
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform apply plan
    ${{ else }}:
      displayName: Terraform destroy
    condition: ${{ parameters.taskcondition }}
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    inputs:
      awsCredentials: ${{ parameters.awsServiceConnectionName }}
      regionName: ${{ parameters.awsRegion }}
      scriptType: inline
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      disableAutoCwd: true
      inlineScript: |
        set -eu  # fail on error
        export TF_LOG=${{ parameters.terraformLogLevel}}
        trap "echo Error on line $LINENO" ERR

        echo "Show terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} show ${{ parameters.planFilePath }}

        echo "Applying terraform plan"
        terraform ${{ parameters.terraformCLIGlobalOptionsForApply }} apply ${{ parameters.terraformCLIOptionsForApply }} ${{ parameters.planFilePath }}

        echo "Finished applying terraform plan"
