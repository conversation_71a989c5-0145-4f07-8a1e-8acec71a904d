parameters:
  - name: armServiceConnection

steps:
  - task: AzureCLI@2
    displayName: Login using a Federated Service Connection
    condition: always()
    inputs:
      azureSubscription: ${{ parameters.armServiceConnection }}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        set -eu  # fail on error
        subscriptionId=$(az account show --query id -o tsv)
        echo "##vso[task.setvariable variable=ARM_CLIENT_ID]$servicePrincipalId"
        echo "##vso[task.setvariable variable=ARM_OIDC_TOKEN]$idToken"
        echo "##vso[task.setvariable variable=ARM_SUBSCRIPTION_ID]$subscriptionId"
        echo "##vso[task.setvariable variable=ARM_TENANT_ID]$tenantId"
        echo "##vso[task.setvariable variable=ARM_TYPE]workloadidentity"
        echo -n $idToken > $(Build.SourcesDirectory)/oidc-token
        echo "##vso[task.setvariable variable=ARM_OIDC_TOKEN_FILE_PATH]$(Build.SourcesDirectory)/oidc-token"

      addSpnToEnvironment: true