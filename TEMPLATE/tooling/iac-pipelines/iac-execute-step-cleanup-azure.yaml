parameters:
  # Terraform configuration
  - name: terraformProjectLocation

  - name: storageAccountName
  - name: storageAccountContainerName
  - name: terraformStateFilePath
  - name: sasTokenLifetimeMinutes
    default: 540
  # Auth
  - name: armServiceConnectionName    

steps:
  - task: DownloadPipelineArtifact@2
    displayName: "Download Terraform plan pipeline artifact"
    inputs:
      download: current
      artifact: "tfplan"
      path: "$(Build.SourcesDirectory)/${{ parameters.terraformProjectLocation }}"  

### Cleanup steps
  # Terraform delete resource group
  - task: AzureCLI@2
    displayName: Cleanup environment
    condition: or(failed(), canceled())
    env:
      ARM_SAS_TOKEN: $(ARM_SAS_TOKEN)
      ARM_USE_CLI: true
      AZURE_STORAGE_SAS_TOKEN: $(ARM_SAS_TOKEN)
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    inputs:
      azureSubscription: ${{ parameters.armServiceConnectionName }}
      addSpnToEnvironment: true
      scriptType: bash    
      workingDirectory: "$(Build.SourcesDirectory)/${{ parameters.terraformProjectLocation }}"
      scriptLocation: inlineScript
      inlineScript: |
        FILE=rgname.txt
        while IFS= read -r RG_NAME
        do
          echo "Checking if resource group $RG_NAME exists..."
          az group show --name $RG_NAME > rg_exist.out 2>&1 || true
          RG_NOT_EXIST=`grep -c ResourceGroupNotFound rg_exist.out || true`
          if [ ${RG_NOT_EXIST} -ne 0 ]
          then
            echo "Cleanup is not required. Resource group does not exist."
          else
            echo "Resource group exists. Attempting to delete resource group: ${RG_NAME}"
            az group delete --name ${RG_NAME} --yes
            if [ $? -eq 0 ]
            then
              echo "SUCCESS: Resource group has been deleted successfully."
              echo " "
            else
              echo "WARNING: Resource group deletion failed!"
              echo " "
            fi
          fi
        done < "$FILE"

        # Remove lock file
        echo "Trying to release lock from state file..."
        az storage blob lease break \
        --account-name ${{ parameters.storageAccountName }} \
        --container-name ${{ parameters.storageAccountContainerName }} \
        --blob-name ${{ parameters.terraformStateFilePath }} \
        -o tsv | true

