parameters:
  # AWS Auth
  - name: awsServiceConnectionName
  - name: awsRegion

  # AWS Backend
  - name: awsS3BucketName
  - name: awsS3BucketRegion
  - name: awsS3BucketKmsKeyId

  # GIT Prepare
  - name: tfModulesAZDOOrg
    default: 'ADOS-OTPHU-01'
  
  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformStateFilePath
  - name: terraformVersion
  - name: terraformExtraNoProxy
  - name: terraformRCFileForNetworkMirror
  - name: terraformUnlockStateLockID
    default: ''

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForInit
  - name: terraformCLIOptionsForInit

  # Artifact options
  - name: publishArtifact
    default: false

### Init steps
steps:

  # # GIT insteadOf configuration
  # # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/step-tf-git-prepare.yaml
  # - template: iac-common/step-tf-git-prepare.yaml@pipelinetemplates
  #   parameters:
  #     terraformModulesAccessPAT: $(TERRAFORM_MODULES_ACCESS_PAT)

  - template: ../common/step-git-prepare.yaml
    parameters:
      tfModulesAZDOOrg: ${{ parameters.tfModulesAZDOOrg }}

  # Checkouts
  - checkout: self
    path: "source_repo"
  - checkout: tooling
    path: "tooling_repo"

  # Download lock artifact for deploy stage (publishArtifact=false)
  - ${{ if eq(parameters.publishArtifact, false) }}:
    - task: DownloadPipelineArtifact@2
      displayName: Download terraform lock file
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}
        artifact: tflock

  # Copy .terraformrc to user home
  - template: iac-generic-step-terraformrc.yaml
    parameters:
      terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
      terraformVersion: ${{ parameters.terraformVersion }}

  # Terraform Init
  - task: AWSShellScript@1
    displayName: Terraform init
    name: prepare
    inputs:
      awsCredentials: ${{ parameters.awsServiceConnectionName }}
      regionName: ${{ parameters.awsRegion }}
      scriptType: inline
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      disableAutoCwd: true
      inlineScript: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR

        export TERRAFORMVERSION=${{ parameters.terraformVersion }}
        export TERRAFORM_VERSION=${{ parameters.terraformVersion }}
        NO_PROXY_TEMP=${NO_PROXY:+$NO_PROXY,}${{ parameters.terraformExtraNoProxy }}
        export NO_PROXY=$NO_PROXY_TEMP
        export no_proxy=$NO_PROXY
        echo "All environment variables:"
        env | sort
        echo "##vso[task.setvariable variable=NO_PROXY]${NO_PROXY}"
        echo "##vso[task.setvariable variable=no_proxy]${no_proxy}"

        echo "Initializing terraform"
        BACKEND_CONFIGS="-backend-config=bucket=${{ parameters.awsS3BucketName }} -backend-config=key=${{ parameters.terraformStateFilePath }} -backend-config=region=${{ parameters.awsS3BucketRegion }} -backend-config=kms_key_id=${{ parameters.awsS3BucketKmsKeyId }} -backend-config="use_lockfile=true""
        
        terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} init $BACKEND_CONFIGS ${{ parameters.terraformCLIOptionsForInit }}

  # Unlock statefile
  - ${{ if ne(parameters.terraformUnlockStateLockID, '') }}:
    - task: AWSShellScript@1
      displayName: Terraform unlocking locked state
      inputs:
        awsCredentials: ${{ parameters.awsServiceConnectionName }}
        regionName: ${{ parameters.awsRegion }}
        scriptType: inline
        workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
        inlineScript: |
          set -eu  # fail on error
          trap "echo Error on line $LINENO" ERR

          echo "Unlocking locked state file with ID: ${{ parameters.terraformUnlockStateLockID }}"
          terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} force-unlock -force ${{ parameters.terraformUnlockStateLockID }}
          echo "Finished unlocking locked state file"

  # Publish lock artifact for deploy stage (publishArtifact=true)
  - ${{ if eq(parameters.publishArtifact, true) }}:
    - task: PublishPipelineArtifact@1
      displayName: Publish terraform lock file
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}/.terraform.lock.hcl
        artifact: tflock
