# Pipelinetemplates v7 → v8 Migration

## Overview

This document describes the required changes when migrating from pipelinetemplates v7 to v8. The main feature of v8 is the modified **config repository handling**, which affects the checkout process and pipeline parameters.

## Summary of Key Changes

### 1. Config Repository Reference Handling (v8 Feature)

In v8, the `pipeline-generic.yaml` expects new parameters:
- `configRepoReference`: Specifies which repository to use as config
- `setVersionNumberToGitTagForRelease`: Git tag-based version control setting

### 2. Checkout Behavior Changes

**In v7:**
- The `self` repository is automatically checked out to the `s` path
- Explicit `checkout: self` is required in the init step

**In v8:**
- The `pipeline-generic.yaml` handles the `self` checkout based on `configRepoReference`
- **NO** explicit `checkout: self` needed in the init step (filtered out by v8 condition)

### 3. Checkov Scan Handling

**In v7:**
- The `skipCheckovScan` parameter is passed directly to pipeline-generic

**In v8:**
- Checkov scan handling has changed in pipeline-generic
- The `skipCheckovScan` parameter is **NOT** passed in v8

## Caller-Side Requirements

### 1. Add Pipeline Parameters

In the calling pipeline (e.g., `execute-tf.yaml`), add the following parameter:

```yaml
parameters:
  - name: pipelinetemplatesTag
    type: string
    default: v8  # or v7 if not yet migrated
    values:
      - v7
      - v8
```

### 2. Update Resources Repository Reference

Make the `pipelinetemplates` repository reference dynamic:

```yaml
resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: v6
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/${{ parameters.pipelinetemplatesTag }}
      endpoint: devopsinfra
```

### 3. Update Tooling Template Call

Pass the parameter when calling the `tooling/iac-pipelines/iac-execute.yaml` template:

```yaml
extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    # ... other parameters ...
    pipelinetemplatesTag: ${{ parameters.pipelinetemplatesTag }}
```

### 4. Optional: configRepoReference Parameter

If you want to use a repository other than `self` as config in v8:

```yaml
extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    # ... other parameters ...
    pipelinetemplatesTag: ${{ parameters.pipelinetemplatesTag }}
    configRepoReference: my-config-repo  # default: self
```

## Tooling Pipeline Changes (Already Implemented)

The `tooling/iac-pipelines/iac-execute.yaml` already contains the necessary logic:

### 1. New Parameters

```yaml
parameters:
  - name: pipelinetemplatesTag
    type: string
    default: 'v7'
    displayName: 'Pipelinetemplates repository reference'
  - name: configRepoReference
    default: self
```

### 2. Conditional Parameter Passing to pipeline-generic

```yaml
extends:
  template: iac-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    # ... common parameters ...
    ${{ if eq(parameters.pipelinetemplatesTag, 'v8') }}:
      configRepoReference: ${{ parameters.configRepoReference }}
      setVersionNumberToGitTagForRelease: false
```

### 3. Conditional Checkout in Init Step

In the `tooling/iac-pipelines/iac-execute-step-init-azure.yaml` file:

```yaml
# Checkouts
- ${{ if eq(parameters.pipelinetemplatesTag, 'v7') }}:
  - checkout: self
    path: "s"
- checkout: tooling
  path: "tooling_repo"
```

### 4. Conditional Checkov Scan Parameter

```yaml
${{ if eq(parameters.pipelinetemplatesTag, 'v7') }}:
  skipCheckovScan: ${{ parameters.skipCheckovScan }}
```

## Example: terraform-azurerm-acr Migration

The ACR repository is already set to v8. Key changes:

### execute-tf.yaml Modifications

```yaml
# Add parameter
parameters:
  - name: pipelinetemplatesTag
    type: string
    default: v8
    values:
      - v7
      - v8

# Update resources
resources:
  repositories:
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/${{ parameters.pipelinetemplatesTag }}
      endpoint: devopsinfra

# Update template call
extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    # ... other parameters ...
    pipelinetemplatesTag: ${{ parameters.pipelinetemplatesTag }}
```

## Migration Steps (Checklist)

- [ ] **1. Add Parameter**: Add `pipelinetemplatesTag` parameter to the pipeline
- [ ] **2. Update Resources**: Make `pipelinetemplates` repository ref dynamic
- [ ] **3. Template Call**: Pass `pipelinetemplatesTag` parameter to tooling template
- [ ] **4. Test with v7**: Run pipeline with `pipelinetemplatesTag: v7`
- [ ] **5. Test with v8**: Run pipeline with `pipelinetemplatesTag: v8`
- [ ] **6. Update Default**: If everything works, change default value to v8
- [ ] **7. Documentation**: Update pipeline documentation

## Rollback to v7

If issues arise with v8, simply revert the parameter:

```yaml
parameters:
  - name: pipelinetemplatesTag
    type: string
    default: v7  # rollback to v7
    values:
      - v7
      - v8
```
