parameters:
- name: tfModulesAZDOOrg
  type: string
  default: ADOS-OTPHU-01

steps:
  - task: Bash@3
    displayName: Prepare git config for ${{ parameters.tfModulesAZDOOrg }}  
    inputs:  
      targetType: 'inline'  
      script: |  
        set -eu

        BASE_URL="https://${{ parameters.tfModulesAZDOOrg }}@dev.azure.com/${{ parameters.tfModulesAZDOOrg }}/"
    
        echo "Setting git config for $BASE_URL"
        git config --global url."https://${{ parameters.tfModulesAZDOOrg }}:$(TERRAFORM_MODULES_ACCESS_PAT)@dev.azure.com/${{ parameters.tfModulesAZDOOrg }}/".insteadOf "$BASE_URL"

        echo "===== git config ====="
        cat ~/.gitconfig