# Composer System Documentation

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [How It Works](#how-it-works)
- [Directory Structure](#directory-structure)
- [Adding New Variables](#adding-new-variables)
- [Migration from Manual Configuration](#migration-from-manual-configuration)
- [Naming Pattern Catalog](#naming-pattern-catalog)

---

## Overview

The **Composer System** is a hierarchical, template-based configuration management system for Azure DevOps pipelines and Terraform deployments. It eliminates the need for manual, environment-specific configuration files by automatically computing variables based on conventions and overrides.

> **Note**: This documentation describes the **Azure-specific** composer system located in `tooling/env/azure/`. Similar systems for GCP and AWS will be created in separate directories (`tooling/env/gcp/`, `tooling/env/aws/`) in the future.

### Key Benefits
- **DRY Principle**: Define conventions once, override only when necessary
- **Consistency**: Enforces naming standards across all environments
- **Maintainability**: Centralized configuration reduces duplication
- **Scalability**: Easy to add new environments, regions, or projects
- **Type Safety**: Azure DevOps validates variable types at pipeline runtime

### Core Concept
Instead of maintaining separate configuration files for each environment-region-project combination (e.g., `DEV-gwc-iac.yaml`, `TST-weu-iac.yaml`), the composer system:
1. Defines **default conventions** (standard naming patterns)
2. Applies **hierarchical overrides** (environment → region → project → combined)
3. **Computes final variables** from patterns and parameters

---

## Architecture

### Components

```
tooling/env/
├── azure/                     # Azure-specific composer system
│   ├── composer.yaml          # Main orchestration file
│   ├── defaults.yaml          # Default conventions
│   └── overrides/
│       ├── environment/       # Environment-specific overrides (dev.yaml, tst.yaml, prd.yaml)
│       ├── region/            # Region-specific overrides (weu.yaml, gwc.yaml)
│       ├── project/           # Project-specific overrides (iac.yaml, iac-dmz.yaml)
│       └── combined/          # Full combination overrides (env-region-project)
│                              # Examples: dev-weu-iac.yaml, tst-gwc-iac.yaml, prd-weu-iac-dmz.yaml
├── gcp/                       # (Future) GCP-specific composer system
├── aws/                       # (Future) AWS-specific composer system
└── [legacy files]             # Static YAML files (DEV-aws-iac.yaml, PRD-gcp-automation.yaml, etc.)
```

### Variable Types

1. **Parameters** (Input)
   - `environment`: DEV/TST/PRD
   - `region`: weu/gwc
   - `project`: iac/iac-dmz

2. **Fixed Variables** (Constants)
   - `cloud`: azure
   - `subsidiary`: otphq
   - `terraformVersion`: 1.13.4

3. **Computed Variables** (Derived)
   - `armServiceConnectionName`: Computed from `serviceConnectionPattern`
   - `storageAccountName`: `stac{region}{environment}{resourceSuffix}`
   - `storageAccountResourceGroup`: `rgrp-{region}-{environment}-{project}-01`
   - `subnet_name`: Computed from `subnetName` pattern

4. **Override Variables** (Environment/Region/Project-specific)
   - `resourceSuffix`: Different per environment (e.g., `ritm2480573`)
   - `regionSuffix`: Different per region (weu=01, gwc=02)
   - `subnetName`: Different per environment (DEV: `privateendpoints`, TST/PRD: `snet-{region}-pe01`)

---

## How It Works

### Execution Flow

The composer system processes variables in **4 steps**:

```yaml
# STEP 1: Parameter Mapping
- name: environment
  value: ${{ lower(parameters.environment) }}  # DEV → dev

# STEP 2: Load Defaults
- template: defaults.yaml

# STEP 3: Load Overrides (Hierarchical)
- template: overrides/environment/${{ lower(parameters.environment) }}.yaml
- template: overrides/region/${{ parameters.region }}.yaml
- template: overrides/project/${{ parameters.project }}.yaml
- template: overrides/combined/${{ lower(parameters.environment) }}-${{ parameters.region }}-${{ parameters.project }}.yaml

# STEP 4: Compute Final Variables
- name: storageAccountName
  value: stac${{ variables.region }}${{ variables.environment }}${{ variables.resourceSuffix }}
```

### Override Hierarchy

Later overrides take precedence over earlier ones:

```
defaults.yaml
  ↓ (overridden by)
environment/dev.yaml
  ↓ (overridden by)
region/gwc.yaml
  ↓ (overridden by)
project/iac.yaml
  ↓ (overridden by)
combined/dev-gwc-iac.yaml  ← HIGHEST PRIORITY
```

### Example: DEV-gwc-iac

For `environment=DEV`, `region=gwc`, `project=iac`:

1. **defaults.yaml**: `storageAccountContainerName: tfstate-terratest`
2. **environment/dev.yaml**: `storageAccountContainerName: tfstate` ✅ (overrides default)
3. **region/gwc.yaml**: `regionSuffix: "02"`
4. **project/iac.yaml**: `appCode: terratest`
5. **combined/dev-gwc-iac.yaml**: `resourceSuffix: ritm2480573`
6. **composer.yaml** computes:
   - `storageAccountName: stacgwcdevritm2480573`
   - `storageAccountResourceGroup: rgrp-gwc-dev-iac-01`

---

## Directory Structure

### defaults.yaml
Defines standard conventions.

```yaml
variables:
  # Fixed values
  - name: cloud
    value: azure

  - name: subsidiary
    value: otphq

  # Default variables
  - name: resourceSuffix
    value: ''  # Empty by default, set in combined overrides

  - name: storageAccountContainerName
    value: tfstate-terratest

  # Resource-specific variables
  - name: subnetName
    value: 'snet-{region}-pe01'  # Default pattern, overridden in DEV
```

### overrides/environment/dev.yaml
DEV-specific overrides.

```yaml
variables:
  - name: storageAccountContainerName
    value: tfstate  # DEV uses different container name
```

### overrides/region/weu.yaml
West Europe region-specific values.

```yaml
variables:
  - name: region_full
    value: westeurope
  
  - name: regionSuffix
    value: "01"  # Used in service connection naming
```

### overrides/project/iac.yaml
IAC project-specific values.

```yaml
variables:
  - name: appCode
    value: terratest  # IAC uses terratest instead of project name
```

### overrides/combined/dev-weu-iac.yaml
DEV+WEU+IAC specific values (highest priority).

```yaml
variables:
  - name: resourceSuffix
    value: ritm2480573  # Environment+project specific, same across all regions

  - name: serviceConnectionPattern
    value: "{ENV}-OTP-ADO-IaC-sub-{env}-{regionSuffix}-FED-OTPHU-COE-TEMPLATESPEC"
    # Environment+project specific, same across all regions

  - name: ingress_nginx_ip
    value: "***********"  # AKS Ingress NGINX IP address for DEV-WEU-IAC

  - name: subnetName
    value: "privateendpoints"  # DEV uses legacy naming convention
```

### composer.yaml
Main orchestration file that loads all overrides and computes final variables.

```yaml
parameters:
  - name: environment
  - name: project
  - name: region

variables:
  # STEP 1: Parameter mapping
  - name: environment
    value: ${{ lower(parameters.environment) }}
  
  # STEP 2: Load defaults
  - template: defaults.yaml
  
  # STEP 3: Load overrides
  - template: overrides/environment/${{ lower(parameters.environment) }}.yaml
  - template: overrides/region/${{ parameters.region }}.yaml
  - template: overrides/project/${{ parameters.project }}.yaml
  - template: overrides/combined/${{ lower(parameters.environment) }}-${{ parameters.region }}-${{ parameters.project }}.yaml
  
  # STEP 4: Compute final variables
  - name: storageAccountName
    value: stac${{ variables.region }}${{ variables.environment }}${{ variables.resourceSuffix }}

  - name: subnet_name
    value: ${{ replace(variables.subnetName, '{region}', variables.region) }}
```

---

## Adding New Variables

### Scenario 1: Add a New Fixed Variable

**Use case**: Add a new constant that applies to all environments.

**Location**: `defaults.yaml`

```yaml
variables:
  - name: myNewConstant
    value: some-fixed-value
    # Description of what this variable does
```

### Scenario 2: Add a New Computed Variable

**Use case**: Derive a value from existing variables.

**Location**: `composer.yaml` (STEP 4)

```yaml
variables:
  # ... existing variables ...
  
  - name: myComputedVariable
    value: prefix-${{ variables.region }}-${{ variables.environment }}-suffix
    # Description of the computed value
```

### Scenario 3: Add a Project-Specific Variable

**Use case**: A variable that differs per project.

**Location**: `overrides/project/{project}.yaml`

```yaml
# overrides/project/iac.yaml
variables:
  - name: myProjectVariable
    value: iac-specific-value
```

```yaml
# overrides/project/iac-dmz.yaml
variables:
  - name: myProjectVariable
    value: iac-dmz-specific-value
```

### Scenario 4: Add an Environment+Region+Project-Specific Variable

**Use case**: A variable that differs per environment, region, AND project combination.

**Location**: `overrides/combined/{env}-{region}-{project}.yaml`

```yaml
# overrides/combined/dev-weu-iac.yaml
variables:
  - name: mySpecificVariable
    value: dev-weu-iac-specific-value
```

```yaml
# overrides/combined/dev-gwc-iac.yaml
variables:
  - name: mySpecificVariable
    value: dev-gwc-iac-specific-value
```

```yaml
# overrides/combined/tst-weu-iac.yaml
variables:
  - name: mySpecificVariable
    value: tst-iac-specific-value
```

### Scenario 5: Add a Resource-Specific Variable

**Use case**: A variable used only by specific Azure resources (e.g., AKS, Application Gateway).

**Location**: `defaults.yaml` (with empty default) + `overrides/combined/*.yaml` (with actual values)

```yaml
# defaults.yaml
variables:
  - name: my_resource_specific_ip
    value: ''
    # Resource-specific IP address (set in combined overrides)
```

```yaml
# overrides/combined/dev-weu-iac.yaml
variables:
  - name: my_resource_specific_ip
    value: "************"  # WEU-specific IP for DEV-IAC

# overrides/combined/dev-gwc-iac.yaml
variables:
  - name: my_resource_specific_ip
    value: "************"  # GWC-specific IP for DEV-IAC
```

### Scenario 6: Region-Specific Values

**Use case**: A variable that differs per region but is the same across all environments and projects.

**Location**: `overrides/region/{region}.yaml`

**Example**: Region-specific suffixes or full names

```yaml
# overrides/region/weu.yaml
variables:
  - name: region_full
    value: westeurope

  - name: regionSuffix
    value: "01"

# overrides/region/gwc.yaml
variables:
  - name: region_full
    value: germanywestcentral

  - name: regionSuffix
    value: "02"
```

**When to use region overrides vs combined overrides?**
- Use **region overrides** if the value is the same for all environments and projects in that region
- Use **combined overrides** if the value differs per environment, region, or project combination

---

## Migration from Manual Configuration

### Starting Point

You have X manual configuration files (e.g., `DEV-iac.tfvars`, `TST-iac.tfvars`, `PRD-iac.tfvars`):

```hcl
# DEV-iac.tfvars
cloud       = "azure"
environment = "dev"
region      = "germanywestcentral"
project     = "iac"

vnet_name      = "vnet-gwc-dev-iac-01"
vnet_rgrp_name = "rgrp-gwc-dev-iac-01"
kv_name        = "kvau-gwc-dev-RITM2480573"
```

### Pattern Formation

Analyze what changes across different files:
- `cloud`, `environment`, `region`, `project` → always different → **placeholder**
- `vnet-gwc-dev-iac-01` → `vnet-{region}-{environment}-{project}-01` → **pattern**
- `RITM2480573` → environment+project specific → **override file**

### Template

Create the `terraform.tfvars.template` file:

```hcl
# terraform.tfvars.template
cloud       = "{{ CLOUD }}"
environment = "{{ ENVIRONMENT }}"
region      = "{{ REGION_FULL }}"
project     = "{{ PROJECT }}"

# ✅ RECOMMENDED: Use computed variables from composer.yaml
# These are centrally managed and ensure consistency across all projects
vnet_name      = "{{ VIRTUAL_NETWORK_NAME }}"
vnet_rgrp_name = "{{ VIRTUAL_NETWORK_RESOURCE_GROUP_NAME }}"
subnet_name    = "{{ PRIVATE_ENDPOINT_SUBNET_NAME }}"
kv_name        = "{{ KEY_VAULT_NAME }}"
kv_rgrp_name   = "{{ KEY_VAULT_RESOURCE_GROUP_NAME }}"

# Alternative (not recommended): Manual pattern construction
# Only use this if you need project-specific naming that differs from defaults
# vnet_name      = "vnet-{{ REGION }}-{{ ENVIRONMENT }}-{{ PROJECT }}-01"
# vnet_rgrp_name = "rgrp-{{ REGION }}-{{ ENVIRONMENT }}-{{ PROJECT }}-01"
# kv_name        = "kvau-{{ REGION }}-{{ ENVIRONMENT }}-{{ RESOURCE_SUFFIX }}"
# kv_rgrp_name   = "rgrp-{{ REGION }}-{{ ENVIRONMENT }}-{{ PROJECT }}-01"
```

**Why use computed variables?**
- ✅ **Consistency**: Naming patterns defined once in `defaults.yaml`
- ✅ **Maintainability**: Change patterns in one place, affects all projects
- ✅ **Override support**: Can be customized per environment/project if needed
- ✅ **Less error-prone**: No manual pattern construction in templates

Put unique values (e.g., `RITM2480573`) in override files:

```yaml
# tooling/env/azure/overrides/combined/dev-weu-iac.yaml
variables:
  - name: resourceSuffix
    value: ritm2480573
    # Environment+project specific, same across all regions
```

**Available placeholders**:
- `{{ AKS_SUBNET_NAME }}` - AKS cluster subnet name (computed)
- `{{ APP_CODE }}` - Application code
- `{{ APPGW_PRIVATE_IP }}` - Application Gateway private IP address
- `{{ APPGW_SUBNET_NAME }}` - Application Gateway subnet name (computed)
- `{{ ARM_SERVICE_CONNECTION_NAME }}` - Azure DevOps ARM service connection name
- `{{ CLOUD }}` - Cloud provider (azure)
- `{{ COMPUTE_SUBNET_NAME }}` - VM compute subnet name (computed)
- `{{ ENVIRONMENT }}` - Environment (dev/tst/prd)
- `{{ INGRESS_NGINX_IP }}` - AKS Ingress NGINX IP address
- `{{ KEY_VAULT_COMMA_SEPARATED_SECRET_NAMES }}` - Comma-separated Key Vault secret names
- `{{ KEY_VAULT_NAME }}` - Azure Key Vault name (computed)
- `{{ KEY_VAULT_RESOURCE_GROUP_NAME }}` - Key Vault resource group name (computed)
- `{{ KEY_VAULT_SERVICE_CONNECTION_NAME }}` - Azure DevOps Key Vault service connection name
- `{{ PRIVATE_ENDPOINT_SUBNET_NAME }}` - Private endpoint subnet name (computed)
- `{{ PROJECT }}` - Project name
- `{{ REGION }}` - Region short form (e.g., `gwc`, `weu`)
- `{{ REGION_FULL }}` - Region full name (e.g., `germanywestcentral`, `westeurope`)
- `{{ REGION_SHORT }}` - Alias for `{{ REGION }}`
- `{{ REGION_SUFFIX }}` - Region suffix (weu=01, gwc=02)
- `{{ RESOURCE_GROUP_NAME }}` - Storage account resource group name
- `{{ RESOURCE_SUFFIX }}` - Resource suffix (uppercase, e.g., RITM2480573)
- `{{ ROUTE_TABLE_NAME }}` - Azure route table name (computed)
- `{{ ROUTE_TABLE_RESOURCE_GROUP_NAME }}` - Route table resource group name (computed)
- `{{ STORAGE_ACCOUNT_CONTAINER_NAME }}` - Terraform state container name
- `{{ STORAGE_ACCOUNT_NAME }}` - Storage account name for Terraform state
- `{{ STORAGE_ACCOUNT_RESOURCE_GROUP }}` - Storage account resource group name
- `{{ PRIVATE_ENDPOINT_SUBNET_NAME }}` - Subnet name for private endpoints
- `{{ SUBSIDIARY }}` - Organization subsidiary (otphq)
- `{{ TARGET }}` - Target Azure subscription identifier (computed)
- `{{ TERRAFORM_VERSION }}` - Terraform version
- `{{ VIRTUAL_NETWORK_NAME }}` - Azure virtual network name (computed)
- `{{ VIRTUAL_NETWORK_RESOURCE_GROUP_NAME }}` - Virtual network resource group name (computed)

---

## Naming Pattern Catalog

This section documents all discovered naming patterns and their exceptions.

### Azure Resource Naming

#### Storage Account
**Pattern**: `stac{region}{environment}{resourceSuffix}`

**Examples**:
- DEV-gwc-iac: `stacgwcdevritm2480573`
- TST-weu-iac: `stacweutstritm1669979`
- PRD-weu-iac: `stacweuprdritm1670198`

**Notes**:
- No hyphens (storage account naming restriction)
- `resourceSuffix` is environment+project specific
- Defined in: `composer.yaml` (computed)

#### Resource Group
**Pattern**: `rgrp-{region}-{environment}-{project}-01`

**Examples**:
- DEV-gwc-iac: `rgrp-gwc-dev-iac-01`
- TST-weu-iac: `rgrp-weu-tst-iac-01`
- PRD-weu-iac: `rgrp-weu-prd-iac-01`

**Notes**:
- Suffix is **always** `-01` (NOT `regionSuffix`)
- Used for storage account resource group
- Defined in: `composer.yaml` (computed)

#### Key Vault
**Pattern**: `{project}-{environment}-shared01` (default) or `kvau-{region}-{environment}-{PROJECTSUFFIX}` (DEV override)

**Examples**:
- DEV-gwc-iac: `kvau-gwc-dev-RITM2480573`
- TST-weu-iac: `iac-tst-shared01`
- PRD-weu-iac: `iac-prd-shared01`

**Notes**:
- DEV uses different pattern with region and resource suffix
- TST/PRD use simplified pattern without region
- `resourceSuffix` is **uppercase** in DEV (converted during template generation)
- Defined in: `defaults.yaml` (pattern: `keyVaultNamePattern`) + `environment/dev.yaml` (DEV override)
- **Composer variable**: `key_vault_name` (computed)
- **Template placeholder**: `{{ KEY_VAULT_NAME }}`

#### Key Vault Resource Group
**Pattern**: `rgrp-{region}-{environment}-{project}-01`

**Examples**:
- DEV-gwc-iac: `rgrp-gwc-dev-iac-01`
- TST-weu-iac: `rgrp-weu-tst-iac-01`
- PRD-weu-iac: `rgrp-weu-prd-iac-01`

**Notes**:
- Same pattern as storage account resource group
- Defined in: `defaults.yaml` (pattern: `virtualNetworkResourceGroupNamePattern`)
- **Composer variable**: `key_vault_resource_group_name` (computed)
- **Template placeholder**: `{{ KEY_VAULT_RESOURCE_GROUP_NAME }}`

#### Virtual Network
**Pattern**: `vnet-{region}-{environment}-{project}-01`

**Examples**:
- DEV-gwc-iac: `vnet-gwc-dev-iac-01`
- TST-weu-iac: `vnet-weu-tst-iac-01`
- DEV-weu-demo01: `vnet-weu-dev-demo01-01`

**Notes**:
- Suffix is **always** `-01` (NOT `regionSuffix`)
- Defined in: `defaults.yaml` (pattern: `virtualNetworkNamePattern`)
- **Composer variable**: `virtual_network_name` (computed)
- **Template placeholder**: `{{ VIRTUAL_NETWORK_NAME }}`

#### Virtual Network Resource Group
**Pattern**: `rgrp-{region}-{environment}-{project}-01`

**Examples**:
- DEV-gwc-iac: `rgrp-gwc-dev-iac-01`
- TST-weu-iac: `rgrp-weu-tst-iac-01`
- PRD-weu-iac: `rgrp-weu-prd-iac-01`

**Notes**:
- Same pattern as storage account resource group
- Defined in: `defaults.yaml` (pattern: `virtualNetworkResourceGroupNamePattern`)
- **Composer variable**: `virtual_network_resource_group_name` (computed)
- **Template placeholder**: `{{ VIRTUAL_NETWORK_RESOURCE_GROUP_NAME }}`

#### Route Table
**Pattern**: `rtbl-{region}-{environment}-{project}-core`

**Examples**:
- DEV-gwc-iac: `rtbl-gwc-dev-iac-core`
- TST-weu-iac: `rtbl-weu-tst-iac-core`
- PRD-weu-iac: `rtbl-weu-prd-iac-core`

**Notes**:
- Suffix is **always** `-core`
- Defined in: `defaults.yaml` (pattern: `routeTableNamePattern`)
- **Composer variable**: `route_table_name` (computed)
- **Template placeholder**: `{{ ROUTE_TABLE_NAME }}`

#### Route Table Resource Group
**Pattern**: `rgrp-{region}-{environment}-{project}-01`

**Examples**:
- DEV-gwc-iac: `rgrp-gwc-dev-iac-01`
- TST-weu-iac: `rgrp-weu-tst-iac-01`
- PRD-weu-iac: `rgrp-weu-prd-iac-01`

**Notes**:
- Same pattern as other resource groups
- Defined in: `defaults.yaml` (pattern: `virtualNetworkResourceGroupNamePattern`)
- **Composer variable**: `route_table_resource_group_name` (computed)
- **Template placeholder**: `{{ ROUTE_TABLE_RESOURCE_GROUP_NAME }}`

#### Subnets

##### Private Endpoint Subnet
**Pattern**: `snet-{region}-pe01` (default) or `privateendpoints` (DEV override)

**Examples**:
- Private Endpoints (TST/PRD): `snet-weu-pe01`
- Private Endpoints (DEV): `privateendpoints` ⚠️ **EXCEPTION**

**Notes**:
- DEV uses legacy naming `privateendpoints`
- TST/PRD use standard pattern `snet-{region}-pe01`
- Defined in: `defaults.yaml` (pattern: `privateEndpointSubnetNamePattern`) + `environment/dev.yaml` (DEV override)
- **Composer variable**: `private_endpoint_subnet_name` (computed)
- **Template placeholder**: `{{ PRIVATE_ENDPOINT_SUBNET_NAME }}`

##### AKS Subnet
**Pattern**: `snet-{region}-aks01`

**Examples**:
- AKS subnet: `snet-weu-aks01`
- AKS subnet: `snet-gwc-aks01`

**Notes**:
- Standard pattern for AKS cluster subnets
- Defined in: `defaults.yaml` (pattern: `aksSubnetNamePattern`)
- **Composer variable**: `aks_subnet_name` (computed)
- **Template placeholder**: `{{ AKS_SUBNET_NAME }}`

##### Compute Subnet
**Pattern**: `snet-{region}-compute01`

**Examples**:
- Compute subnet: `snet-weu-compute01`
- Compute subnet: `snet-gwc-compute01`

**Notes**:
- Standard pattern for VM compute subnets
- Defined in: `defaults.yaml` (pattern: `computeSubnetNamePattern`)
- **Composer variable**: `compute_subnet_name` (computed)
- **Template placeholder**: `{{ COMPUTE_SUBNET_NAME }}`

##### Application Gateway Subnet
**Pattern**: `snet-{region}-compute04`

**Examples**:
- AppGW subnet: `snet-weu-compute04`
- AppGW subnet: `snet-gwc-compute04`

**Notes**:
- Uses compute04 suffix for Application Gateway
- Defined in: `defaults.yaml` (pattern: `appgwSubnetNamePattern`)
- **Composer variable**: `appgw_subnet_name` (computed)
- **Template placeholder**: `{{ APPGW_SUBNET_NAME }}`

### Azure DevOps Naming

#### Service Connection (ARM)
**Pattern**: `{ENV}-OTP-ADO-IaC-sub-{env}-{regionSuffix}-OTPHU-COE-TEMPLATESPEC-FED`

**Examples**:
- DEV-gwc-iac: `DEV-OTP-ADO-IaC-sub-dev-02-FED-OTPHU-COE-TEMPLATESPEC`
- TST-weu-iac: `TST-OTP-ADO-IaC-sub-tst-01-OTPHU-COE-TEMPLATESPEC-FED`
- PRD-weu-iac: `PRD-OTP-ADO-IaC-sub-prd-01-OTPHU-COE-TEMPLATESPEC-FED`

**Notes**:
- `{ENV}` = uppercase environment (DEV/TST/PRD)
- `{env}` = lowercase environment (dev/tst/prd)
- `{regionSuffix}` = region-specific suffix (weu=01, gwc=02)
- DEV-iac has different word order: `-FED-OTPHU-` vs `-OTPHU-...-FED`
- Defined in: `defaults.yaml` (pattern: `serviceConnectionPattern`) + `combined/dev-{region}-iac.yaml` (override)
- **Composer variable**: `armServiceConnectionName` (computed)
- **Template placeholder**: `{{ ARM_SERVICE_CONNECTION_NAME }}`

#### Service Connection (Key Vault)
**Pattern**: `{ENV}-OTP-ADO-IaC-sub-{env}-{regionSuffix}-OTPHU-COE-TEMPLATESPEC-FED`

**Examples**:
- DEV-gwc-iac: `DEV-OTP-ADO-IaC-sub-dev-02-FED-OTPHU-COE-TEMPLATESPEC`
- TST-weu-iac: `TST-OTP-ADO-IaC-sub-tst-01-OTPHU-COE-TEMPLATESPEC-FED`
- PRD-weu-iac: `PRD-OTP-ADO-IaC-sub-prd-01-OTPHU-COE-TEMPLATESPEC-FED`

**Notes**:
- Same pattern as ARM service connection
- Defined in: `defaults.yaml` (pattern: `serviceConnectionPattern`)
- **Composer variable**: `keyVaultServiceConnectionName` (computed)
- **Template placeholder**: `{{ KEY_VAULT_SERVICE_CONNECTION_NAME }}`

#### Target Subscription
**Pattern**: `OTP-ADO-IaC-sub-{env}-{regionSuffix}` (default) or project-specific overrides

**Examples**:
- iac project: `OTP-ADO-IaC-sub-dev-01`
- iac-dmz project: `OTP-ADO-IaC-DMZ-sub-dev-01`

**Notes**:
- Used to identify the target Azure subscription
- Can be overridden per project (e.g., iac-dmz adds `-DMZ-` infix)
- Defined in: `composer.yaml` (default) + `overrides/project/*.yaml` (project-specific patterns)
- **Composer variable**: `target` (computed)
- **Template placeholder**: `{{ TARGET }}`

#### Storage Container
**Pattern**: Environment-dependent

**Values**:
- DEV: `tfstate`
- TST, PRD: `tfstate-terratest`

**Notes**:
- DEV uses different container name
- Defined in: `defaults.yaml` (default: `tfstate-terratest`) + `environment/dev.yaml` (DEV override: `tfstate`)
- **Composer variable**: `storageAccountContainerName` (computed)
- **Template placeholder**: `{{ STORAGE_ACCOUNT_CONTAINER_NAME }}`

### Region-Specific Values

#### Region Full Name
**Mapping**:
- `weu` → `westeurope`
- `gwc` → `germanywestcentral`

**Defined in**: `overrides/region/{region}.yaml`

#### Region Suffix
**Mapping**:
- `weu` → `01`
- `gwc` → `02`

**Usage**: Only in service connection naming, **NOT** in resource naming

**Defined in**: `overrides/region/{region}.yaml`

### Project-Specific Values

#### App Code
**Mapping**:
- `iac` → `terratest`
- `iac-dmz` → `terratest`
- Other projects → `{project}` (default)

**Defined in**: `defaults.yaml` (default) + `overrides/project/*.yaml` (overrides)

#### Resource Suffix
**Mapping** (environment+project specific):
- DEV-iac: `ritm2480573`
- TST-iac: `ritm1669979`
- PRD-iac: `ritm1670198`
- iac-dmz: `ritm2502944`

**Defined in**: `overrides/combined/{env}-{project}.yaml` or `overrides/project/{project}.yaml`

### Resource-Specific Variables

#### AKS Ingress NGINX IP
**Pattern**: Environment+project specific

**Examples**:
- DEV-iac: `***********`
- TST-iac: `***********`
- PRD-iac: `***********`

**Defined in**: `overrides/combined/{env}-{region}-iac.yaml`

#### Application Gateway Private IP
**Pattern**: Environment+region+project specific

**Examples**:
- TST-WEU-iac: `************`
- PRD-WEU-iac: `************`
- TST-GWC-iac: TODO
- PRD-GWC-iac: TODO

**Defined in**: `overrides/combined/{env}-{region}-iac.yaml`

#### Subnet Name for Private Endpoints
**Pattern**: `snet-{region}-pe01` (default), overridden in DEV

**Examples**:
- DEV-iac: `privateendpoints` (override)
- TST-iac: `snet-weu-pe01` (default pattern)
- PRD-iac: `snet-weu-pe01` (default pattern)

**Notes**:
- DEV environment uses legacy naming: `privateendpoints`
- TST/PRD use standard pattern: `snet-{region}-pe01`
- Defined in: `defaults.yaml` (pattern) + `combined/dev-{region}-iac.yaml` (DEV override)

---

## Best Practices

1. **Keep defaults.yaml minimal**: Only include standard values that apply to most environments
2. **Use overrides sparingly**: Only override when necessary (environment-specific, project-specific values)
3. **Document exceptions**: Add comments explaining why an override exists
4. **Prefer computed variables**: Use patterns instead of hardcoded values
5. **Test thoroughly**: Validate generated variables match expected values before deleting manual configs
6. **Use descriptive comments**: Explain what each variable is used for

---

## Troubleshooting

### Variable Not Found
**Problem**: Pipeline fails with "variable not found" error

**Solution**: Check the override hierarchy - ensure the variable is defined in one of:
1. `defaults.yaml`
2. `overrides/environment/{env}.yaml`
3. `overrides/region/{region}.yaml`
4. `overrides/project/{project}.yaml`
5. `overrides/combined/{env}-{region}-{project}.yaml`
6. `composer.yaml` (computed variables)

### Wrong Value Generated
**Problem**: Variable has unexpected value

**Solution**: Check override precedence - later overrides take priority:
```
defaults.yaml < environment < region < project < combined
```

### Template Not Found
**Problem**: Pipeline fails with "template not found" error

**Solution**: Ensure all referenced override files exist:
```bash
ls -la tooling/env/azure/overrides/environment/
ls -la tooling/env/azure/overrides/region/
ls -la tooling/env/azure/overrides/project/
ls -la tooling/env/azure/overrides/combined/
```

---

## References

- Azure DevOps Template Expressions: https://learn.microsoft.com/en-us/azure/devops/pipelines/process/templates
- Azure Naming Conventions: https://learn.microsoft.com/en-us/azure/cloud-adoption-framework/ready/azure-best-practices/resource-naming
- Terraform Backend Configuration: https://developer.hashicorp.com/terraform/language/settings/backends/azurerm

