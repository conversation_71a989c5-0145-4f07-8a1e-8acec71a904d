# ============================================================================
# NAMING CONVENTIONS
# ============================================================================
# camelCase variables: Used in Azure DevOps pipelines
# snake_case variables: Used in Terraform configurations

parameters:
  - name: environment
    displayName: 'Environment (DEV/TST/PRD)'
  - name: project
    displayName: 'Project (iac/demo01/iac-dmz)'
  - name: region
    displayName: 'Region (weu/gwc)'

variables:
  # ============================================================================
  # STEP 1: Parameter mapping
  # ============================================================================
  - name: environment
    value: ${{ lower(parameters.environment) }}

  - name: project
    value: ${{ parameters.project }}

  - name: region
    value: ${{ parameters.region }}

  # ============================================================================
  # STEP 2: Load defaults (conventions based on TST/PRD standards)
  # ============================================================================
  - template: defaults.yaml

  # ============================================================================
  # STEP 3: Load overrides in hierarchical order
  # ============================================================================

  # 3a. Environment-specific overrides
  - template: overrides/environment/${{ lower(parameters.environment) }}.yaml

  # 3b. Region-specific overrides
  - template: overrides/region/${{ parameters.region }}.yaml

  # 3c. Project-specific overrides
  - template: overrides/project/${{ parameters.project }}.yaml

  # 3d. Combined overrides (environment + region + project specific combinations)
  - template: overrides/combined/${{ lower(parameters.environment) }}-${{ parameters.region }}-${{ parameters.project }}.yaml

  # ============================================================================
  # STEP 4: Compute final variables from patterns
  # ============================================================================

  # Base computed variables (other variables depend on these)
  # ----------------------------------------------------------------------------
  - name: armServiceConnectionName
    value: ${{ replace(replace(replace(variables.serviceConnectionPattern, '{ENV}', upper(variables.environment)), '{env}', variables.environment), '{subscriptionSuffix}', variables.subscriptionSuffix) }}
    # Azure DevOps service connection name for ARM operations

  - name: virtual_network_resource_group_name
    value: ${{ replace(replace(replace(variables.virtualNetworkResourceGroupNamePattern, '{region}', variables.region), '{env}', variables.environment), '{project}', variables.project) }}
    # Resource group name for virtual network resources

  # Derived variables (computed from base variables and patterns)
  # ----------------------------------------------------------------------------
  - name: key_vault_name
    value: ${{ replace(replace(replace(replace(variables.keyVaultNamePattern, '{project}', variables.project), '{env}', variables.environment), '{region}', variables.region), '{PROJECTSUFFIX}', upper(variables.resourceSuffix)) }}
    # Azure Key Vault name

  - name: key_vault_resource_group_name
    value: ${{ replace(replace(replace(variables.keyVaultResourceGroupNamePattern, '{region}', variables.region), '{env}', variables.environment), '{project}', variables.project) }}
    # Resource group name for Key Vault (computed from pattern)

  - name: keyVaultServiceConnectionName
    value: ${{ variables.armServiceConnectionName }}
    # Azure DevOps service connection name for Key Vault operations

  - name: route_table_name
    value: ${{ replace(replace(replace(variables.routeTableNamePattern, '{region}', variables.region), '{env}', variables.environment), '{project}', variables.project) }}
    # Azure route table name

  - name: route_table_resource_group_name
    value: ${{ variables.virtual_network_resource_group_name }}
    # Resource group name for route table

  - name: storageAccountName
    value: ${{ replace(replace(replace(variables.storageAccountNamePattern, '{region}', variables.region), '{env}', variables.environment), '{resourceSuffix}', variables.resourceSuffix) }}
    # Azure storage account name for Terraform state (computed from pattern)

  - name: storageAccountResourceGroup
    value: ${{ replace(replace(replace(variables.storageAccountResourceGroupPattern, '{region}', variables.region), '{env}', variables.environment), '{project}', variables.project) }}
    # Resource group name for storage account (computed from pattern)

  # ============================================================================
  # Resource-specific computed variables
  # ============================================================================

  # --- Private Endpoint Subnet ---
  - name: private_endpoint_subnet_name
    value: ${{ replace(variables.privateEndpointSubnetNamePattern, '{region}', variables.region) }}
    # Private endpoint subnet name (computed from pattern)

  # --- AKS Subnet ---
  - name: aks_subnet_name
    value: ${{ replace(variables.aksSubnetNamePattern, '{region}', variables.region) }}
    # AKS cluster subnet name (computed from pattern)

  # --- Compute Subnet ---
  - name: compute_subnet_name
    value: ${{ replace(variables.computeSubnetNamePattern, '{region}', variables.region) }}
    # VM compute subnet name (computed from pattern)

  # --- Application Gateway Subnet ---
  - name: appgw_subnet_name
    value: ${{ replace(variables.appgwSubnetNamePattern, '{region}', variables.region) }}
    # Application Gateway subnet name (computed from pattern)

  - name: target
    value: ${{ replace(replace(variables.subscriptionPattern, '{env}', variables.environment), '{subscriptionSuffix}', variables.subscriptionSuffix) }}
    # Target Azure subscription identifier

  - name: virtual_network_name
    value: ${{ replace(replace(replace(variables.virtualNetworkNamePattern, '{region}', variables.region), '{env}', variables.environment), '{project}', variables.project) }}
    # Azure virtual network name

  - name: subnet_name
    value: ${{ variables.private_endpoint_subnet_name }}
    # Subnet name for private endpoints (alias for private_endpoint_subnet_name)

