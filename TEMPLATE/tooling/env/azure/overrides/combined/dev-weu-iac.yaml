# ============================================================================
# DEV + WEU + IAC Combined Overrides
# ============================================================================
# IAC project has environment-specific serviceConnectionPattern and resourceSuffix in DEV
# West Europe region specific values

variables:
  - name: resourceSuffix
    value: ritm2480573
    # Environment+project specific, same across all regions

  - name: serviceConnectionPattern
    value: "{ENV}-OTP-ADO-IaC-sub-{env}-{subscriptionSuffix}-FED-OTPHU-COE-TEMPLATESPEC"
    # Environment+project specific, same across all regions

  - name: subscriptionSuffix
    value: "02"
    # DEV-iac uses 02 instead of default 01

  - name: ingress_nginx_ip
    value: ''
    # TODO: Set WEU-specific AKS Ingress NGINX IP address for DEV-WEU-IAC

  # --- Application Gateway Resource-specific ---
  - name: appgw_private_ip
    value: ''
    # TODO: Application Gateway private IP address for DEV-WEU-IAC (if/when AppGW is deployed in DEV)

  # --- Private Endpoints ---
  # Note: privateEndpointSubnetNamePattern is overridden in environment/dev.yaml


