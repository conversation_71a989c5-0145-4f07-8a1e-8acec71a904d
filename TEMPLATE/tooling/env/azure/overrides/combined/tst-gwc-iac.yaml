# ============================================================================
# TST + GWC + IAC Combined Overrides
# ============================================================================
# Only resourceSuffix needs to be set, serviceConnectionPattern uses default
# Germany West Central region specific values

variables:
  - name: resourceSuffix
    value: ritm1669979
    # Environment+project specific, same across all regions

  # --- AKS Resource-specific ---
  - name: ingress_nginx_ip
    value: "***********"
    # AKS Ingress NGINX IP address for TST-GWC-IAC

  # --- Application Gateway Resource-specific ---
  - name: appgw_private_ip
    value: ''
    # TODO: Application Gateway private IP address for TST-GWC-IAC (if/when AppGW is deployed in GWC)
