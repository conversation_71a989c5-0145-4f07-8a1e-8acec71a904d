# ============================================================================
# DEV + GWC + IAC Combined Overrides
# ============================================================================
# IAC project has environment-specific serviceConnectionPattern and resourceSuffix in DEV
# Germany West Central region specific values

variables:
  - name: resourceSuffix
    value: ritm2480573
    # Environment+region+project specific for DEV-GWC-IAC

  - name: serviceConnectionPattern
    value: "{ENV}-OTP-ADO-IaC-sub-{env}-{subscriptionSuffix}-FED-OTPHU-COE-TEMPLATESPEC"
    # Environment+project specific, same across all regions

  - name: subscriptionSuffix
    value: "02"
    # DEV-iac uses 02 instead of default 01

  - name: ingress_nginx_ip
    value: ''
    # TODO: Set GWC-specific AKS Ingress NGINX IP address for DEV-GWC-IAC

  # --- Application Gateway Resource-specific ---
  - name: appgw_private_ip
    value: ''
    # TODO: Application Gateway private IP address for DEV-GWC-IAC (if/when AppGW is deployed in DEV)

  # --- Private Endpoints ---
  # Note: privateEndpointSubnetNamePattern is overridden in environment/dev.yaml

