# ============================================================================
# DEV + GWC + IAC-DMZ Combined Overrides
# ============================================================================
# IAC-DMZ project specific values for DEV environment in Germany West Central region

variables:
  # --- AKS Resource-specific ---
  - name: ingress_nginx_ip
    value: ''
    # TODO: Set GWC-specific AKS Ingress NGINX IP address for DEV-GWC-IAC-DMZ

  # --- Application Gateway Resource-specific ---
  - name: appgw_private_ip
    value: ''
    # TODO: Application Gateway private IP address for DEV-GWC-IAC-DMZ (if/when AppGW is deployed in DEV)

  # --- Private Endpoints ---
  - name: subnetName
    value: "privateendpoints"
    # TODO: Verify if DEV uses same legacy naming convention for IAC-DMZ in GWC region

