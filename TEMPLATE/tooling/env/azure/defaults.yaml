# ============================================================================
# DEFAULT CONVENTIONS
# ============================================================================
# Overrides are loaded from: environment/, region/, project/, combined/

variables:
  # ============================================================================
  # Fixed values
  # ============================================================================
  - name: cloud
    value: azure
    # Cloud provider platform

  - name: keyVaultCommaSeparatedSecretNames
    value: sbb-pat
    # Comma-separated list of secret names to retrieve from Key Vault

  - name: subsidiary
    value: otphq
    # Organization subsidiary identifier

  - name: terraformVersion
    value: 1.13.4
    # Terraform version used for infrastructure provisioning

  # ============================================================================
  # Default variables
  # ============================================================================
  - name: appCode
    value: ${{ parameters.project }}
    # Application code, defaults to project name

  - name: keyVaultNamePattern
    value: "{project}-{env}-shared01"
    # Naming pattern for Azure Key Vault resources

  - name: keyVaultResourceGroupNamePattern
    value: "rgrp-{region}-{env}-{project}-01"
    # Naming pattern for Key Vault resource group

  - name: resourceSuffix
    value: ''
    # Optional suffix for resources like storage accounts and key vaults

  - name: routeTableNamePattern
    value: "rtbl-{region}-{env}-{project}-core"
    # Naming pattern for Azure route table resources

  - name: serviceConnectionPattern
    value: "{ENV}-OTP-ADO-IaC-sub-{env}-{subscriptionSuffix}-OTPHU-COE-TEMPLATESPEC-FED"
    # Naming pattern for Azure DevOps service connections

  - name: storageAccountContainerName
    value: tfstate-terratest
    # Container name for Terraform state storage

  - name: storageAccountNamePattern
    value: "stac{region}{env}{resourceSuffix}"
    # Naming pattern for Azure storage account (used for Terraform state)

  - name: storageAccountResourceGroupPattern
    value: "rgrp-{region}-{env}-{project}-01"
    # Naming pattern for storage account resource group

  - name: subscriptionSuffix
    value: "01"
    # Subscription suffix used in service connection and subscription naming (default: 01, DEV-iac uses 02)

  - name: virtualNetworkNamePattern
    value: "vnet-{region}-{env}-{project}-01"
    # Naming pattern for Azure virtual network resources

  - name: virtualNetworkResourceGroupNamePattern
    value: "rgrp-{region}-{env}-{project}-01"
    # Naming pattern for virtual network resource group

  # ============================================================================
  # Resource-specific variables
  # ============================================================================
  # These variables are specific to certain Azure resources and may need
  # to be overridden based on the resource type being deployed

  # --- AKS (Azure Kubernetes Service) ---
  - name: aksSubnetNamePattern
    value: "snet-{region}-aks01"
    # Naming pattern for AKS cluster subnet

  - name: ingress_nginx_ip
    value: ''
    # AKS Ingress NGINX IP address (environment-specific, set in combined overrides)

  # --- Application Gateway ---
  - name: appgwSubnetNamePattern
    value: "snet-{region}-compute04"
    # Naming pattern for Application Gateway subnet

  - name: appgw_private_ip
    value: ''
    # Application Gateway private IP address (environment-specific, set in combined overrides)

  # --- Virtual Machines (Compute) ---
  - name: computeSubnetNamePattern
    value: "snet-{region}-compute01"
    # Naming pattern for VM compute subnet

  # --- Private Endpoints ---
  - name: privateEndpointSubnetNamePattern
    value: "snet-{region}-pe01"
    # Naming pattern for private endpoint subnet



