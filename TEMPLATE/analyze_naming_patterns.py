#!/usr/bin/env python3
"""
Analyze naming patterns in tfvars files to understand conventions.
"""

import re
from pathlib import Path
from collections import defaultdict

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Match variable assignments: variable_name = "value"
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            # Skip comments
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return variables

def analyze_patterns():
    """Analyze naming patterns across all tfvars files."""
    
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Store patterns for each variable
    patterns = defaultdict(lambda: defaultdict(list))
    
    # Process each terraform directory
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        # Find all .tfvars files
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            
            # Extract environment from filename or variables
            env = None
            if 'DEV' in tfvars_file.name:
                env = 'dev'
            elif 'TST' in tfvars_file.name:
                env = 'tst'
            elif 'PRD' in tfvars_file.name:
                env = 'prd'
            elif 'environment' in variables:
                env = variables['environment']
            
            # Store patterns
            for var_name, var_value in variables.items():
                if env:
                    patterns[var_name][var_value].append({
                        'file': str(tfvars_file.relative_to(base_dir)),
                        'env': env,
                        'project': variables.get('project', 'unknown'),
                        'region': variables.get('region', 'unknown')
                    })
    
    return patterns

def main():
    print("Analyzing naming patterns...")
    patterns = analyze_patterns()
    
    # Focus on key variables
    key_vars = [
        'vnet_name', 'vnet_rgrp_name', 'snet_name',
        'pe_snet_name', 'pe_vnet_name', 'pe_vnet_rgrp_name',
        'rtbl_name', 'rtbl_rgrp_name',
        'kvau_name', 'kvau_rgrp_name', 'kv_name', 'kv_rgrp_name',
        'subnet_name'
    ]
    
    output_file = 'naming_patterns_analysis.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("NAMING PATTERNS ANALYSIS\n")
        f.write("=" * 80 + "\n\n")
        
        for var_name in key_vars:
            if var_name not in patterns:
                continue
                
            f.write(f"\n{'=' * 80}\n")
            f.write(f"{var_name.upper()}\n")
            f.write(f"{'=' * 80}\n\n")
            
            # Group by pattern
            value_counts = {}
            for value, occurrences in patterns[var_name].items():
                value_counts[value] = len(occurrences)
            
            # Sort by frequency
            sorted_values = sorted(value_counts.items(), key=lambda x: x[1], reverse=True)
            
            f.write(f"Total unique values: {len(sorted_values)}\n")
            f.write(f"Total occurrences: {sum(value_counts.values())}\n\n")
            
            # Show top 20 patterns
            f.write("Top patterns:\n")
            f.write("-" * 80 + "\n")
            for value, count in sorted_values[:20]:
                f.write(f"{count:3d}x  {value}\n")
                
                # Show examples
                examples = patterns[var_name][value][:2]
                for ex in examples:
                    f.write(f"       env={ex['env']}, project={ex['project']}, region={ex['region']}\n")
                f.write("\n")
            
            if len(sorted_values) > 20:
                f.write(f"\n... and {len(sorted_values) - 20} more unique values\n")
    
    print(f"Naming patterns analysis saved to: {output_file}")
    
    # Analyze region abbreviations
    region_abbrev = defaultdict(set)
    
    for var_name in ['vnet_name', 'vnet_rgrp_name', 'snet_name']:
        if var_name not in patterns:
            continue
            
        for value, occurrences in patterns[var_name].items():
            for occ in occurrences:
                region = occ['region']
                # Extract region abbreviation from value
                # Common patterns: weu, gwc, neu, etc.
                match = re.search(r'-([a-z]{2,4})-', value)
                if match:
                    abbrev = match.group(1)
                    region_abbrev[region].add(abbrev)
    
    # Save region mapping
    region_file = 'region_abbreviations.txt'
    with open(region_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("REGION ABBREVIATIONS\n")
        f.write("=" * 80 + "\n\n")
        
        for region in sorted(region_abbrev.keys()):
            abbrevs = sorted(region_abbrev[region])
            f.write(f"{region:30s} -> {', '.join(abbrevs)}\n")
    
    print(f"Region abbreviations saved to: {region_file}")

if __name__ == '__main__':
    main()

