====================================================================================================
REPOSITORY USAGE ANALYSIS
====================================================================================================


subsidiary (30 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-search
  - ai-services
  - analysisservice
  - apimanagement
  - app-configuration
  - application-insights
  - appservice
  - brick-pdns
  - cassandrami
  - communicationservices
  - databricks
  - datafactory
  - dce
  - dcr
  - eventgrid
  - frontdoor
  - lb
  - linux-web-app
  - load-testing
  - machine-learning
  - purview
  - recovery-services-vault
  - redis-cache
  - rg
  - rhel-vm
  - rhel-vm-scale-set
  - storageaccount
  - synapse-workspace
  - windows-vm-scale-set

vnet_name (23 repos):
----------------------------------------------------------------------------------------------------
  - acr
  - ai-foundry
  - ai-search
  - apimanagement
  - application-insights
  - brick-pdns
  - brick-private-endpoint
  - cassandrami
  - databricks
  - lb
  - linux-web-app
  - machine-learning
  - recovery-services-vault
  - rhel-vm
  - rhel-vm-scale-set
  - storageaccount
  - streamanalytics
  - subnet
  - synapse-workspace
  - trafficmanager
  - windows-vm
  - windows-vm-scale-set
  - windows-web-app

vnet_rgrp_name (23 repos):
----------------------------------------------------------------------------------------------------
  - acr
  - ai-foundry
  - ai-search
  - apimanagement
  - application-insights
  - brick-pdns
  - brick-private-endpoint
  - cassandrami
  - databricks
  - lb
  - linux-web-app
  - machine-learning
  - recovery-services-vault
  - rhel-vm
  - rhel-vm-scale-set
  - storageaccount
  - streamanalytics
  - subnet
  - synapse-workspace
  - trafficmanager
  - windows-vm
  - windows-vm-scale-set
  - windows-web-app

pe_vnet_name (10 repos):
----------------------------------------------------------------------------------------------------
  - app-configuration
  - application-gateway
  - application-insights
  - datafactory
  - eventgrid
  - linux-function-app
  - linux-web-app
  - recovery-services-vault
  - redis-cache
  - windows-function-app

pe_vnet_rgrp_name (10 repos):
----------------------------------------------------------------------------------------------------
  - app-configuration
  - application-gateway
  - application-insights
  - datafactory
  - eventgrid
  - linux-function-app
  - linux-web-app
  - recovery-services-vault
  - redis-cache
  - windows-function-app

rtbl_name (18 repos):
----------------------------------------------------------------------------------------------------
  - aks
  - apimanagement
  - application-insights
  - cassandrami
  - databricks
  - datafactory
  - lb
  - linux-function-app
  - linux-web-app
  - recovery-services-vault
  - rhel-vm
  - rhel-vm-scale-set
  - subnet
  - trafficmanager
  - windows-function-app
  - windows-vm
  - windows-vm-scale-set
  - windows-web-app

rtbl_rgrp_name (14 repos):
----------------------------------------------------------------------------------------------------
  - apimanagement
  - application-insights
  - cassandrami
  - databricks
  - datafactory
  - lb
  - linux-function-app
  - linux-web-app
  - recovery-services-vault
  - rhel-vm
  - rhel-vm-scale-set
  - windows-function-app
  - windows-vm
  - windows-vm-scale-set

kvau_rgrp_name (10 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-language
  - ai-search
  - app-configuration
  - cassandrami
  - datafactory
  - recovery-services-vault
  - rhel-vm
  - rhel-vm-scale-set
  - windows-vm-scale-set

kv_rgrp_name (7 repos):
----------------------------------------------------------------------------------------------------
  - acr
  - ai-services
  - databricks
  - load-testing
  - machine-learning
  - storageaccount
  - windows-vm

vnet_vnet_name (4 repos):
----------------------------------------------------------------------------------------------------
  - aks
  - datafactory
  - linux-function-app
  - windows-function-app

vnet_vnet_rgrp_name (4 repos):
----------------------------------------------------------------------------------------------------
  - aks
  - datafactory
  - linux-function-app
  - windows-function-app

prep_subnet_vnet_name (3 repos):
----------------------------------------------------------------------------------------------------
  - ai-language
  - ai-services
  - databricks

prep_subnet_vnet_rgrp_name (3 repos):
----------------------------------------------------------------------------------------------------
  - ai-language
  - ai-services
  - databricks

mhsm_umid_name (3 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-language
  - ai-search

mhsm_umid_rgrp_name (3 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-language
  - ai-search

mhsm_key (3 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-language
  - ai-search

ingress_nginx_ip (1 repos):
----------------------------------------------------------------------------------------------------
  - aks

appgw_private_ip (1 repos):
----------------------------------------------------------------------------------------------------
  - application-gateway

aad_admin_name (1 repos):
----------------------------------------------------------------------------------------------------
  - synapse-workspace

synapse_role_assignment_principal_id (1 repos):
----------------------------------------------------------------------------------------------------
  - synapse-workspace


====================================================================================================
PATTERN ANALYSIS
====================================================================================================

Repos using 'subsidiary' (30 repos):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-search
  - ai-services
  - analysisservice
  - apimanagement
  - app-configuration
  - application-insights
  - appservice
  - brick-pdns
  - cassandrami
  - communicationservices
  - databricks
  - datafactory
  - dce
  - dcr
  - eventgrid
  - frontdoor
  - lb
  - linux-web-app
  - load-testing
  - machine-learning
  - purview
  - recovery-services-vault
  - redis-cache
  - rg
  - rhel-vm
  - rhel-vm-scale-set
  - storageaccount
  - synapse-workspace
  - windows-vm-scale-set


Repos using HSM variables (mhsm_*):
----------------------------------------------------------------------------------------------------
  - ai-foundry
  - ai-language
  - ai-search


Repos using 'ingress_nginx_ip' (AKS specific):
----------------------------------------------------------------------------------------------------
  - aks


Repos using Application Gateway variables:
----------------------------------------------------------------------------------------------------
  - application-gateway


Repos using Database/Synapse variables:
----------------------------------------------------------------------------------------------------
  - synapse-workspace
