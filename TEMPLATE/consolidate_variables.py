#!/usr/bin/env python3
"""
Consolidate variables - find which variables are actually the same thing.
"""

import re
from pathlib import Path
from collections import defaultdict

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        pass
        
    return variables

def main():
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Track which files have which variables with which values
    file_variables = {}  # file_path -> {var_name: var_value}
    
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            file_ref = f"{terraform_dir.name}/{tfvars_file.relative_to(terraform_dir)}"
            file_variables[file_ref] = variables
    
    # Define variable groups that might be the same
    variable_groups = {
        'vnet_name': [
            'vnet_name',
            'pe_vnet_name',
            'vnet_vnet_name',
            'appgw_vnet_name',
            'prep_subnet_vnet_name',
            'prep_storage_subnet_vnet_name',
            'primary_vnet_name',
            'secondary_vnet_name',
        ],
        'vnet_rgrp_name': [
            'vnet_rgrp_name',
            'pe_vnet_rgrp_name',
            'vnet_vnet_rgrp_name',
            'appgw_vnet_rgrp_name',
            'prep_subnet_vnet_rgrp_name',
            'prep_storage_subnet_vnet_rgrp_name',
            'primary_vnet_rgrp_name',
            'secondary_vnet_rgrp_name',
        ],
        'subnet_name': [
            'subnet_name',
            'snet_name',
            'pe_snet_name',
            'vnet_snet_name',
            'appgw_snet_name',
            'prep_subnet_name',
            'prep_storage_subnet_name',
        ],
        'key_vault_name': [
            'key_vault_name',
            'kvau_name',
            'kv_name',
            'keyvault_name',
        ],
        'kvau_rgrp_name': [
            'kvau_rgrp_name',
            'kv_rgrp_name',
        ],
        'rtbl_name': [
            'rtbl_name',
        ],
        'rtbl_rgrp_name': [
            'rtbl_rgrp_name',
            'rtbl_rg_name',
        ],
    }
    
    output_file = 'CONSOLIDATION_REPORT.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("VARIABLE CONSOLIDATION REPORT\n")
        f.write("=" * 100 + "\n\n")
        f.write("Analyzing which variables are actually the same thing...\n\n")
        
        for canonical_name, var_list in variable_groups.items():
            f.write("\n" + "=" * 100 + "\n")
            f.write(f"GROUP: {canonical_name}\n")
            f.write("=" * 100 + "\n\n")
            
            # Find files that have multiple variables from this group
            files_with_multiple = []
            
            for file_ref, variables in file_variables.items():
                vars_in_file = {}
                for var_name in var_list:
                    if var_name in variables:
                        vars_in_file[var_name] = variables[var_name]
                
                if len(vars_in_file) > 1:
                    files_with_multiple.append((file_ref, vars_in_file))
            
            if files_with_multiple:
                f.write(f"Files with MULTIPLE variables from this group:\n")
                f.write("-" * 100 + "\n\n")
                
                for file_ref, vars_in_file in files_with_multiple:
                    f.write(f"{file_ref}:\n")
                    
                    # Check if all values are the same
                    values = list(vars_in_file.values())
                    all_same = len(set(values)) == 1
                    
                    if all_same:
                        f.write(f"  ✓ ALL SAME: '{values[0]}'\n")
                    else:
                        f.write(f"  ✗ DIFFERENT VALUES:\n")
                    
                    for var_name, var_value in vars_in_file.items():
                        f.write(f"    {var_name:40s} = '{var_value}'\n")
                    f.write("\n")
            else:
                f.write(f"No files have multiple variables from this group.\n")
            
            # Show usage statistics for each variable in the group
            f.write("\n" + "-" * 100 + "\n")
            f.write("Usage statistics:\n")
            f.write("-" * 100 + "\n\n")
            
            for var_name in var_list:
                count = sum(1 for vars in file_variables.values() if var_name in vars)
                if count > 0:
                    # Get unique values
                    values = set()
                    for vars in file_variables.values():
                        if var_name in vars:
                            values.add(vars[var_name])
                    
                    f.write(f"{var_name:40s}: {count:3d} files, {len(values):2d} unique values\n")
            
            # Recommendation
            f.write("\n" + "-" * 100 + "\n")
            f.write("RECOMMENDATION:\n")
            f.write("-" * 100 + "\n\n")
            
            # Count usage for each variable
            usage_counts = {}
            for var_name in var_list:
                count = sum(1 for vars in file_variables.values() if var_name in vars)
                if count > 0:
                    usage_counts[var_name] = count
            
            if not usage_counts:
                f.write("No variables in this group are used.\n")
                continue
            
            # Find the most used variable
            most_used = max(usage_counts.items(), key=lambda x: x[1])
            
            # Check if variables are truly aliases (same values in same files)
            are_aliases = True
            different_examples = []
            
            for file_ref, variables in file_variables.items():
                vars_in_file = {}
                for var_name in var_list:
                    if var_name in variables:
                        vars_in_file[var_name] = variables[var_name]
                
                if len(vars_in_file) > 1:
                    values = list(vars_in_file.values())
                    if len(set(values)) > 1:
                        are_aliases = False
                        different_examples.append((file_ref, vars_in_file))
            
            if are_aliases:
                f.write(f"✓ All variables in this group are TRUE ALIASES (same values when present together)\n\n")
                f.write(f"Canonical variable: {canonical_name}\n")
                f.write(f"Most used variable: {most_used[0]} ({most_used[1]} files)\n\n")
                f.write(f"Action: Keep only '{canonical_name}' in defaults.yaml\n")
                f.write(f"        Map all others as aliases:\n")
                for var_name in var_list:
                    if var_name != canonical_name and var_name in usage_counts:
                        f.write(f"        - {var_name} -> ${{{{ variables.{canonical_name} }}}}\n")
            else:
                f.write(f"✗ Variables in this group are NOT always aliases!\n\n")
                f.write(f"Examples where values differ:\n")
                for file_ref, vars_in_file in different_examples[:3]:
                    f.write(f"\n  {file_ref}:\n")
                    for var_name, var_value in vars_in_file.items():
                        f.write(f"    {var_name:40s} = '{var_value}'\n")
                
                f.write(f"\nAction: These need separate handling!\n")
    
    print(f"Consolidation report saved to: {output_file}")
    
    # Print summary
    print("\n" + "=" * 100)
    print("SUMMARY")
    print("=" * 100)
    
    for canonical_name, var_list in variable_groups.items():
        usage_counts = {}
        for var_name in var_list:
            count = sum(1 for vars in file_variables.values() if var_name in vars)
            if count > 0:
                usage_counts[var_name] = count
        
        if usage_counts:
            print(f"\n{canonical_name}:")
            for var_name, count in sorted(usage_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {var_name:40s}: {count:3d} files")

if __name__ == '__main__':
    main()

