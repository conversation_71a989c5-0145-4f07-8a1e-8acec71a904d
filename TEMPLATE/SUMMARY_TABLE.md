# Öss<PERSON><PERSON><PERSON><PERSON> táblázat - Változók kezelése

## G<PERSON><PERSON>

| Kategória | Változók száma | % | Ak<PERSON><PERSON> |
|-----------|----------------|---|-------|
| ✅ <PERSON><PERSON>r a composer-ben | 13 | 20% | <PERSON><PERSON><PERSON> teen<PERSON> |
| 🔧 Számítható (hozzáadandó) | 6 | 9% | defaults.yaml-ba |
| ➕ Alias-ok (hozzáadandó) | 3 | 5% | defaults.yaml-ba |
| 📄 Rit<PERSON><PERSON> használt | 42 | 66% | tfvars.template |
| **ÖSSZESEN** | **64** | **100%** | |

## R<PERSON><PERSON>tes bontás

### ✅ <PERSON><PERSON><PERSON> a composer-ben van (13 változó)

| # | Tfvars v<PERSON>lt<PERSON> | Composer v<PERSON><PERSON><PERSON><PERSON> | Használat | Repo-k |
|---|----------------|------------------|-----------|--------|
| 1 | project | project | 307 | 45 |
| 2 | cloud | cloud | 293 | 44 |
| 3 | environment | environment | 293 | 44 |
| 4 | region | region | 292 | 44 |
| 5 | snet_name | subnet_name | 98 | 11 |
| 6 | pe_snet_name | subnet_name | 64 | 10 |
| 7 | kvau_name | key_vault_name | 49 | 9 |
| 8 | kv_name | key_vault_name | 34 | 7 |
| 9 | prep_subnet_name | subnet_name | 34 | 3 |
| 10 | vnet_snet_name | subnet_name | 16 | 1 |
| 11 | subnet_name | subnet_name | 10 | 1 |
| 12 | keyvault_name | key_vault_name | 3 | 1 |
| 13 | prep_storage_subnet_name | subnet_name | 3 | 1 |

**Akció**: ✅ Nincs, ezek már működnek!

---

### 🔧 Számítható - Hozzáadandó a defaults.yaml-ba (6 változó)

| # | Változó | Használat | Repo-k | Számítási formula |
|---|---------|-----------|--------|-------------------|
| 1 | subsidiary | 176 | 30 | `${{ parameters.subsidiary }}` |
| 2 | vnet_name | 167 | 23 | `vnet-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01` |
| 3 | vnet_rgrp_name | 167 | 23 | `rgrp-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01` |
| 4 | rtbl_name | 83 | 18 | `rtbl-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-core` |
| 5 | rtbl_rgrp_name | 65 | 14 | `${{ variables.vnet_rgrp_name }}` |
| 6 | kvau_rgrp_name | 52 | 10 | `${{ variables.vnet_rgrp_name }}` |

**Akció**: 🔧 Hozzáadni a defaults.yaml-hoz

---

### ➕ Alias-ok - Hozzáadandó a defaults.yaml-ba (3 változó)

| # | Változó | Használat | Repo-k | Alias erre |
|---|---------|-----------|--------|------------|
| 1 | pe_vnet_name | 64 | 10 | vnet_name |
| 2 | pe_vnet_rgrp_name | 64 | 10 | vnet_rgrp_name |
| 3 | kv_rgrp_name | 33 | 7 | kvau_rgrp_name |

**Akció**: ➕ Hozzáadni a defaults.yaml-hoz

---

### 📄 Ritkán használt - tfvars.template-be (42 változó)

#### Modul-specifikus változók (magas prioritás)

| Változó | Használat | Repo-k | Mely modul(ok) |
|---------|-----------|--------|----------------|
| ingress_nginx_ip | 9 | 1 | aks |
| mhsm_umid_name | 9 | 3 | ai-foundry, ai-language, ai-search |
| mhsm_umid_rgrp_name | 9 | 3 | ai-foundry, ai-language, ai-search |
| mhsm_key | 9 | 3 | ai-foundry, ai-language, ai-search |
| appgw_vnet_name | 12 | 1 | application-gateway |
| appgw_vnet_rgrp_name | 12 | 1 | application-gateway |
| appgw_snet_name | 12 | 1 | application-gateway |
| appgw_private_ip | 7 | 1 | application-gateway |
| aad_admin_name | 6 | 1 | synapse-workspace |
| synapse_role_assignment_principal_id | 6 | 1 | synapse-workspace |

#### Alias változók (közepes prioritás)

| Változó | Használat | Repo-k | Mely modul(ok) |
|---------|-----------|--------|----------------|
| vnet_vnet_name | 32 | 4 | aks, datafactory, linux-function-app, windows-function-app |
| vnet_vnet_rgrp_name | 32 | 4 | aks, datafactory, linux-function-app, windows-function-app |
| prep_subnet_vnet_name | 34 | 3 | ai-language, ai-services, databricks |
| prep_subnet_vnet_rgrp_name | 34 | 3 | ai-language, ai-services, databricks |

#### Ritkán használt (alacsony prioritás)

| Változó | Használat | Repo-k | Megjegyzés |
|---------|-----------|--------|------------|
| address_prefix | 15 | 4 | CIDR specific |
| resource_name_suffix | 14 | 1 | Naming specific |
| keyvault_id | 9 | 2 | Computed value |
| puip_name | 5 | 1 | Public IP specific |
| puip_rgrp_name | 5 | 1 | Public IP specific |
| rtbl_rg_name | 4 | 1 | Alias rtbl_rgrp_name |
| snet_prefix | 3 | 1 | CIDR specific |
| lb_private_ip | 3 | 1 | Load Balancer specific |
| lb_frontend_ip1 | 3 | 1 | Load Balancer specific |
| prep_storage_subnet_vnet_name | 3 | 1 | Alias |
| prep_storage_subnet_vnet_rgrp_name | 3 | 1 | Alias |
| first_subnet | 3 | 1 | Multi-subnet |
| apgw_public_ip_id | 2 | 1 | AppGW specific |
| remote_state_* | 2 | 1 | Remote state specific |
| ... | ... | ... | ... |

**Akció**: 📄 tfvars.template fájlokba dokumentálni

---

## Implementációs prioritások

### Prioritás 1: KRITIKUS (azonnal) - 1 nap

**Hozzáadni a composer-hez:**
- subsidiary parameter
- vnet_name, vnet_rgrp_name
- rtbl_name, rtbl_rgrp_name
- kvau_rgrp_name
- pe_vnet_name, pe_vnet_rgrp_name
- kv_rgrp_name

**Eredmény**: 31 változó (48%) automatikusan kezelve

### Prioritás 2: MAGAS (1 hét)

**tfvars.template létrehozása ezekben a repo-kban:**
- terraform-azurerm-aks
- terraform-azurerm-ai-foundry
- terraform-azurerm-ai-language
- terraform-azurerm-ai-search
- terraform-azurerm-application-gateway
- terraform-azurerm-synapse-workspace

**Eredmény**: Modul-specifikus változók dokumentálva

### Prioritás 3: KÖZEPES (2 hét)

**tfvars.template létrehozása ezekben a repo-kban:**
- terraform-azurerm-datafactory
- terraform-azurerm-linux-function-app
- terraform-azurerm-windows-function-app
- terraform-azurerm-databricks
- terraform-azurerm-ai-services

**Eredmény**: Alias változók dokumentálva

### Prioritás 4: ALACSONY (3 hét)

**tfvars.template létrehozása minden más repo-ban**

**Eredmény**: Teljes lefedettség

---

## Várható hatás

### Jelenlegi helyzet
```
Átlagos tfvars fájl:
- 15-20 változó manuálisan megadva
- Sok ismétlődés
- Elírási lehetőségek
```

### Új helyzet (Prioritás 1 után)
```
Átlagos tfvars fájl:
- 0-5 változó manuálisan megadva (csak modul-specifikus)
- Nincs ismétlődés
- Kevesebb elírási lehetőség
```

### Számok

| Metrika | Előtte | Utána | Javulás |
|---------|--------|-------|---------|
| Manuális változók | 15-20 | 0-5 | 75-100% |
| Elírási lehetőség | Magas | Alacsony | 80% |
| Karbantartási idő | 100% | 25% | 75% |
| Konzisztencia | 60% | 95% | 35% |

---

## Következő lépések

1. ✅ **Elemzés elkészült** - Jelen dokumentum
2. ⏳ **Jóváhagyás** - Döntés a javaslatról
3. ⏳ **Prioritás 1** - Composer bővítése (1 nap)
4. ⏳ **Prioritás 2** - Kritikus tfvars.template-ek (1 hét)
5. ⏳ **Prioritás 3** - További tfvars.template-ek (2 hét)
6. ⏳ **Prioritás 4** - Teljes lefedettség (3 hét)
7. ⏳ **Dokumentáció** - README-k frissítése
8. ⏳ **Tesztelés** - Példa projektek
9. ⏳ **Rollout** - Fokozatos bevezetés

---

**Készítette**: Augment AI  
**Dátum**: 2025-11-04  
**Verzió**: 2.0 (Alapos elemzés)

