====================================================================================================
VERIFICATION REPORT - Checking Assumptions
====================================================================================================

====================================================================================================
Q1: Are snet_name and subnet_name the same?
====================================================================================================

snet_name has 7 unique values:
  'snet-weu-pe01' (50 files)
    - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
    - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
    - terraform-azurerm-ai-search/examples/03-network/PRD-iac.tfvars
    ... and 47 more
  'privateendpoints' (16 files)
    - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
    - terraform-azurerm-ai-search/examples/02-hsm/DEV-demo01.tfvars
    - terraform-azurerm-machine-learning/examples/01-default/DEV-demo01.tfvars
    ... and 13 more
  'privateendpoints2' (12 files)
    - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
    - terraform-azurerm-ai-search/examples/03-network/DEV-demo01.tfvars
    - terraform-azurerm-ai-search/examples/01-default/DEV-demo01.tfvars
    ... and 9 more
  'snet-weu-compute01' (10 files)
    - terraform-azurerm-rhel-vm/examples/01-default/PRD-iac.tfvars
    - terraform-azurerm-rhel-vm/examples/01-default/TST-iac.tfvars
    - terraform-azurerm-rhel-vm/examples/02-compute_gallery_image/PRD-iac.tfvars
    ... and 7 more
  'snet-test1' (5 files)
    - terraform-azurerm-rhel-vm/examples/01-default/DEV-demo01.tfvars
    - terraform-azurerm-rhel-vm/examples/02-compute_gallery_image/DEV-demo01.tfvars
    - terraform-azurerm-windows-vm/examples/04-vm_autoshutdown/DEV-demo01.tfvars
    ... and 2 more
  'snet-gwc-pe01' (4 files)
    - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
    - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
    - terraform-azurerm-ai-search/examples/02-hsm/PRD-iac.tfvars
    ... and 1 more
  'snet-gwc-compute01' (1 files)
    - terraform-azurerm-rhel-vm/examples/01-default-gwc/TST-iac.tfvars

subnet_name has 3 unique values:
  'snet-weu-pe01' (6 files)
    - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
    - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
    - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
    ... and 3 more
  'privateendpoints' (3 files)
    - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
    - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
    - terraform-azurerm-acr/examples/03-disabled-acr/DEV-demo01.tfvars
  'aks' (1 files)
    - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars

====================================================================================================
Q2: Are kv_name and keyvault_name the same?
====================================================================================================

kv_name has 5 unique values:
  'iac-prd-shared01' (11 files)
    - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
    - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
    - terraform-azurerm-acr/examples/03-disabled-acr/PRD-iac.tfvars
    ... and 8 more
  'iac-tst-shared01' (11 files)
    - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
    - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
    - terraform-azurerm-acr/examples/03-disabled-acr/TST-iac.tfvars
    ... and 8 more
  'kvau-weu-dev-DEVD000001' (10 files)
    - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
    - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
    - terraform-azurerm-acr/examples/03-disabled-acr/DEV-demo01.tfvars
    ... and 7 more
  'kvau-gwc-dev-RITM2480573' (1 files)
    - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
  'kvaugwctstiac-shared01' (1 files)
    - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars

keyvault_name has 3 unique values:
  'kvau-weu-dev-DEVD000001' (1 files)
    - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
  'iac-prd-shared01' (1 files)
    - terraform-azurerm-datafactory/examples/03-integration_runtime/PRD-iac.tfvars
  'iac-tst-shared01' (1 files)
    - terraform-azurerm-datafactory/examples/03-integration_runtime/TST-iac.tfvars

====================================================================================================
Q3: Is subsidiary always 'otphq'?
====================================================================================================

subsidiary has 1 unique values:
  'otphq' (176 files)
    - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
    - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
    - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
    - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
    - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
    ... and 171 more

====================================================================================================
Q4: Is pe_vnet_name always the same as vnet_name?
====================================================================================================

Checking files that have BOTH pe_vnet_name and vnet_name:

✓ SAME: terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/PRD-iac.tfvars
  pe_vnet_name = 'vnet-weu-prd-iac-01'
  vnet_name    = 'vnet-weu-prd-iac-01'

✗ DIFFERENT: terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
  pe_vnet_name = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'
  vnet_name    = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'

✓ SAME: terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/TST-iac.tfvars
  pe_vnet_name = 'vnet-weu-tst-iac-01'
  vnet_name    = 'vnet-weu-tst-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/02-webapp_from_image/DEV-demo01.tfvars
  pe_vnet_name = 'vnet-weu-dev-demo01-01'
  vnet_name    = 'vnet-weu-dev-demo01-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/02-webapp_from_image/PRD-iac.tfvars
  pe_vnet_name = 'vnet-weu-prd-iac-01'
  vnet_name    = 'vnet-weu-prd-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/02-webapp_from_image/TST-iac.tfvars
  pe_vnet_name = 'vnet-weu-tst-iac-01'
  vnet_name    = 'vnet-weu-tst-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/01-default/DEV-demo01.tfvars
  pe_vnet_name = 'vnet-weu-dev-demo01-01'
  vnet_name    = 'vnet-weu-dev-demo01-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/01-default/PRD-iac.tfvars
  pe_vnet_name = 'vnet-weu-prd-iac-01'
  vnet_name    = 'vnet-weu-prd-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars
  pe_vnet_name = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'
  vnet_name    = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/01-default/TST-iac.tfvars
  pe_vnet_name = 'vnet-weu-tst-iac-01'
  vnet_name    = 'vnet-weu-tst-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/03-share_service_plan/DEV-demo01.tfvars
  pe_vnet_name = 'vnet-weu-dev-demo01-01'
  vnet_name    = 'vnet-weu-dev-demo01-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/03-share_service_plan/PRD-iac.tfvars
  pe_vnet_name = 'vnet-weu-prd-iac-01'
  vnet_name    = 'vnet-weu-prd-iac-01'

✓ SAME: terraform-azurerm-linux-web-app/examples/03-share_service_plan/TST-iac.tfvars
  pe_vnet_name = 'vnet-weu-tst-iac-01'
  vnet_name    = 'vnet-weu-tst-iac-01'

✓ SAME: terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/DEV-demo01.tfvars
  pe_vnet_name = 'vnet-weu-dev-demo01-01'
  vnet_name    = 'vnet-weu-dev-demo01-01'

✓ SAME: terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/PRD-iac.tfvars
  pe_vnet_name = 'vnet-weu-prd-iac-01'
  vnet_name    = 'vnet-weu-prd-iac-01'

✓ SAME: terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/TST-iac.tfvars
  pe_vnet_name = 'vnet-weu-tst-iac-01'
  vnet_name    = 'vnet-weu-tst-iac-01'


====================================================================================================
Q5: Which variables have different values per environment?
====================================================================================================

Found 44 variables with environment-specific values:


appgw_private_ip:
--------------------------------------------------------------------------------
  DEV:
    '************' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
    '***********' (1 files)
      - terraform-azurerm-application-gateway/examples/03-private_and_public/DEV-demo01.tfvars
  TST:
    '************' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/TST-iac.tfvars
  PRD:
    '************' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/PRD-iac.tfvars

appgw_snet_name:
--------------------------------------------------------------------------------
  DEV:
    'apgwpoc' (4 files)
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars
      ... and 2 more
    'snet-test2' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
  TST:
    'snet-weu-compute04' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars
      ... and 1 more
  PRD:
    'snet-weu-compute04' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars
      ... and 1 more

appgw_vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'vnet-weu-dev-demo01-02' (4 files)
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars
      ... and 2 more
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
  TST:
    'vnet-weu-tst-iac-01' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars
      ... and 1 more
  PRD:
    'vnet-weu-prd-iac-01' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars
      ... and 1 more

appgw_vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (4 files)
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars
      ... and 2 more
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars
      ... and 1 more
  PRD:
    'rgrp-weu-prd-iac-01' (3 files)
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars
      ... and 1 more

environment:
--------------------------------------------------------------------------------
  DEV:
    'dev' (108 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 106 more
  TST:
    'tst' (94 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 92 more
  PRD:
    'prd' (90 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 88 more
    'dev' (1 files)
      - terraform-azurerm-windows-vm/examples/04-vm_autoshutdown/PRD-iac.tfvars

first_subnet:
--------------------------------------------------------------------------------
  DEV:
    '************/28' (1 files)
      - terraform-azurerm-subnet/examples/01-default/DEV-demo01.tfvars
  TST:
    '**********/28' (1 files)
      - terraform-azurerm-subnet/examples/01-default/TST-iac.tfvars
  PRD:
    '**********/28' (1 files)
      - terraform-azurerm-subnet/examples/01-default/PRD-iac.tfvars

ingress_nginx_ip:
--------------------------------------------------------------------------------
  DEV:
    '**********' (3 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-coeinf.tfvars
      - terraform-azurerm-aks/examples/16-aks-with-nodepool-subnet/DEV-coeinf.tfvars
      ... and 1 more
    '************' (2 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-demo01.tfvars
      - terraform-azurerm-aks/examples/01-default/DEV-demo01.tfvars
  TST:
    '***********' (2 files)
      - terraform-azurerm-aks/examples/01-default-GWC/TST-iac.tfvars
      - terraform-azurerm-aks/examples/01-default/TST-iac.tfvars
  PRD:
    '***********' (2 files)
      - terraform-azurerm-aks/examples/01-default-GWC/PRD-iac.tfvars
      - terraform-azurerm-aks/examples/01-default/PRD-iac.tfvars

keyvault_id:
--------------------------------------------------------------------------------
  DEV:
    '/subscriptions/65b59cbd-8b9e-48aa-be61-11bf4864aa09/resourceGroups/rgrp-weu-dev-demo01-01/providers/Microsoft.KeyVault/vaults/kvau-weu-dev-DEVD000001' (3 files)
      - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
      - terraform-azurerm-datafactory/examples/04-vsts_config/DEV-demo01.tfvars
      ... and 1 more
    '/subscriptions/52953d73-d162-4fb2-a5d3-b004a6a84781/resourceGroups/rgrp-weu-dev-kvau01/providers/Microsoft.KeyVault/vaults/kvau-weu-dev-shared' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/DEV-demo01.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/DEV-demo01.tfvars
  TST:
    '/subscriptions/4158adeb-d3de-4af6-86e9-b4b64dfdb52b/resourceGroups/rgrp-weu-tst-iac-01/providers/Microsoft.KeyVault/vaults/iac-tst-shared01' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/TST-iac.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/TST-iac.tfvars
  PRD:
    '/subscriptions/100a24f7-de96-44df-9568-7802dcae2bef/resourceGroups/rgrp-weu-prd-iac-01/providers/Microsoft.KeyVault/vaults/iac-prd-shared01' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/PRD-iac.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/PRD-iac.tfvars

keyvault_name:
--------------------------------------------------------------------------------
  DEV:
    'kvau-weu-dev-DEVD000001' (1 files)
      - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
  TST:
    'iac-tst-shared01' (1 files)
      - terraform-azurerm-datafactory/examples/03-integration_runtime/TST-iac.tfvars
  PRD:
    'iac-prd-shared01' (1 files)
      - terraform-azurerm-datafactory/examples/03-integration_runtime/PRD-iac.tfvars

kv_name:
--------------------------------------------------------------------------------
  DEV:
    'kvau-weu-dev-DEVD000001' (10 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 8 more
    'kvau-gwc-dev-RITM2480573' (1 files)
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
  TST:
    'iac-tst-shared01' (11 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 9 more
    'kvaugwctstiac-shared01' (1 files)
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'iac-prd-shared01' (11 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 9 more

kv_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (10 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 8 more
    'rgrp-gwc-dev-iac-01' (1 files)
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (11 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 9 more
    'rgrp-gwc-tst-iac-01' (1 files)
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (10 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 8 more

kvau_name:
--------------------------------------------------------------------------------
  DEV:
    'kvau-weu-dev-DEVD000001' (8 files)
      - terraform-azurerm-app-configuration/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/DEV-demo01.tfvars
      ... and 6 more
    'kvau-weu-dev-premium-01' (4 files)
      - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-language/examples/01-default/DEV-demo01.tfvars
      ... and 2 more
    'kvau-weu-dev-shared' (4 files)
      - terraform-azurerm-cassandrami/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-windows-vm-scale-set/examples/04-flexible/DEV-coeinf.tfvars
      ... and 2 more
  TST:
    'iac-tst-shared01' (17 files)
      - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/01-default/TST-iac.tfvars
      ... and 15 more
  PRD:
    'iac-prd-shared01' (16 files)
      - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/01-default/PRD-iac.tfvars
      ... and 14 more

kvau_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (13 files)
      - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-language/examples/01-default/DEV-demo01.tfvars
      ... and 11 more
    'rgrp-weu-dev-kvau01' (4 files)
      - terraform-azurerm-cassandrami/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-windows-vm-scale-set/examples/04-flexible/DEV-coeinf.tfvars
      ... and 2 more
  TST:
    'rgrp-weu-tst-IaC-01' (13 files)
      - terraform-azurerm-cassandrami/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-datafactory/examples/03-integration_runtime/TST-iac.tfvars
      ... and 11 more
    'rgrp-weu-tst-iac-01' (5 files)
      - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/01-default/TST-iac.tfvars
      ... and 3 more
  PRD:
    'rgrp-weu-prd-IaC-01' (12 files)
      - terraform-azurerm-cassandrami/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-datafactory/examples/03-integration_runtime/PRD-iac.tfvars
      ... and 10 more
    'rgrp-weu-prd-iac-01' (5 files)
      - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/01-default/PRD-iac.tfvars
      ... and 3 more

lb_frontend_ip1:
--------------------------------------------------------------------------------
  DEV:
    '***********' (1 files)
      - terraform-azurerm-lb/examples/01-default/DEV-demo01.tfvars
  TST:
    '**********' (1 files)
      - terraform-azurerm-lb/examples/01-default/TST-iac.tfvars
  PRD:
    '**********' (1 files)
      - terraform-azurerm-lb/examples/01-default/PRD-iac.tfvars

lb_private_ip:
--------------------------------------------------------------------------------
  DEV:
    '***********' (1 files)
      - terraform-azurerm-lb/examples/01-default/DEV-demo01.tfvars
  TST:
    '**********' (1 files)
      - terraform-azurerm-lb/examples/01-default/TST-iac.tfvars
  PRD:
    '**********' (1 files)
      - terraform-azurerm-lb/examples/01-default/PRD-iac.tfvars

mhsm_key:
--------------------------------------------------------------------------------
  DEV:
    'https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/demo-hsm-01/1f37fc75970e04a320d0e6a49af0aae8' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
      ... and 1 more
  TST:
    'https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/iactst-hsm-01/de35a4c752a50c9f0366b780b385c479' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      ... and 1 more
  PRD:
    'https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/iacprd-hsm-01/3b5b774ece514c702238ed9f31ae7272' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars
      ... and 1 more

mhsm_umid_name:
--------------------------------------------------------------------------------
  DEV:
    'umid-gwc-dev-demo-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
      ... and 1 more
  TST:
    'umid-gwc-tst-iac-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      ... and 1 more
  PRD:
    'umid-gwc-prd-iac-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars
      ... and 1 more

mhsm_umid_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-gwc-dev-demo01-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
      ... and 1 more
  TST:
    'rgrp-gwc-tst-iac-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      ... and 1 more
  PRD:
    'rgrp-gwc-prd-iac-01' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars
      ... and 1 more

pe_snet_name:
--------------------------------------------------------------------------------
  DEV:
    'privateendpoints' (16 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
      ... and 14 more
    'privateendpoints2' (4 files)
      - terraform-azurerm-app-configuration/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/DEV-demo01.tfvars
      ... and 2 more
    'pe01' (4 files)
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars
      ... and 2 more
  TST:
    'snet-weu-pe01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      ... and 18 more
  PRD:
    'snet-weu-pe01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      ... and 18 more

pe_vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'vnet-weu-dev-demo01-01' (16 files)
      - terraform-azurerm-app-configuration/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
      ... and 14 more
    'vnet-weu-dev-demo01-02' (4 files)
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars
      ... and 2 more
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
    'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01' (2 files)
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      - terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars
  TST:
    'vnet-weu-tst-iac-01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      ... and 18 more
  PRD:
    'vnet-weu-prd-iac-01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      ... and 18 more

pe_vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars
      ... and 18 more
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (2 files)
      - terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars
      - terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars
    'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01' (2 files)
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      - terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars
      ... and 18 more
  PRD:
    'rgrp-weu-prd-iac-01' (20 files)
      - terraform-azurerm-app-configuration/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars
      ... and 18 more

prep_storage_subnet_name:
--------------------------------------------------------------------------------
  DEV:
    'privateendpoints' (1 files)
      - terraform-azurerm-purview/examples/01-default/DEV-coeinf.tfvars
  TST:
    'snet-weu-pe01' (1 files)
      - terraform-azurerm-purview/examples/01-default/TST-iac.tfvars
  PRD:
    'snet-weu-pe01' (1 files)
      - terraform-azurerm-purview/examples/01-default/PRD-iac.tfvars

prep_storage_subnet_vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/DEV-coeinf.tfvars
  TST:
    'vnet-weu-tst-iac-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/TST-iac.tfvars
  PRD:
    'vnet-weu-prd-iac-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/PRD-iac.tfvars

prep_storage_subnet_vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/DEV-coeinf.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (1 files)
      - terraform-azurerm-purview/examples/01-default/PRD-iac.tfvars

prep_subnet_name:
--------------------------------------------------------------------------------
  DEV:
    'privateendpoints2' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/DEV-demo01.tfvars
      ... and 8 more
    'privateendpoints' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
  TST:
    'snet-weu-pe01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/TST-iac.tfvars
      ... and 8 more
    'snet-gwc-pe01' (2 files)
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'snet-weu-pe01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/PRD-iac.tfvars
      ... and 8 more
    'snet-gwc-pe01' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars

prep_subnet_vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'vnet-weu-dev-demo01-01' (9 files)
      - terraform-azurerm-ai-language/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/DEV-demo01.tfvars
      ... and 7 more
    'vnet-gwc-dev-demo01-01' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (1 files)
      - terraform-azurerm-databricks/examples/01-default/DEV-coeinf.tfvars
  TST:
    'vnet-weu-tst-iac-01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/TST-iac.tfvars
      ... and 8 more
    'vnet-gwc-tst-iac-01' (2 files)
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'vnet-weu-prd-iac-01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/PRD-iac.tfvars
      ... and 8 more
    'vnet-gwc-prd-iac-01' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars

prep_subnet_vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (9 files)
      - terraform-azurerm-ai-language/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/DEV-demo01.tfvars
      ... and 7 more
    'rgrp-gwc-dev-demo01-01' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/DEV-demo01.tfvars
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (1 files)
      - terraform-azurerm-databricks/examples/01-default/DEV-coeinf.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/TST-iac.tfvars
      ... and 8 more
    'rgrp-gwc-tst-iac-01' (2 files)
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (10 files)
      - terraform-azurerm-ai-language/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-services/examples/02-openai/PRD-iac.tfvars
      ... and 8 more
    'rgrp-gwc-prd-iac-01' (1 files)
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars

project:
--------------------------------------------------------------------------------
  DEV:
    'abb' (51 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 49 more
    'iac' (43 files)
      - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      ... and 41 more
    'sa1' (10 files)
      - terraform-azurerm-storageaccount/examples/04-premium-nfs-share/DEV-demo01.tfvars
      - terraform-azurerm-storageaccount/examples/08-block-blob/DEV-demo01.tfvars
      ... and 8 more
    'juh' (4 files)
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/DEV-demo01.tfvars
      - terraform-azurerm-streamanalytics/examples/01-new-cluster/DEV-demo01.tfvars
      ... and 2 more
    'redvuk' (2 files)
      - terraform-azurerm-brick-private-endpoint/examples/01-default/DEV-coeinf.tfvars
      - terraform-azurerm-subnet/examples/01-default/DEV-demo01.tfvars
    'coe' (1 files)
      - terraform-azurerm-subnet/examples/02-PowerPlatformVnetSupp/DEV-demo01.tfvars
    'avd' (1 files)
      - terraform-azurerm-virtual-desktop/examples/04-complete-deployment/DEV-coeinf.tfvars
  TST:
    'iac' (78 files)
      - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      ... and 76 more
    'abb' (16 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 14 more
    'juh' (5 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/TST-iac.tfvars
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/TST-iac.tfvars
      ... and 3 more
  PRD:
    'iac' (75 files)
      - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      ... and 73 more
    'abb' (16 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 14 more
    'juh' (5 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/PRD-iac.tfvars
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/PRD-iac.tfvars
      ... and 3 more

region:
--------------------------------------------------------------------------------
  DEV:
    'westeurope' (103 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 101 more
    'germanywestcentral' (4 files)
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      ... and 2 more
  TST:
    'westeurope' (88 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 86 more
    'germanywestcentral' (6 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/TST-iac.tfvars
      ... and 4 more
  PRD:
    'westeurope' (88 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 86 more
    'germanywestcentral' (3 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-language/examples/03-hsm/PRD-iac.tfvars
      ... and 1 more

remote_state_rgrp_name:
--------------------------------------------------------------------------------
  TST:
    'rgrp-weu-tst-iac-01' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/PRD-iac.tfvars

remote_state_stac_name:
--------------------------------------------------------------------------------
  TST:
    'stacweutstritm1669979' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/TST-iac.tfvars
  PRD:
    'stacweuprdritm1670198' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/PRD-iac.tfvars

resource_name_suffix:
--------------------------------------------------------------------------------
  DEV:
    'nostrc' (3 files)
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/DEV-demo01.tfvars
      - terraform-azurerm-streamanalytics/examples/04-no-cluster-realistic/DEV-demo01.tfvars
      ... and 1 more
    'newstrc' (1 files)
      - terraform-azurerm-streamanalytics/examples/01-new-cluster/DEV-demo01.tfvars
  TST:
    'nostrc' (3 files)
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/TST-iac.tfvars
      - terraform-azurerm-streamanalytics/examples/04-no-cluster-realistic/TST-iac.tfvars
      ... and 1 more
    'existingstrc' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/TST-iac.tfvars
    'newstrc' (1 files)
      - terraform-azurerm-streamanalytics/examples/01-new-cluster/TST-iac.tfvars
  PRD:
    'nostrc' (3 files)
      - terraform-azurerm-streamanalytics/examples/03-no-cluster/PRD-iac.tfvars
      - terraform-azurerm-streamanalytics/examples/04-no-cluster-realistic/PRD-iac.tfvars
      ... and 1 more
    'existingstrc' (1 files)
      - terraform-azurerm-streamanalytics/examples/02-existing-cluster/PRD-iac.tfvars
    'newstrc' (1 files)
      - terraform-azurerm-streamanalytics/examples/01-new-cluster/PRD-iac.tfvars

rtbl_name:
--------------------------------------------------------------------------------
  DEV:
    'rtbl-weu-dev-demo01-core' (20 files)
      - terraform-azurerm-cassandrami/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
      ... and 18 more
    'otp-dd-coeinfdev-sub-dev-01-rt-westeu-01' (10 files)
      - terraform-azurerm-apimanagement/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      ... and 8 more
    'aks-route' (2 files)
      - terraform-azurerm-aks/examples/16-aks-with-nodepool-subnet/DEV-coeinf.tfvars
      - terraform-azurerm-aks/examples/17-aks-overlay/DEV-coeinf.tfvars
    'rtbl-weu-dev-demo01-aks01' (1 files)
      - terraform-azurerm-aks/examples/17-aks-overlay/DEV-demo01.tfvars
  TST:
    'rtblweutstdefault' (24 files)
      - terraform-azurerm-apimanagement/examples/02-default/TST-iac.tfvars
      - terraform-azurerm-apimanagement/examples/01-default/TST-iac.tfvars
      ... and 22 more
    'rtblweutstaks01' (1 files)
      - terraform-azurerm-aks/examples/17-aks-overlay/TST-iac.tfvars
    'rtblgwctstdefault' (1 files)
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'rtblweuprddefault' (24 files)
      - terraform-azurerm-apimanagement/examples/02-default/PRD-iac.tfvars
      - terraform-azurerm-apimanagement/examples/01-default/PRD-iac.tfvars
      ... and 22 more

rtbl_rg_name:
--------------------------------------------------------------------------------
  DEV:
    'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01' (2 files)
      - terraform-azurerm-aks/examples/16-aks-with-nodepool-subnet/DEV-coeinf.tfvars
      - terraform-azurerm-aks/examples/17-aks-overlay/DEV-coeinf.tfvars
    'rgrp-weu-dev-demo01-01' (1 files)
      - terraform-azurerm-aks/examples/17-aks-overlay/DEV-demo01.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (1 files)
      - terraform-azurerm-aks/examples/17-aks-overlay/TST-iac.tfvars

rtbl_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (17 files)
      - terraform-azurerm-cassandrami/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
      ... and 15 more
    'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01' (7 files)
      - terraform-azurerm-apimanagement/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      ... and 5 more
  TST:
    'rgrp-weu-tst-iac-01' (20 files)
      - terraform-azurerm-apimanagement/examples/02-default/TST-iac.tfvars
      - terraform-azurerm-apimanagement/examples/01-default/TST-iac.tfvars
      ... and 18 more
    'rgrp-gwc-tst-iac-01' (1 files)
      - terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (20 files)
      - terraform-azurerm-apimanagement/examples/02-default/PRD-iac.tfvars
      - terraform-azurerm-apimanagement/examples/01-default/PRD-iac.tfvars
      ... and 18 more

snet_name:
--------------------------------------------------------------------------------
  DEV:
    'privateendpoints' (16 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/DEV-demo01.tfvars
      ... and 14 more
    'privateendpoints2' (12 files)
      - terraform-azurerm-ai-foundry/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-ai-search/examples/03-network/DEV-demo01.tfvars
      ... and 10 more
    'snet-test1' (5 files)
      - terraform-azurerm-rhel-vm/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-rhel-vm/examples/02-compute_gallery_image/DEV-demo01.tfvars
      ... and 3 more
  TST:
    'snet-weu-pe01' (25 files)
      - terraform-azurerm-ai-foundry/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-ai-search/examples/03-network/TST-iac.tfvars
      ... and 23 more
    'snet-weu-compute01' (5 files)
      - terraform-azurerm-rhel-vm/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-rhel-vm/examples/02-compute_gallery_image/TST-iac.tfvars
      ... and 3 more
    'snet-gwc-pe01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/TST-iac.tfvars
    'snet-gwc-compute01' (1 files)
      - terraform-azurerm-rhel-vm/examples/01-default-gwc/TST-iac.tfvars
  PRD:
    'snet-weu-pe01' (25 files)
      - terraform-azurerm-ai-foundry/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-ai-search/examples/03-network/PRD-iac.tfvars
      ... and 23 more
    'snet-weu-compute01' (5 files)
      - terraform-azurerm-rhel-vm/examples/01-default/PRD-iac.tfvars
      - terraform-azurerm-rhel-vm/examples/02-compute_gallery_image/PRD-iac.tfvars
      ... and 3 more
    'snet-gwc-pe01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/PRD-iac.tfvars

snet_prefix:
--------------------------------------------------------------------------------
  DEV:
    '10.94.26.64/28' (1 files)
      - terraform-azurerm-lb/examples/01-default/DEV-demo01.tfvars
  TST:
    '10.94.64.0/28' (1 files)
      - terraform-azurerm-lb/examples/01-default/TST-iac.tfvars
  PRD:
    '**********/28' (1 files)
      - terraform-azurerm-lb/examples/01-default/PRD-iac.tfvars

subnet_name:
--------------------------------------------------------------------------------
  DEV:
    'privateendpoints' (3 files)
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
      ... and 1 more
    'aks' (1 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
  TST:
    'snet-weu-pe01' (3 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 1 more
  PRD:
    'snet-weu-pe01' (3 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 1 more

synapse_role_assignment_principal_id:
--------------------------------------------------------------------------------
  DEV:
    '1b97fd85-5dd5-47a2-8874-48286734309a' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/DEV-demo01.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/DEV-demo01.tfvars
  TST:
    '1ead47cb-48b9-4ce5-8dc6-6395cb285281' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/TST-iac.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/TST-iac.tfvars
  PRD:
    'af56edd7-5d53-445e-8905-ba6ad49aaefa' (2 files)
      - terraform-azurerm-synapse-workspace/examples/02-github_repo/PRD-iac.tfvars
      - terraform-azurerm-synapse-workspace/examples/01-default/PRD-iac.tfvars

vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'vnet-weu-dev-demo01-01' (44 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 42 more
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (6 files)
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      - terraform-azurerm-brick-private-endpoint/examples/01-default/DEV-coeinf.tfvars
      ... and 4 more
    'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01' (4 files)
      - terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars
      - terraform-azurerm-trafficmanager/examples/02-azure_endpoint/DEV-coeinf.tfvars
      ... and 2 more
    'vnet-gwc-dev-demo01-01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/DEV-demo01.tfvars
    'vnet-gwc-dev-iac-01' (1 files)
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
  TST:
    'vnet-weu-tst-iac-01' (52 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 50 more
    'vnet-gwc-tst-iac-01' (4 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/TST-iac.tfvars
      ... and 2 more
  PRD:
    'vnet-weu-prd-iac-01' (52 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 50 more
    'vnet-gwc-prd-iac-01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/PRD-iac.tfvars

vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (44 files)
      - terraform-azurerm-acr/examples/02-encryption/DEV-demo01.tfvars
      - terraform-azurerm-acr/examples/01-default/DEV-demo01.tfvars
      ... and 42 more
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (6 files)
      - terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
      - terraform-azurerm-brick-private-endpoint/examples/01-default/DEV-coeinf.tfvars
      ... and 4 more
    'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01' (4 files)
      - terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars
      - terraform-azurerm-trafficmanager/examples/02-azure_endpoint/DEV-coeinf.tfvars
      ... and 2 more
    'rgrp-gwc-dev-demo01-01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/DEV-demo01.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/DEV-demo01.tfvars
    'rgrp-gwc-dev-iac-01' (1 files)
      - terraform-azurerm-acr/examples/01-default/DEV-iac.tfvars
  TST:
    'rgrp-weu-tst-iac-01' (52 files)
      - terraform-azurerm-acr/examples/02-encryption/TST-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/TST-iac.tfvars
      ... and 50 more
    'rgrp-gwc-tst-iac-01' (4 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/TST-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/TST-iac.tfvars
      ... and 2 more
  PRD:
    'rgrp-weu-prd-iac-01' (52 files)
      - terraform-azurerm-acr/examples/02-encryption/PRD-iac.tfvars
      - terraform-azurerm-acr/examples/01-default/PRD-iac.tfvars
      ... and 50 more
    'rgrp-gwc-prd-iac-01' (2 files)
      - terraform-azurerm-ai-foundry/examples/02-hsm/PRD-iac.tfvars
      - terraform-azurerm-ai-search/examples/02-hsm/PRD-iac.tfvars

vnet_snet_name:
--------------------------------------------------------------------------------
  DEV:
    'aks' (9 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-demo01.tfvars
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-coeinf.tfvars
      ... and 7 more
  TST:
    'snet-weu-aks01' (3 files)
      - terraform-azurerm-aks/examples/01-default/TST-iac.tfvars
      - terraform-azurerm-aks/examples/12-aks-with-builtin-insights/TST-iac.tfvars
      ... and 1 more
    'snet-gwc-aks01' (1 files)
      - terraform-azurerm-aks/examples/01-default-GWC/TST-iac.tfvars
  PRD:
    'snet-weu-aks01' (3 files)
      - terraform-azurerm-aks/examples/01-default-GWC/PRD-iac.tfvars
      - terraform-azurerm-aks/examples/01-default/PRD-iac.tfvars
      ... and 1 more

vnet_vnet_name:
--------------------------------------------------------------------------------
  DEV:
    'vnet-weu-dev-demo01-01' (9 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-demo01.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/DEV-demo01.tfvars
      ... and 7 more
    'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01' (6 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-coeinf.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/DEV-coeinf.tfvars
      ... and 4 more
  TST:
    'vnet-weu-tst-iac-01' (8 files)
      - terraform-azurerm-aks/examples/11-aks-with-subnet/TST-iac.tfvars
      - terraform-azurerm-aks/examples/01-default/TST-iac.tfvars
      ... and 6 more
    'vnet-gwc-tst-iac-01' (1 files)
      - terraform-azurerm-aks/examples/01-default-GWC/TST-iac.tfvars
  PRD:
    'vnet-weu-prd-iac-01' (8 files)
      - terraform-azurerm-aks/examples/01-default-GWC/PRD-iac.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/PRD-iac.tfvars
      ... and 6 more

vnet_vnet_rgrp_name:
--------------------------------------------------------------------------------
  DEV:
    'rgrp-weu-dev-demo01-01' (9 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-demo01.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/DEV-demo01.tfvars
      ... and 7 more
    'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01' (6 files)
      - terraform-azurerm-aks/examples/01-default-GWC/DEV-coeinf.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/DEV-coeinf.tfvars
      ... and 4 more
  TST:
    'rgrp-weu-tst-iac-01' (8 files)
      - terraform-azurerm-aks/examples/11-aks-with-subnet/TST-iac.tfvars
      - terraform-azurerm-aks/examples/01-default/TST-iac.tfvars
      ... and 6 more
    'rgrp-gwc-tst-iac-01' (1 files)
      - terraform-azurerm-aks/examples/01-default-GWC/TST-iac.tfvars
  PRD:
    'rgrp-weu-prd-iac-01' (8 files)
      - terraform-azurerm-aks/examples/01-default-GWC/PRD-iac.tfvars
      - terraform-azurerm-aks/examples/11-aks-with-subnet/PRD-iac.tfvars
      ... and 6 more


====================================================================================================
SPECIAL FOCUS: ingress_nginx_ip (AKS)
====================================================================================================

DEV:
  '************'
    - terraform-azurerm-aks/examples/01-default-GWC/DEV-demo01.tfvars
    - terraform-azurerm-aks/examples/01-default/DEV-demo01.tfvars
  '**********'
    - terraform-azurerm-aks/examples/01-default-GWC/DEV-coeinf.tfvars
    - terraform-azurerm-aks/examples/16-aks-with-nodepool-subnet/DEV-coeinf.tfvars
    - terraform-azurerm-aks/examples/01-default/DEV-coeinf.tfvars

TST:
  '***********'
    - terraform-azurerm-aks/examples/01-default-GWC/TST-iac.tfvars
    - terraform-azurerm-aks/examples/01-default/TST-iac.tfvars

PRD:
  '***********'
    - terraform-azurerm-aks/examples/01-default-GWC/PRD-iac.tfvars
    - terraform-azurerm-aks/examples/01-default/PRD-iac.tfvars

