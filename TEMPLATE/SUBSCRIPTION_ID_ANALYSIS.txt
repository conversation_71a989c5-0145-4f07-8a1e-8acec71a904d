====================================================================================================
SUBSCRIPTION ID ANALYSIS
====================================================================================================

Total files with subscription info: 9

====================================================================================================
BY ENVIRONMENT
====================================================================================================

DEV:
  Unique subscription IDs: 2
    52953d73-d162-4fb2-a5d3-b004a6a84781: 2 files
    65b59cbd-8b9e-48aa-be61-11bf4864aa09: 3 files

PRD:
  Unique subscription IDs: 1
    100a24f7-de96-44df-9568-7802dcae2bef: 2 files

TST:
  Unique subscription IDs: 1
    4158adeb-d3de-4af6-86e9-b4b64dfdb52b: 2 files

====================================================================================================
BY PROJECT
====================================================================================================

demo01:
  Unique subscription IDs: 2
    52953d73-d162-4fb2-a5d3-b004a6a84781: 2 files
    65b59cbd-8b9e-48aa-be61-11bf4864aa09: 3 files

iac:
  Unique subscription IDs: 2
    100a24f7-de96-44df-9568-7802dcae2bef: 2 files
    4158adeb-d3de-4af6-86e9-b4b64dfdb52b: 2 files

====================================================================================================
BY ENVIRONMENT + PROJECT
====================================================================================================

DEV-demo01:
  Unique subscription IDs: 2
    52953d73-d162-4fb2-a5d3-b004a6a84781: 2 files
    65b59cbd-8b9e-48aa-be61-11bf4864aa09: 3 files

PRD-iac:
  Unique subscription IDs: 1
    100a24f7-de96-44df-9568-7802dcae2bef: 2 files

TST-iac:
  Unique subscription IDs: 1
    4158adeb-d3de-4af6-86e9-b4b64dfdb52b: 2 files

====================================================================================================
DETAILED FILE LIST
====================================================================================================

terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
  Environment: DEV
  Project: demo01
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 65b59cbd-8b9e-48aa-be61-11bf4864aa09

terraform-azurerm-datafactory/examples/04-vsts_config/DEV-demo01.tfvars
  Environment: DEV
  Project: demo01
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 65b59cbd-8b9e-48aa-be61-11bf4864aa09

terraform-azurerm-datafactory/examples/05-ssis_IR/DEV-demo01.tfvars
  Environment: DEV
  Project: demo01
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 65b59cbd-8b9e-48aa-be61-11bf4864aa09

terraform-azurerm-synapse-workspace/examples/01-default/DEV-demo01.tfvars
  Environment: DEV
  Project: demo01
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 52953d73-d162-4fb2-a5d3-b004a6a84781

terraform-azurerm-synapse-workspace/examples/02-github_repo/DEV-demo01.tfvars
  Environment: DEV
  Project: demo01
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 52953d73-d162-4fb2-a5d3-b004a6a84781

terraform-azurerm-synapse-workspace/examples/01-default/PRD-iac.tfvars
  Environment: PRD
  Project: iac
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 100a24f7-de96-44df-9568-7802dcae2bef

terraform-azurerm-synapse-workspace/examples/02-github_repo/PRD-iac.tfvars
  Environment: PRD
  Project: iac
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 100a24f7-de96-44df-9568-7802dcae2bef

terraform-azurerm-synapse-workspace/examples/01-default/TST-iac.tfvars
  Environment: TST
  Project: iac
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 4158adeb-d3de-4af6-86e9-b4b64dfdb52b

terraform-azurerm-synapse-workspace/examples/02-github_repo/TST-iac.tfvars
  Environment: TST
  Project: iac
  Region: westeurope
  Variable: keyvault_id
  Subscription ID: 4158adeb-d3de-4af6-86e9-b4b64dfdb52b

====================================================================================================
RECOMMENDATION
====================================================================================================

✗ Subscription ID is NOT consistent per environment!

✗ Subscription ID varies even within environment + project!

Action: This needs manual investigation!
