====================================================================================================
VARIABLE CONSOLIDATION REPORT
====================================================================================================

Analyzing which variables are actually the same thing...


====================================================================================================
GROUP: vnet_name
====================================================================================================

Files with MULTIPLE variables from this group:
----------------------------------------------------------------------------------------------------

terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-02'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-02'
    appgw_vnet_name                          = 'vnet-weu-dev-demo01-02'

terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    appgw_vnet_name                          = 'vnet-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    appgw_vnet_name                          = 'vnet-weu-tst-iac-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-02'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-02'
    appgw_vnet_name                          = 'vnet-weu-dev-demo01-02'

terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    appgw_vnet_name                          = 'vnet-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    pe_vnet_name                             = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    appgw_vnet_name                          = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    appgw_vnet_name                          = 'vnet-weu-tst-iac-01'

terraform-azurerm-application-gateway/examples/03-private_and_public/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-02'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-02'
    appgw_vnet_name                          = 'vnet-weu-dev-demo01-02'

terraform-azurerm-application-gateway/examples/02-private/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-02'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-02'
    appgw_vnet_name                          = 'vnet-weu-dev-demo01-02'

terraform-azurerm-application-gateway/examples/02-private/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    appgw_vnet_name                          = 'vnet-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    pe_vnet_name                             = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    appgw_vnet_name                          = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'

terraform-azurerm-application-gateway/examples/02-private/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    appgw_vnet_name                          = 'vnet-weu-tst-iac-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars:
  ✗ DIFFERENT VALUES:
    vnet_name                                = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    pe_vnet_name                             = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'

terraform-azurerm-databricks/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    prep_subnet_vnet_name                    = 'vnet-weu-prd-iac-01'

terraform-azurerm-databricks/examples/01-default/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    vnet_name                                = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    prep_subnet_vnet_name                    = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'

terraform-azurerm-databricks/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    prep_subnet_vnet_name                    = 'vnet-weu-tst-iac-01'

terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-gwc-tst-iac-01'
    vnet_name                                = 'vnet-gwc-tst-iac-01'
    prep_subnet_vnet_name                    = 'vnet-gwc-tst-iac-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'
    vnet_vnet_name                           = 'vnet-weu-dev-demo01-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    vnet_vnet_name                           = 'vnet-weu-prd-iac-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    vnet_vnet_name                           = 'vnet-weu-tst-iac-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'
    vnet_vnet_name                           = 'vnet-weu-dev-demo01-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    vnet_vnet_name                           = 'vnet-weu-prd-iac-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    vnet_vnet_name                           = 'vnet-weu-tst-iac-01'

terraform-azurerm-linux-function-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'
    vnet_vnet_name                           = 'vnet-weu-dev-demo01-01'

terraform-azurerm-linux-function-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    vnet_vnet_name                           = 'vnet-weu-prd-iac-01'

terraform-azurerm-linux-function-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    vnet_vnet_name                           = 'vnet-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    vnet_name                                = 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    vnet_name                                = 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'
    vnet_name                                = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'
    pe_vnet_name                             = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'

terraform-azurerm-linux-web-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    vnet_name                                = 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    vnet_name                                = 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    vnet_name                                = 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    vnet_name                                = 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'

terraform-azurerm-subnet/examples/02-PowerPlatformVnetSupp/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    primary_vnet_name                        = 'vnet-weu-dev-demo01-01'
    secondary_vnet_name                      = 'vnet-gwc-dev-demo01-01'

terraform-azurerm-windows-function-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'vnet-weu-dev-demo01-01'
    pe_vnet_name                             = 'vnet-weu-dev-demo01-01'
    vnet_vnet_name                           = 'vnet-weu-dev-demo01-01'

terraform-azurerm-windows-function-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-prd-iac-01'
    pe_vnet_name                             = 'vnet-weu-prd-iac-01'
    vnet_vnet_name                           = 'vnet-weu-prd-iac-01'

terraform-azurerm-windows-function-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'vnet-weu-tst-iac-01'
    pe_vnet_name                             = 'vnet-weu-tst-iac-01'
    vnet_vnet_name                           = 'vnet-weu-tst-iac-01'


----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

vnet_name                               : 167 files,  9 unique values
pe_vnet_name                            :  64 files,  6 unique values
vnet_vnet_name                          :  32 files,  5 unique values
appgw_vnet_name                         :  12 files,  4 unique values
prep_subnet_vnet_name                   :  34 files,  7 unique values
prep_storage_subnet_vnet_name           :   3 files,  3 unique values
primary_vnet_name                       :   1 files,  1 unique values
secondary_vnet_name                     :   1 files,  1 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✗ Variables in this group are NOT always aliases!

Examples where values differ:

  terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars:
    vnet_name                                = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
    pe_vnet_name                             = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'

  terraform-azurerm-subnet/examples/02-PowerPlatformVnetSupp/DEV-demo01.tfvars:
    primary_vnet_name                        = 'vnet-weu-dev-demo01-01'
    secondary_vnet_name                      = 'vnet-gwc-dev-demo01-01'

Action: These need separate handling!

====================================================================================================
GROUP: vnet_rgrp_name
====================================================================================================

Files with MULTIPLE variables from this group:
----------------------------------------------------------------------------------------------------

terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-tst-iac-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    pe_vnet_rgrp_name                        = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    appgw_vnet_rgrp_name                     = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'

terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-tst-iac-01'

terraform-azurerm-application-gateway/examples/03-private_and_public/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-application-gateway/examples/02-private/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-application-gateway/examples/02-private/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-prd-iac-01'

terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    pe_vnet_rgrp_name                        = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    appgw_vnet_rgrp_name                     = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'

terraform-azurerm-application-gateway/examples/02-private/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    appgw_vnet_rgrp_name                     = 'rgrp-weu-tst-iac-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars:
  ✗ DIFFERENT VALUES:
    vnet_rgrp_name                           = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    pe_vnet_rgrp_name                        = 'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01'

terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'

terraform-azurerm-databricks/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    prep_subnet_vnet_rgrp_name               = 'rgrp-weu-prd-iac-01'

terraform-azurerm-databricks/examples/01-default/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    vnet_rgrp_name                           = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    prep_subnet_vnet_rgrp_name               = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'

terraform-azurerm-databricks/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    prep_subnet_vnet_rgrp_name               = 'rgrp-weu-tst-iac-01'

terraform-azurerm-databricks/examples/02-cmk_hsm/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-gwc-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-gwc-tst-iac-01'
    prep_subnet_vnet_rgrp_name               = 'rgrp-gwc-tst-iac-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-prd-iac-01'

terraform-azurerm-datafactory/examples/03-integration_runtime/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-tst-iac-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-prd-iac-01'

terraform-azurerm-datafactory/examples/05-ssis_IR/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-tst-iac-01'

terraform-azurerm-linux-function-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-linux-function-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-prd-iac-01'

terraform-azurerm-linux-function-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    vnet_rgrp_name                           = 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/02-webapp_from_image/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    vnet_rgrp_name                           = 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/01-default/DEV-coeinf.tfvars:
  ✓ ALL SAME: 'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01'
    vnet_rgrp_name                           = 'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01'
    pe_vnet_rgrp_name                        = 'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01'

terraform-azurerm-linux-web-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    vnet_rgrp_name                           = 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'

terraform-azurerm-linux-web-app/examples/03-share_service_plan/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    vnet_rgrp_name                           = 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'

terraform-azurerm-recovery-services-vault/examples/02-vm-with-backup/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    vnet_rgrp_name                           = 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'

terraform-azurerm-subnet/examples/02-PowerPlatformVnetSupp/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    primary_vnet_rgrp_name                   = 'rgrp-weu-dev-demo01-01'
    secondary_vnet_rgrp_name                 = 'rgrp-gwc-dev-demo01-01'

terraform-azurerm-windows-function-app/examples/01-default/DEV-demo01.tfvars:
  ✓ ALL SAME: 'rgrp-weu-dev-demo01-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-dev-demo01-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-dev-demo01-01'

terraform-azurerm-windows-function-app/examples/01-default/PRD-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-prd-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-prd-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-prd-iac-01'

terraform-azurerm-windows-function-app/examples/01-default/TST-iac.tfvars:
  ✓ ALL SAME: 'rgrp-weu-tst-iac-01'
    pe_vnet_rgrp_name                        = 'rgrp-weu-tst-iac-01'
    vnet_vnet_rgrp_name                      = 'rgrp-weu-tst-iac-01'


----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

vnet_rgrp_name                          : 167 files,  9 unique values
pe_vnet_rgrp_name                       :  64 files,  5 unique values
vnet_vnet_rgrp_name                     :  32 files,  5 unique values
appgw_vnet_rgrp_name                    :  12 files,  4 unique values
prep_subnet_vnet_rgrp_name              :  34 files,  7 unique values
prep_storage_subnet_vnet_rgrp_name      :   3 files,  3 unique values
primary_vnet_rgrp_name                  :   1 files,  1 unique values
secondary_vnet_rgrp_name                :   1 files,  1 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✗ Variables in this group are NOT always aliases!

Examples where values differ:

  terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars:
    vnet_rgrp_name                           = 'OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01'
    pe_vnet_rgrp_name                        = 'otp-dd-coeinfdev-sub-dev-01-rg-westeu-01'

  terraform-azurerm-subnet/examples/02-PowerPlatformVnetSupp/DEV-demo01.tfvars:
    primary_vnet_rgrp_name                   = 'rgrp-weu-dev-demo01-01'
    secondary_vnet_rgrp_name                 = 'rgrp-gwc-dev-demo01-01'

Action: These need separate handling!

====================================================================================================
GROUP: subnet_name
====================================================================================================

Files with MULTIPLE variables from this group:
----------------------------------------------------------------------------------------------------

terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'pe01'
    appgw_snet_name                          = 'apgwpoc'

terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'pe01'
    appgw_snet_name                          = 'apgwpoc'

terraform-azurerm-application-gateway/examples/04-path_based_routing/PRD-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

terraform-azurerm-application-gateway/examples/04-path_based_routing/DEV-coeinf.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'privateendpoints'
    appgw_snet_name                          = 'snet-test2'

terraform-azurerm-application-gateway/examples/04-path_based_routing/TST-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

terraform-azurerm-application-gateway/examples/03-private_and_public/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'pe01'
    appgw_snet_name                          = 'apgwpoc'

terraform-azurerm-application-gateway/examples/02-private/DEV-demo01.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'pe01'
    appgw_snet_name                          = 'apgwpoc'

terraform-azurerm-application-gateway/examples/02-private/PRD-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

terraform-azurerm-application-gateway/examples/02-private/DEV-coeinf.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'privateendpoints'
    appgw_snet_name                          = 'snet-test2'

terraform-azurerm-application-gateway/examples/02-private/TST-iac.tfvars:
  ✗ DIFFERENT VALUES:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'


----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

subnet_name                             :  10 files,  3 unique values
snet_name                               :  98 files,  7 unique values
pe_snet_name                            :  64 files,  4 unique values
vnet_snet_name                          :  16 files,  3 unique values
appgw_snet_name                         :  12 files,  3 unique values
prep_subnet_name                        :  34 files,  4 unique values
prep_storage_subnet_name                :   3 files,  2 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✗ Variables in this group are NOT always aliases!

Examples where values differ:

  terraform-azurerm-application-gateway/examples/01-default/DEV-demo01.tfvars:
    pe_snet_name                             = 'pe01'
    appgw_snet_name                          = 'apgwpoc'

  terraform-azurerm-application-gateway/examples/01-default/PRD-iac.tfvars:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

  terraform-azurerm-application-gateway/examples/01-default/TST-iac.tfvars:
    pe_snet_name                             = 'snet-weu-pe01'
    appgw_snet_name                          = 'snet-weu-compute04'

Action: These need separate handling!

====================================================================================================
GROUP: key_vault_name
====================================================================================================

No files have multiple variables from this group.

----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

kvau_name                               :  49 files,  5 unique values
kv_name                                 :  34 files,  5 unique values
keyvault_name                           :   3 files,  3 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✓ All variables in this group are TRUE ALIASES (same values when present together)

Canonical variable: key_vault_name
Most used variable: kvau_name (49 files)

Action: Keep only 'key_vault_name' in defaults.yaml
        Map all others as aliases:
        - kvau_name -> ${{ variables.key_vault_name }}
        - kv_name -> ${{ variables.key_vault_name }}
        - keyvault_name -> ${{ variables.key_vault_name }}

====================================================================================================
GROUP: kvau_rgrp_name
====================================================================================================

No files have multiple variables from this group.

----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

kvau_rgrp_name                          :  52 files,  6 unique values
kv_rgrp_name                            :  33 files,  5 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✓ All variables in this group are TRUE ALIASES (same values when present together)

Canonical variable: kvau_rgrp_name
Most used variable: kvau_rgrp_name (52 files)

Action: Keep only 'kvau_rgrp_name' in defaults.yaml
        Map all others as aliases:
        - kv_rgrp_name -> ${{ variables.kvau_rgrp_name }}

====================================================================================================
GROUP: rtbl_name
====================================================================================================

No files have multiple variables from this group.

----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

rtbl_name                               :  83 files,  8 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✓ All variables in this group are TRUE ALIASES (same values when present together)

Canonical variable: rtbl_name
Most used variable: rtbl_name (83 files)

Action: Keep only 'rtbl_name' in defaults.yaml
        Map all others as aliases:

====================================================================================================
GROUP: rtbl_rgrp_name
====================================================================================================

No files have multiple variables from this group.

----------------------------------------------------------------------------------------------------
Usage statistics:
----------------------------------------------------------------------------------------------------

rtbl_rgrp_name                          :  65 files,  5 unique values
rtbl_rg_name                            :   4 files,  3 unique values

----------------------------------------------------------------------------------------------------
RECOMMENDATION:
----------------------------------------------------------------------------------------------------

✓ All variables in this group are TRUE ALIASES (same values when present together)

Canonical variable: rtbl_rgrp_name
Most used variable: rtbl_rgrp_name (65 files)

Action: Keep only 'rtbl_rgrp_name' in defaults.yaml
        Map all others as aliases:
        - rtbl_rg_name -> ${{ variables.rtbl_rgrp_name }}
