# Subscription ID elemzés és javaslat

## 🔍 Elemzés eredménye

### Hasz<PERSON>lat a tfvars fájlo<PERSON>ban

**Összesen**: 9 fájl használja a `keyvault_id` változót (ami tartalmazza a subscription ID-t)

### Subscription ID-k környezetenként

| Környezet | Subscription ID-k száma | Értékek |
|-----------|-------------------------|---------|
| **DEV** | 2 | `65b59cbd-8b9e-48aa-be61-11bf4864aa09` (3 fájl)<br>`52953d73-d162-4fb2-a5d3-b004a6a84781` (2 fájl) |
| **TST** | 1 | `4158adeb-d3de-4af6-86e9-b4b64dfdb52b` (2 fájl) |
| **PRD** | 1 | `100a24f7-de96-44df-9568-7802dcae2bef` (2 fájl) |

### ⚠️ Probléma: DEV-demo01 esetén 2 különböző subscription ID!

**Részletek:**

#### DataFactory (3 fájl)
```
Subscription ID: 65b59cbd-8b9e-48aa-be61-11bf4864aa09
Key Vault: kvau-weu-dev-DEVD000001
Resource Group: rgrp-weu-dev-demo01-01

Fájlok:
- terraform-azurerm-datafactory/examples/03-integration_runtime/DEV-demo01.tfvars
- terraform-azurerm-datafactory/examples/04-vsts_config/DEV-demo01.tfvars
- terraform-azurerm-datafactory/examples/05-ssis_IR/DEV-demo01.tfvars
```

#### Synapse (2 fájl)
```
Subscription ID: 52953d73-d162-4fb2-a5d3-b004a6a84781
Key Vault: kvau-weu-dev-shared
Resource Group: rgrp-weu-dev-kvau01

Fájlok:
- terraform-azurerm-synapse-workspace/examples/01-default/DEV-demo01.tfvars
- terraform-azurerm-synapse-workspace/examples/02-github_repo/DEV-demo01.tfvars
```

### 🤔 Miért van 2 különböző subscription ID?

A különbség a **Key Vault**-ban van:
- **DataFactory**: `kvau-weu-dev-DEVD000001` (projekt-specifikus KV)
- **Synapse**: `kvau-weu-dev-shared` (shared KV)

Ez azt jelenti, hogy:
1. **Különböző Key Vault-ok** vannak használatban
2. Ezek **különböző subscription-ökben** vannak
3. A `keyvault_id` változó **nem csak a subscription ID-t tartalmazza**, hanem a teljes Key Vault resource ID-t!

## 📋 Következtetés

### ❌ NEM lehet környezetenként fix érték

A subscription ID **NEM konzisztens** még környezeten belül sem, mert:
- DEV-demo01 esetén 2 különböző subscription ID van
- Ez azért van, mert különböző Key Vault-ok vannak használatban

### ❓ Kérdés: Mi a valódi használat?

A `keyvault_id` változó **teljes resource ID**, nem csak subscription ID:
```
/subscriptions/{subscription-id}/resourceGroups/{rg-name}/providers/Microsoft.KeyVault/vaults/{kv-name}
```

**Kérdések:**
1. Valóban szükség van a teljes `keyvault_id`-ra, vagy csak a subscription ID-ra?
2. Ha csak subscription ID kell, akkor melyik subscription-t használjuk?
3. Ha teljes keyvault_id kell, akkor ez **számítható** a meglévő változókból?

## 💡 Javaslatok

### Opció 1: Subscription ID környezetenként (egyszerűsített)

Ha csak a **subscription ID** kell (nem a teljes keyvault_id), akkor:

**Feltételezés**: A DEV-demo01 esetén a DataFactory subscription az "elsődleges"

```yaml
# tooling/env/overrides/environment/dev.yaml
variables:
  - name: subscription_id
    value: "65b59cbd-8b9e-48aa-be61-11bf4864aa09"

# tooling/env/overrides/environment/tst.yaml
variables:
  - name: subscription_id
    value: "4158adeb-d3de-4af6-86e9-b4b64dfdb52b"

# tooling/env/overrides/environment/prd.yaml
variables:
  - name: subscription_id
    value: "100a24f7-de96-44df-9568-7802dcae2bef"
```

**Probléma**: A Synapse-nek más subscription kell DEV-ben!

### Opció 2: Subscription ID projekt + környezetenként

```yaml
# tooling/env/overrides/combined/dev-demo01.yaml
variables:
  - name: subscription_id
    value: "65b59cbd-8b9e-48aa-be61-11bf4864aa09"  # DataFactory subscription
  
  # Vagy külön változók:
  - name: datafactory_subscription_id
    value: "65b59cbd-8b9e-48aa-be61-11bf4864aa09"
  
  - name: synapse_subscription_id
    value: "52953d73-d162-4fb2-a5d3-b004a6a84781"

# tooling/env/overrides/combined/tst-iac.yaml
variables:
  - name: subscription_id
    value: "4158adeb-d3de-4af6-86e9-b4b64dfdb52b"

# tooling/env/overrides/combined/prd-iac.yaml
variables:
  - name: subscription_id
    value: "100a24f7-de96-44df-9568-7802dcae2bef"
```

**Probléma**: Bonyolult, ha minden modulnak más subscription kell.

### Opció 3: Keyvault ID számítása (ajánlott)

Ha a `keyvault_id` változó kell (teljes resource ID), akkor **számítható**:

```yaml
# defaults.yaml-ban:
- name: keyvault_id
  value: "/subscriptions/${{ variables.subscription_id }}/resourceGroups/${{ variables.kvau_rgrp_name }}/providers/Microsoft.KeyVault/vaults/${{ variables.key_vault_name }}"
```

**Előfeltétel**: Kell egy `subscription_id` változó!

**Probléma**: A Synapse más Key Vault-ot használ (`kvau-weu-dev-shared` vs `kvau-weu-dev-DEVD000001`)

### Opció 4: Keyvault ID modul-specifikus (legegyszerűbb)

Mivel csak **2 modul** használja (DataFactory és Synapse), és **különböző Key Vault-okat** használnak:

**Megoldás**: A `keyvault_id` maradjon a **tfvars.template** fájlban!

```yaml
# terraform-azurerm-datafactory/tfvars.template
keyvault_id = "/subscriptions/{subscription-id}/resourceGroups/{kvau-rg}/providers/Microsoft.KeyVault/vaults/{kv-name}"

# terraform-azurerm-synapse-workspace/tfvars.template
keyvault_id = "/subscriptions/{subscription-id}/resourceGroups/{kvau-rg}/providers/Microsoft.KeyVault/vaults/{kv-name}"
```

**Előny**: 
- Egyszerű
- Nincs szükség subscription_id változóra a composer-ben
- Minden modul a saját Key Vault-ját használhatja

**Hátrány**:
- Manuálisan kell kitölteni

## 🎯 Végső javaslat

### 1. NE adjuk hozzá a subscription_id-t a composer-hez

**Indokok:**
- Csak 2 modul használja (DataFactory, Synapse)
- Még környezeten belül is eltér (DEV-demo01: 2 különböző subscription)
- A `keyvault_id` teljes resource ID, nem csak subscription ID
- Különböző Key Vault-ok vannak használatban

### 2. A keyvault_id maradjon modul-specifikus

**Megoldás:**
- tfvars.template fájlban definiáljuk
- Minden modul a saját Key Vault-ját használja
- Manuálisan kell kitölteni (de csak 2 modulnál)

### 3. HA mégis kell subscription_id a jövőben

**Akkor**: Környezetenként adjuk hozzá:

```yaml
# tooling/env/overrides/environment/dev.yaml
variables:
  - name: subscription_id
    value: "65b59cbd-8b9e-48aa-be61-11bf4864aa09"  # Elsődleges DEV subscription

# tooling/env/overrides/environment/tst.yaml
variables:
  - name: subscription_id
    value: "4158adeb-d3de-4af6-86e9-b4b64dfdb52b"

# tooling/env/overrides/environment/prd.yaml
variables:
  - name: subscription_id
    value: "100a24f7-de96-44df-9568-7802dcae2bef"
```

**Megjegyzés**: Ez az "elsődleges" subscription ID lenne, de nem minden modul használná ugyanazt!

## 📊 Összefoglalás

| Kérdés | Válasz |
|--------|--------|
| **Subscription ID fix érték?** | ❌ NEM - még környezeten belül is eltér! |
| **Subscription ID környezetenként?** | ⚠️ Részben - de vannak kivételek (pl. Synapse) |
| **Subscription ID paraméter kell?** | ❌ NEM ajánlott - csak 2 modul használja, eltérően |
| **Keyvault ID számítható?** | ⚠️ Részben - de különböző KV-k vannak használatban |
| **Ajánlott megoldás** | ✅ Maradjon modul-specifikus (tfvars.template) |

---

**Készítette**: Augment AI  
**Dátum**: 2025-11-04  
**Verzió**: 1.0

