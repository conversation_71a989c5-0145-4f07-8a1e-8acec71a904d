================================================================================
VARIABLES MISSING FROM DEFAULTS.YAML
================================================================================

These variables are used in tfvars files but not defined in defaults.yaml
Total missing: 67

Sorted by frequency (most common first):
--------------------------------------------------------------------------------

project: 308 occurrences
environment: 294 occurrences
region: 293 occurrences
subsidiary: 176 occurrences
vnet_rgrp_name: 168 occurrences
vnet_name: 168 occurrences
snet_name: 98 occurrences
rtbl_name: 83 occurrences
rtbl_rgrp_name: 65 occurrences
pe_vnet_rgrp_name: 64 occurrences
pe_snet_name: 64 occurrences
pe_vnet_name: 64 occurrences
kvau_rgrp_name: 52 occurrences
kvau_name: 49 occurrences
kv_rgrp_name: 35 occurrences
kv_name: 35 occurrences
prep_subnet_vnet_name: 34 occurrences
prep_subnet_vnet_rgrp_name: 34 occurrences
prep_subnet_name: 34 occurrences
vnet_vnet_name: 32 occurrences
vnet_vnet_rgrp_name: 32 occurrences
vnet_snet_name: 16 occurrences
address_prefix: 15 occurrences
resource_name_suffix: 14 occurrences
appgw_snet_name: 12 occurrences
appgw_vnet_rgrp_name: 12 occurrences
appgw_vnet_name: 12 occurrences
strj_streaming_units: 9 occurrences
ingress_nginx_ip: 9 occurrences
mhsm_umid_rgrp_name: 9 occurrences
keyvault_id: 9 occurrences
mhsm_umid_name: 9 occurrences
mhsm_key: 9 occurrences
appgw_private_ip: 7 occurrences
synapse_role_assignment_principal_id: 6 occurrences
aad_admin_name: 6 occurrences
puip_rgrp_name: 5 occurrences
strc_capacity: 5 occurrences
puip_name: 5 occurrences
rtbl_rg_name: 4 occurrences
keyvault_name: 3 occurrences
first_subnet: 3 occurrences
enable_public_ip: 3 occurrences
snet_prefix: 3 occurrences
lb_private_ip: 3 occurrences
prep_storage_subnet_vnet_rgrp_name: 3 occurrences
prep_storage_subnet_name: 3 occurrences
lb_backend_ips: 3 occurrences
prep_storage_subnet_vnet_name: 3 occurrences
lb_frontend_ip1: 3 occurrences
second_subnets: 3 occurrences
remote_state_stac_name: 2 occurrences
remote_state_cont_name: 2 occurrences
apgw_public_ip_id: 2 occurrences
remote_state_key: 2 occurrences
remote_state_rgrp_name: 2 occurrences
secondary_vnet_rgrp_name: 1 occurrences
secondary_subnet_prefix: 1 occurrences
secondary_vnet_name: 1 occurrences
owner: 1 occurrences
primary_vnet_name: 1 occurrences
primary_vnet_rgrp_name: 1 occurrences
enterprise_policy_location: 1 occurrences
primary_subnet_prefix: 1 occurrences
primary_region: 1 occurrences
secondary_region: 1 occurrences
usernodepool_subnet_address_prefix: 1 occurrences


================================================================================
CATEGORIZED MISSING VARIABLES
================================================================================


Network - VNet:
----------------------------------------
  - vnet_rgrp_name (168 occurrences)
  - vnet_name (168 occurrences)
  - pe_vnet_rgrp_name (64 occurrences)
  - pe_vnet_name (64 occurrences)
  - vnet_vnet_name (32 occurrences)
  - vnet_vnet_rgrp_name (32 occurrences)
  - vnet_snet_name (16 occurrences)
  - appgw_vnet_rgrp_name (12 occurrences)
  - appgw_vnet_name (12 occurrences)
  - secondary_vnet_rgrp_name (1 occurrences)
  - secondary_vnet_name (1 occurrences)
  - primary_vnet_name (1 occurrences)
  - primary_vnet_rgrp_name (1 occurrences)

Network - Subnet:
----------------------------------------
  - snet_name (98 occurrences)
  - pe_snet_name (64 occurrences)
  - prep_subnet_vnet_name (34 occurrences)
  - prep_subnet_vnet_rgrp_name (34 occurrences)
  - prep_subnet_name (34 occurrences)
  - appgw_snet_name (12 occurrences)
  - first_subnet (3 occurrences)
  - snet_prefix (3 occurrences)
  - prep_storage_subnet_vnet_rgrp_name (3 occurrences)
  - prep_storage_subnet_name (3 occurrences)
  - prep_storage_subnet_vnet_name (3 occurrences)
  - second_subnets (3 occurrences)
  - secondary_subnet_prefix (1 occurrences)
  - primary_subnet_prefix (1 occurrences)
  - usernodepool_subnet_address_prefix (1 occurrences)

Network - Route Table:
----------------------------------------
  - rtbl_name (83 occurrences)
  - rtbl_rgrp_name (65 occurrences)
  - rtbl_rg_name (4 occurrences)

Network - Application Gateway:
----------------------------------------
  - appgw_private_ip (7 occurrences)
  - apgw_public_ip_id (2 occurrences)

Network - Load Balancer:
----------------------------------------
  - lb_private_ip (3 occurrences)
  - lb_backend_ips (3 occurrences)
  - lb_frontend_ip1 (3 occurrences)

Network - Other:
----------------------------------------
  - address_prefix (15 occurrences)
  - ingress_nginx_ip (9 occurrences)
  - synapse_role_assignment_principal_id (6 occurrences)
  - puip_rgrp_name (5 occurrences)
  - puip_name (5 occurrences)
  - enable_public_ip (3 occurrences)

Key Vault:
----------------------------------------
  - kvau_rgrp_name (52 occurrences)
  - kvau_name (49 occurrences)
  - kv_rgrp_name (35 occurrences)
  - kv_name (35 occurrences)
  - keyvault_id (9 occurrences)
  - keyvault_name (3 occurrences)

HSM/Encryption:
----------------------------------------
  - mhsm_umid_rgrp_name (9 occurrences)
  - mhsm_umid_name (9 occurrences)
  - mhsm_key (9 occurrences)

Storage:
----------------------------------------
  - remote_state_stac_name (2 occurrences)

Database/Synapse:
----------------------------------------
  - aad_admin_name (6 occurrences)

Stream Analytics:
----------------------------------------
  - strj_streaming_units (9 occurrences)
  - strc_capacity (5 occurrences)

Remote State:
----------------------------------------
  - remote_state_cont_name (2 occurrences)
  - remote_state_key (2 occurrences)
  - remote_state_rgrp_name (2 occurrences)

Other:
----------------------------------------
  - project (308 occurrences)
  - environment (294 occurrences)
  - region (293 occurrences)
  - subsidiary (176 occurrences)
  - resource_name_suffix (14 occurrences)
  - owner (1 occurrences)
  - enterprise_policy_location (1 occurrences)
  - primary_region (1 occurrences)
  - secondary_region (1 occurrences)
