# TFVARS Változók Elemzése - Összefoglaló

## Áttekintés

Ez az elemzés végigvizsgálta az összes `terraform-azurerm-*` kezdetű repository `examples` mappáiban található `.tfvars` és `.tfvars.template` fáj<PERSON>kat, és összehasonlította a bennük található változókat a `tooling/env/defaults.yaml` fájlban definiált változókkal.

## Statisztikák

- **Vizsgált repository-k száma**: 60 terraform-azurerm-* repository
- **Talált egyedi változók száma**: 69
- **defaults.yaml-ban definiált változók**: 9
- **Közös változók**: 2 (cloud, subnet_name)
- **Hiányzó változók a defaults.yaml-ból**: 67

## Jelenleg a defaults.yaml-ban lévő változók

1. `appCode` - ❌ Nem használt a tfvars fájlokban
2. `cloud` - ✅ Használt (294 előfordulás)
3. `keyVaultCommaSeparatedSecretNames` - ❌ Nem használt
4. `keyVaultNamePattern` - ❌ Nem használt
5. `projectSuffix` - ❌ Nem használt
6. `serviceConnectionPattern` - ❌ Nem használt
7. `storageAccountContainerName` - ❌ Nem használt
8. `subnet_name` - ✅ Használt (11 előfordulás)
9. `terraformVersion` - ❌ Nem használt

## Leggyakrabban használt változók (hiányzik a defaults.yaml-ból)

### Alapvető konvenciók (300+ előfordulás)
1. **project** - 308 előfordulás
2. **environment** - 294 előfordulás
3. **region** - 293 előfordulás
4. **subsidiary** - 176 előfordulás

### Hálózati erőforrások (100+ előfordulás)
5. **vnet_name** - 168 előfordulás
6. **vnet_rgrp_name** - 168 előfordulás
7. **snet_name** - 98 előfordulás

### Route Table (60+ előfordulás)
8. **rtbl_name** - 83 előfordulás
9. **rtbl_rgrp_name** - 65 előfordulás

### Private Endpoint (60+ előfordulás)
10. **pe_vnet_name** - 64 előfordulás
11. **pe_vnet_rgrp_name** - 64 előfordulás
12. **pe_snet_name** - 64 előfordulás

### Key Vault (35+ előfordulás)
13. **kvau_name** - 49 előfordulás
14. **kvau_rgrp_name** - 52 előfordulás
15. **kv_name** - 35 előfordulás
16. **kv_rgrp_name** - 35 előfordulás

## Kategorizált hiányzó változók

### 1. Alapvető Konvenciók (Other kategória)
- `project` (308)
- `environment` (294)
- `region` (293)
- `subsidiary` (176)
- `resource_name_suffix` (14)
- `owner` (1)
- `enterprise_policy_location` (1)
- `primary_region` (1)
- `secondary_region` (1)

### 2. Hálózat - VNet
- `vnet_name` (168)
- `vnet_rgrp_name` (168)
- `pe_vnet_name` (64)
- `pe_vnet_rgrp_name` (64)
- `vnet_vnet_name` (32)
- `vnet_vnet_rgrp_name` (32)
- `vnet_snet_name` (16)
- `appgw_vnet_name` (12)
- `appgw_vnet_rgrp_name` (12)
- `primary_vnet_name` (1)
- `primary_vnet_rgrp_name` (1)
- `secondary_vnet_name` (1)
- `secondary_vnet_rgrp_name` (1)

### 3. Hálózat - Subnet
- `snet_name` (98)
- `pe_snet_name` (64)
- `prep_subnet_name` (34)
- `prep_subnet_vnet_name` (34)
- `prep_subnet_vnet_rgrp_name` (34)
- `appgw_snet_name` (12)
- `snet_prefix` (3)
- `first_subnet` (3)
- `second_subnets` (3)
- `prep_storage_subnet_name` (3)
- `prep_storage_subnet_vnet_name` (3)
- `prep_storage_subnet_vnet_rgrp_name` (3)
- `primary_subnet_prefix` (1)
- `secondary_subnet_prefix` (1)
- `usernodepool_subnet_address_prefix` (1)

### 4. Hálózat - Route Table
- `rtbl_name` (83)
- `rtbl_rgrp_name` (65)
- `rtbl_rg_name` (4)

### 5. Hálózat - Application Gateway
- `appgw_private_ip` (7)
- `apgw_public_ip_id` (2)

### 6. Hálózat - Load Balancer
- `lb_private_ip` (3)
- `lb_backend_ips` (3)
- `lb_frontend_ip1` (3)

### 7. Hálózat - Egyéb
- `address_prefix` (15)
- `ingress_nginx_ip` (9)
- `puip_name` (5)
- `puip_rgrp_name` (5)
- `enable_public_ip` (3)

### 8. Key Vault
- `kvau_rgrp_name` (52)
- `kvau_name` (49)
- `kv_rgrp_name` (35)
- `kv_name` (35)
- `keyvault_id` (9)
- `keyvault_name` (3)

### 9. HSM/Encryption
- `mhsm_umid_name` (9)
- `mhsm_umid_rgrp_name` (9)
- `mhsm_key` (9)

### 10. Database/Synapse
- `aad_admin_name` (6)
- `synapse_role_assignment_principal_id` (6)

### 11. Stream Analytics
- `strj_streaming_units` (9)
- `strc_capacity` (5)

### 12. Remote State
- `remote_state_rgrp_name` (2)
- `remote_state_stac_name` (2)
- `remote_state_cont_name` (2)
- `remote_state_key` (2)

## Javaslatok

### Prioritás 1: Alapvető változók (azonnal hozzáadandók)
Ezek a változók szinte minden tfvars fájlban megjelennek:

```yaml
- name: project
  value: ${{ parameters.project }}

- name: environment
  value: ${{ parameters.environment }}

- name: region
  value: ${{ parameters.region }}

- name: subsidiary
  value: ${{ parameters.subsidiary }}
```

### Prioritás 2: Hálózati alapok (100+ előfordulás)
```yaml
- name: vnet_name
  value: "vnet-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"

- name: vnet_rgrp_name
  value: "rgrp-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"

- name: snet_name
  value: "snet-${{ variables.regionShort }}-pe01"
```

### Prioritás 3: Private Endpoint változók (60+ előfordulás)
```yaml
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}

- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: pe_snet_name
  value: ${{ variables.snet_name }}
```

### Prioritás 4: Route Table (60+ előfordulás)
```yaml
- name: rtbl_name
  value: "rtbl-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-core"

- name: rtbl_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

### Prioritás 5: Key Vault (35+ előfordulás)
```yaml
- name: kvau_name
  value: "${{ parameters.project }}-${{ parameters.environment }}-shared01"

- name: kvau_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: kv_name
  value: ${{ variables.kvau_name }}

- name: kv_rgrp_name
  value: ${{ variables.kvau_rgrp_name }}
```

## Következő lépések

1. **Döntés a változók hozzáadásáról**: Mely változókat szeretnéd hozzáadni a defaults.yaml-hoz?
2. **Naming convention meghatározása**: Egységes elnevezési konvenció kialakítása a változókhoz
3. **Region mapping**: Region rövidítések (weu, gwc, stb.) mapping-jének definiálása
4. **Override stratégia**: Mely változók legyenek felülírhatók environment/region/project szinten?
5. **Dokumentáció**: A változók használatának dokumentálása

## Megjegyzések

- A `subnet_name` változó már létezik a defaults.yaml-ban, de a tfvars fájlokban inkább a `snet_name` elnevezést használják
- Sok változó különböző prefix-ekkel jelenik meg (pl. `vnet_name`, `pe_vnet_name`, `appgw_vnet_name`), ami azt jelzi, hogy különböző célokra használják őket
- A Key Vault változók két formában jelennek meg: `kvau_*` és `kv_*` - érdemes tisztázni a különbséget

