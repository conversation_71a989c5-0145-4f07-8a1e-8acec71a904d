# Korrigált elemzés - Válaszok a kérdésekre

## 📋 Kérdések és válaszok

### ❓ Q1: Az snet_name és a subnet_name nem ugyanaz?

**Válasz**: ✅ **IGEN, ugyanaz!**

**Bizonyíték:**
- `snet_name`: 98 használat, 7 egyedi érték
  - Leggyakoribb: `snet-weu-pe01` (50 fájl)
  - DEV-ben: `privateendpoints`, `privateendpoints2`, `snet-test1`
  - TST/PRD-ben: `snet-weu-pe01`, `snet-weu-compute01`

- `subnet_name`: 10 használat, 3 egyedi érték
  - Ugyanazok az értékek: `snet-weu-pe01`, `privateendpoints`, `aks`

**Következtetés**: Ezek **alias-ok**, ugyanazt jelentik. A composer-ben már van `subnet_name`, tehát:
```yaml
# defaults.yaml-ban már van:
- name: subnet_name
  value: "snet-weu-pe01"  # TST/PRD
  
# Hozzáadandó alias:
- name: snet_name
  value: ${{ variables.subnet_name }}
```

---

### ❓ Q2: A kv_name meg a keyvault_name?

**Válasz**: ✅ **IGEN, ugyanaz!**

**Bizonyíték:**
- `kv_name`: 34 használat, 5 egyedi érték
  - DEV: `kvau-weu-dev-DEVD000001`, `kvau-gwc-dev-RITM2480573`
  - TST: `iac-tst-shared01`
  - PRD: `iac-prd-shared01`

- `keyvault_name`: 3 használat, 3 egyedi érték
  - DEV: `kvau-weu-dev-DEVD000001`
  - TST: `iac-tst-shared01`
  - PRD: `iac-prd-shared01`

**Következtetés**: Ezek **alias-ok**, ugyanazt jelentik. A composer-ben már van `key_vault_name`, tehát:
```yaml
# defaults.yaml-ban már van:
- name: key_vault_name
  value: ...
  
# Hozzáadandó alias-ok:
- name: kv_name
  value: ${{ variables.key_vault_name }}
  
- name: keyvault_name
  value: ${{ variables.key_vault_name }}
```

---

### ❓ Q3: A subsidiary értéke nem fix-en 'otphq' mindenhol?

**Válasz**: ✅ **IGEN, mindig 'otphq'!**

**Bizonyíték:**
- `subsidiary`: 176 használat, **1 egyedi érték**
  - `otphq` - 100% (176 fájl)

**Következtetés**: **NEM kell parameter**, fix érték!
```yaml
# defaults.yaml-ba:
- name: subsidiary
  value: "otphq"  # Fix érték, nem parameter!
```

---

### ❓ Q4: A pe_vnet_name biztosan azonos a vnet_name-el?

**Válasz**: ⚠️ **MAJDNEM mindig, de van 1 kivétel!**

**Bizonyíték:**
- 18 fájlban van mindkét változó
- 17 fájlban **UGYANAZ** ✓
- 1 fájlban **KÜLÖNBÖZIK** ✗
  - `terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars`
  - `pe_vnet_name = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'` (kisbetűs)
  - `vnet_name = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'` (NAGYBETŰS)
  - Ez valószínűleg **elírás**!

**Következtetés**: Biztonságosan használhatjuk alias-ként:
```yaml
# defaults.yaml-ba:
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}
  
- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

---

### ❓ Q5: AKS esetén az ingress_nginx_ip környezetenként eltér. Központilag vagy template-ben?

**Válasz**: ⚠️ **Környezetenként ÉS projektenként is eltér!**

**Bizonyíték:**
```
DEV:
  '************' - demo01 projekt (2 fájl)
  '**********'   - coeinf projekt (3 fájl)

TST:
  '***********'  - iac projekt (2 fájl)

PRD:
  '***********'  - iac projekt (2 fájl)
```

**Következtetés**: Ez **projekt-specifikus** változó, ami környezetenként is változik!

**Megoldás**: Environment + Project override kombinációval:
```yaml
# tooling/env/overrides/combined/dev-demo01.yaml
variables:
  - name: ingress_nginx_ip
    value: "************"

# tooling/env/overrides/combined/dev-coeinf.yaml
variables:
  - name: ingress_nginx_ip
    value: "**********"

# tooling/env/overrides/combined/tst-iac.yaml
variables:
  - name: ingress_nginx_ip
    value: "***********"

# tooling/env/overrides/combined/prd-iac.yaml
variables:
  - name: ingress_nginx_ip
    value: "***********"
```

---

## 🔍 Más környezetenként változó értékek

Az elemzés **44 változót** talált, amelyek környezetenként eltérnek. Ezek közül a legfontosabbak:

### 1. **Hálózati címek (IP-k, CIDR-ek)**

| Változó | DEV | TST | PRD | Megoldás |
|---------|-----|-----|-----|----------|
| `ingress_nginx_ip` | 10.94.x.x | *********** | *********** | Combined override |
| `appgw_private_ip` | 10.94.x.x | 10.94.68.x | 10.95.28.x | Combined override |
| `first_subnet` | ************/28 | **********/28 | **********/28 | Combined override |
| `lb_private_ip` | Változó | Változó | Változó | Combined override |

**Következtetés**: Ezek **projekt + környezet specifikusak**, combined override-ba!

### 2. **Key Vault ID-k**

| Változó | Példa érték |
|---------|-------------|
| `keyvault_id` | `/subscriptions/{sub-id}/resourceGroups/{rg}/providers/Microsoft.KeyVault/vaults/{kv-name}` |

**Következtetés**: Ez **számítható** a meglévő változókból:
```yaml
- name: keyvault_id
  value: "/subscriptions/${{ variables.subscriptionId }}/resourceGroups/${{ variables.kvau_rgrp_name }}/providers/Microsoft.KeyVault/vaults/${{ variables.key_vault_name }}"
```

**PROBLÉMA**: Nincs `subscriptionId` változó a composer-ben! Ezt hozzá kell adni!

### 3. **Application Gateway változók**

| Változó | DEV | TST | PRD |
|---------|-----|-----|-----|
| `appgw_vnet_name` | vnet-weu-dev-demo01-02 | vnet-weu-tst-iac-01 | vnet-weu-prd-iac-01 |
| `appgw_vnet_rgrp_name` | rgrp-weu-dev-demo01-01 | rgrp-weu-tst-iac-01 | rgrp-weu-prd-iac-01 |
| `appgw_snet_name` | apgwpoc | snet-weu-compute04 | snet-weu-compute04 |
| `appgw_private_ip` | Változó | Változó | Változó |

**Következtetés**: Ezek **modul-specifikusak**, de követik a standard naming-et:
```yaml
# defaults.yaml-ba (ha általánosan használt lenne):
- name: appgw_vnet_name
  value: ${{ variables.vnet_name }}  # Vagy külön VNet, ha kell
  
- name: appgw_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
  
- name: appgw_snet_name
  value: "snet-${{ variables.region }}-compute04"  # Vagy appgw01
```

**DE**: Mivel csak 1 repo használja (application-gateway), inkább **tfvars.template**!

### 4. **VNet alias-ok (vnet_vnet_name)**

| Változó | Használat | Repo-k |
|---------|-----------|--------|
| `vnet_vnet_name` | 32 használat | 4 repo (aks, datafactory, function-app) |
| `vnet_vnet_rgrp_name` | 32 használat | 4 repo |

**Értékek**: Ugyanazok, mint `vnet_name` és `vnet_rgrp_name`!

**Következtetés**: Ezek **alias-ok**:
```yaml
# defaults.yaml-ba:
- name: vnet_vnet_name
  value: ${{ variables.vnet_name }}
  
- name: vnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

---

## 📊 Korrigált javaslatok

### ✅ Hozzáadandó a defaults.yaml-hoz

#### 1. Fix értékek
```yaml
# Subsidiary - mindig ugyanaz
- name: subsidiary
  value: "otphq"
```

#### 2. Számítható értékek
```yaml
# VNet
- name: vnet_name
  value: "vnet-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"

- name: vnet_rgrp_name
  value: "rgrp-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"

# Route Table
- name: rtbl_name
  value: "rtbl-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-core"

- name: rtbl_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

# Key Vault Resource Group
- name: kvau_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

#### 3. Alias-ok
```yaml
# Subnet aliases
- name: snet_name
  value: ${{ variables.subnet_name }}

# Key Vault aliases
- name: kv_name
  value: ${{ variables.key_vault_name }}

- name: keyvault_name
  value: ${{ variables.key_vault_name }}

- name: kv_rgrp_name
  value: ${{ variables.kvau_rgrp_name }}

# Private Endpoint aliases
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}

- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: pe_snet_name
  value: ${{ variables.subnet_name }}

# VNet aliases (for AKS, DataFactory, Function Apps)
- name: vnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: vnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

#### 4. Számítható ID-k (új változók kellenek!)
```yaml
# Key Vault ID - PROBLÉMA: nincs subscriptionId!
# Ezt később, ha lesz subscriptionId változó:
# - name: keyvault_id
#   value: "/subscriptions/${{ variables.subscriptionId }}/resourceGroups/${{ variables.kvau_rgrp_name }}/providers/Microsoft.KeyVault/vaults/${{ variables.key_vault_name }}"
```

### ⚠️ Combined override-ba (környezet + projekt specifikus)

```yaml
# tooling/env/overrides/combined/dev-demo01.yaml
variables:
  - name: ingress_nginx_ip
    value: "************"

# tooling/env/overrides/combined/dev-coeinf.yaml
variables:
  - name: ingress_nginx_ip
    value: "**********"

# tooling/env/overrides/combined/tst-iac.yaml
variables:
  - name: ingress_nginx_ip
    value: "***********"

# tooling/env/overrides/combined/prd-iac.yaml
variables:
  - name: ingress_nginx_ip
    value: "***********"
```

### 📄 tfvars.template-be (modul-specifikus)

Ezek **NEM** kerülnek a defaults.yaml-ba, mert csak 1-2 repo használja:

- `appgw_*` változók → application-gateway repo
- `mhsm_*` változók → ai-foundry, ai-language, ai-search repo-k
- `aad_admin_name`, `synapse_role_assignment_principal_id` → synapse-workspace repo
- `lb_*` változók → lb repo
- `puip_*` változók → public-ip használó repo-k

---

## 📈 Összefoglalás

### Hozzáadandó változók száma

| Kategória | Változók száma |
|-----------|----------------|
| Fix értékek | 1 (subsidiary) |
| Számítható értékek | 5 (vnet_*, rtbl_*, kvau_rgrp_name) |
| Alias-ok | 10 (snet_name, kv_*, pe_*, vnet_vnet_*) |
| **ÖSSZESEN** | **16 változó** |

### Környezet-specifikus változók

| Változó | Hova | Megjegyzés |
|---------|------|------------|
| `ingress_nginx_ip` | Combined override | Projekt + környezet specifikus |
| `appgw_private_ip` | tfvars.template | Csak 1 repo használja |
| `first_subnet` | tfvars.template | Csak 1 repo használja |
| `lb_private_ip` | tfvars.template | Csak 1 repo használja |

### Hiányzó változók a composer-ben

- `subscriptionId` - Kell a keyvault_id számításához!

---

**Készítette**: Augment AI  
**Dátum**: 2025-11-04  
**Verzió**: 3.0 (Korrigált elemzés)

