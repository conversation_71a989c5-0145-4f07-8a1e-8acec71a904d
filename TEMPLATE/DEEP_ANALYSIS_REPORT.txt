====================================================================================================
DEEP ANALYSIS: TFVARS VARIABLES vs COMPOSER SYSTEM
====================================================================================================

Total unique variables in tfvars: 64
Variables in composer system: 22

====================================================================================================
CATEGORY 1: ALREADY IN COMPOSER (or has direct mapping)
====================================================================================================
Count: 13

  project                                  (direct match) (307 uses, 45 repos)
  cloud                                    (direct match) (293 uses, 44 repos)
  environment                              (direct match) (293 uses, 44 repos)
  region                                   (direct match) (292 uses, 44 repos)
  snet_name                                -> subnet_name                    ( 98 uses, 11 repos)
  pe_snet_name                             -> subnet_name                    ( 64 uses, 10 repos)
  kvau_name                                -> key_vault_name                 ( 49 uses,  9 repos)
  kv_name                                  -> key_vault_name                 ( 34 uses,  7 repos)
  prep_subnet_name                         -> subnet_name                    ( 34 uses,  3 repos)
  vnet_snet_name                           -> subnet_name                    ( 16 uses,  1 repos)
  subnet_name                              (direct match) ( 10 uses,  1 repos)
  keyvault_name                            -> key_vault_name                 (  3 uses,  1 repos)
  prep_storage_subnet_name                 -> subnet_name                    (  3 uses,  1 repos)

====================================================================================================
CATEGORY 2: CAN BE COMPUTED from existing composer variables
====================================================================================================
Count: 6

  subsidiary                               (176 uses, 30 repos)
  vnet_name                                (167 uses, 23 repos)
  vnet_rgrp_name                           (167 uses, 23 repos)
  rtbl_name                                ( 83 uses, 18 repos)
  rtbl_rgrp_name                           ( 65 uses, 14 repos)
  kvau_rgrp_name                           ( 52 uses, 10 repos)

====================================================================================================
CATEGORY 3: NEEDS TO BE ADDED to composer
====================================================================================================
Count: 3

  pe_vnet_name                             ( 64 uses, 10 repos)
  pe_vnet_rgrp_name                        ( 64 uses, 10 repos)
  kv_rgrp_name                             ( 33 uses,  7 repos)

====================================================================================================
CATEGORY 4: PROJECT-SPECIFIC (should be in tfvars.template)
====================================================================================================
Count: 0


====================================================================================================
CATEGORY 5: RARE USAGE (<5 repos, should be in tfvars.template)
====================================================================================================
Count: 42

  prep_subnet_vnet_name                    ( 34 uses,  3 repos)
  prep_subnet_vnet_rgrp_name               ( 34 uses,  3 repos)
  vnet_vnet_name                           ( 32 uses,  4 repos)
  vnet_vnet_rgrp_name                      ( 32 uses,  4 repos)
  address_prefix                           ( 15 uses,  4 repos)
  resource_name_suffix                     ( 14 uses,  1 repos)
  appgw_snet_name                          ( 12 uses,  1 repos)
  appgw_vnet_name                          ( 12 uses,  1 repos)
  appgw_vnet_rgrp_name                     ( 12 uses,  1 repos)
  mhsm_umid_name                           (  9 uses,  3 repos)
  mhsm_umid_rgrp_name                      (  9 uses,  3 repos)
  mhsm_key                                 (  9 uses,  3 repos)
  ingress_nginx_ip                         (  9 uses,  1 repos)
  keyvault_id                              (  9 uses,  2 repos)
  appgw_private_ip                         (  7 uses,  1 repos)
  aad_admin_name                           (  6 uses,  1 repos)
  synapse_role_assignment_principal_id     (  6 uses,  1 repos)
  puip_name                                (  5 uses,  1 repos)
  puip_rgrp_name                           (  5 uses,  1 repos)
  rtbl_rg_name                             (  4 uses,  1 repos)
  snet_prefix                              (  3 uses,  1 repos)
  lb_private_ip                            (  3 uses,  1 repos)
  lb_frontend_ip1                          (  3 uses,  1 repos)
  prep_storage_subnet_vnet_name            (  3 uses,  1 repos)
  prep_storage_subnet_vnet_rgrp_name       (  3 uses,  1 repos)
  first_subnet                             (  3 uses,  1 repos)
  apgw_public_ip_id                        (  2 uses,  1 repos)
  remote_state_rgrp_name                   (  2 uses,  1 repos)
  remote_state_stac_name                   (  2 uses,  1 repos)
  remote_state_cont_name                   (  2 uses,  1 repos)
  remote_state_key                         (  2 uses,  1 repos)
  usernodepool_subnet_address_prefix       (  1 uses,  1 repos)
  primary_region                           (  1 uses,  1 repos)
  secondary_region                         (  1 uses,  1 repos)
  owner                                    (  1 uses,  1 repos)
  primary_vnet_name                        (  1 uses,  1 repos)
  primary_vnet_rgrp_name                   (  1 uses,  1 repos)
  secondary_vnet_name                      (  1 uses,  1 repos)
  secondary_vnet_rgrp_name                 (  1 uses,  1 repos)
  primary_subnet_prefix                    (  1 uses,  1 repos)
  secondary_subnet_prefix                  (  1 uses,  1 repos)
  enterprise_policy_location               (  1 uses,  1 repos)
