# Conventions
cloud       = "{{ CLOUD }}"
environment = "{{ ENVIRONMENT }}"
region      = "{{ REGION_FULL }}"
project     = "abb"

# Network configuration
vnet_name      = "{{ VIRTUAL_NETWORK_NAME }}"
vnet_rgrp_name = "{{ VIRTUAL_NETWORK_RESOURCE_GROUP_NAME }}"
subnet_name    = "{{ SUBNET_NAME }}"

# Key Vault configuration
kv_name      = "{{ KEY_VAULT_NAME }}"
kv_rgrp_name = "{{ KEY_VAULT_RESOURCE_GROUP_NAME }}"
