trigger: none

parameters:
- name: environments
  type: object
  default:
  - key: DEV_gwc_iac
    file: DEV-iac
  - key: DEV_weu_demo01
    file: DEV-demo01
  - key: DEV_gwc_iac-dmz
    file: DEV-iac-dmz
  - key: TST_weu_iac
    file: TST-iac
  - key: PRD_weu_iac
    file: PRD-iac

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: fix-git-tag-step

stages:
- stage: TestAllEnvironments
  displayName: 'Test All Environment Configurations'
  jobs:
  # Test each environment in a separate job
  - ${{ each env in parameters.environments }}:
    - job: Test_${{ replace(env.key, '-', '_') }}
      displayName: 'Test ${{ env.key }}'
      pool:
        name: DEV-AksPool-centralagent-Deploy

      variables:
        - name: environment
          value: ${{ split(env.key, '_')[0] }}
        - name: region
          value: ${{ split(env.key, '_')[1] }}
        - name: project
          value: ${{ split(env.key, '_')[2] }}
        - name: staticYamlFile
          value: ${{ env.file }}
        - template: /env/azure/composer.yaml@tooling
          parameters:
            environment: ${{ split(env.key, '_')[0] }}
            region: ${{ split(env.key, '_')[1] }}
            project: ${{ split(env.key, '_')[2] }}

      steps:
      - checkout: self
      - checkout: tooling

      - bash: |
          #!/bin/bash

          # Colors for output
          RED='\033[0;31m'
          GREEN='\033[0;32m'
          YELLOW='\033[1;33m'
          BLUE='\033[0;34m'
          NC='\033[0m' # No Color

          ENV_NAME="$(environment)-$(region)-$(project)"
          YAML_FILE="tooling/env/$(staticYamlFile).yaml"

          echo -e "${BLUE}=========================================="
          echo "Testing Environment: $ENV_NAME"
          echo "  Environment: $(environment)"
          echo "  Region: $(region)"
          echo "  Project: $(project)"
          echo "Static YAML file: $YAML_FILE"
          echo -e "==========================================${NC}"
          echo ""

          # Function to extract value from static YAML
          extract_static_value() {
            local yaml_file=$1
            local var_name=$2

            if [ ! -f "$yaml_file" ]; then
              echo "FILE_NOT_FOUND"
              return 1
            fi

            # Extract value from YAML (handles both with and without quotes)
            local value=$(grep "^  ${var_name}:" "$yaml_file" | sed 's/^[^:]*: *//' | sed 's/^"\(.*\)"$/\1/' | sed "s/^'\(.*\)'$/\1/")
            echo "$value"
          }

          # Function to compare values
          compare_value() {
            local var_name=$1
            local composer_value=$2
            local static_value=$3

            if [ "$composer_value" == "$static_value" ]; then
              echo -e "  ${GREEN}✓${NC} $var_name: $composer_value"
              return 0
            else
              echo -e "  ${RED}✗${NC} $var_name:"
              echo -e "    Composer: '$composer_value'"
              echo -e "    Static:   '$static_value'"
              return 1
            fi
          }

          if [ ! -f "$YAML_FILE" ]; then
            echo -e "${RED}✗ FAILED: Static YAML file not found: $YAML_FILE${NC}"
            exit 1
          fi

          ERRORS=0

          # Compare all variables
          echo "Comparing composer output with static YAML file:"
          echo ""

          # appCode
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "appCode")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "appCode" "$(appCode)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # target
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "target")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "target" "$(target)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # armServiceConnectionName
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "armServiceConnectionName")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "armServiceConnectionName" "$(armServiceConnectionName)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # storageAccountContainerName
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "storageAccountContainerName")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "storageAccountContainerName" "$(storageAccountContainerName)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # storageAccountName
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "storageAccountName")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "storageAccountName" "$(storageAccountName)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # storageAccountResourceGroup
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "storageAccountResourceGroup")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "storageAccountResourceGroup" "$(storageAccountResourceGroup)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # keyVaultServiceConnectionName
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "keyVaultServiceConnectionName")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "keyVaultServiceConnectionName" "$(keyVaultServiceConnectionName)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # KeyVaultName
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "KeyVaultName")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "KeyVaultName" "$(KeyVaultName)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          # keyVaultCommaSeparatedSecretNames
          STATIC_VAL=$(extract_static_value "$YAML_FILE" "keyVaultCommaSeparatedSecretNames")
          if [ -n "$STATIC_VAL" ]; then
            compare_value "keyVaultCommaSeparatedSecretNames" "$(keyVaultCommaSeparatedSecretNames)" "$STATIC_VAL" || ERRORS=$((ERRORS + 1))
          fi

          echo ""
          echo -e "${BLUE}------------------------------------------${NC}"

          if [ $ERRORS -gt 0 ]; then
            echo -e "${RED}✗ FAILED: $ENV_NAME ($ERRORS errors)${NC}"
            echo ""
            echo "##vso[task.logissue type=error]Composer validation failed for $ENV_NAME with $ERRORS errors"
            echo "##vso[task.complete result=Failed;]"
            exit 1
          else
            echo -e "${GREEN}✓ PASSED: $ENV_NAME - All values match!${NC}"
            exit 0
          fi

        displayName: 'Validate Composer vs Static YAML'
        workingDirectory: $(System.DefaultWorkingDirectory)
