#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to analyze all tfvars files in terraform-azurerm-* repositories
and extract all variable names used.
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def extract_variables_from_tfvars(file_path):
    """Extract variable names from a tfvars file."""
    variables = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Match variable assignments: variable_name = value
        # This regex matches:
        # - variable_name = "value"
        # - variable_name = value
        # - variable_name = { ... }
        # - variable_name = [ ... ]
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*='
        
        for line in content.split('\n'):
            # Skip comments
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                variables.add(var_name)
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return variables

def main():
    # Find all terraform-azurerm-* directories
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Dictionary to store all variables found
    all_variables = defaultdict(list)  # variable_name -> list of files where it appears
    
    # Process each terraform directory
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        # Find all .tfvars files
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        tfvars_files.extend(examples_dir.rglob('*.tfvars.template'))
        
        for tfvars_file in tfvars_files:
            variables = extract_variables_from_tfvars(tfvars_file)
            
            for var in variables:
                relative_path = tfvars_file.relative_to(base_dir)
                all_variables[var].append(str(relative_path))
    
    # Sort variables by frequency (most common first)
    sorted_vars = sorted(all_variables.items(), 
                        key=lambda x: len(x[1]), 
                        reverse=True)
    
    # Print results
    print("=" * 80)
    print("VARIABLES FOUND IN TFVARS FILES")
    print("=" * 80)
    print(f"\nTotal unique variables: {len(sorted_vars)}\n")
    
    # Group by frequency
    print("\n" + "=" * 80)
    print("VARIABLES BY FREQUENCY")
    print("=" * 80)
    
    for var_name, file_list in sorted_vars:
        count = len(file_list)
        print(f"\n{var_name}: {count} occurrences")
        # Show first 3 examples
        for example_file in file_list[:3]:
            print(f"  - {example_file}")
        if len(file_list) > 3:
            print(f"  ... and {len(file_list) - 3} more")
    
    # Save to file
    output_file = 'tfvars_variables_analysis.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("VARIABLES FOUND IN TFVARS FILES\n")
        f.write("=" * 80 + "\n")
        f.write(f"\nTotal unique variables: {len(sorted_vars)}\n\n")
        
        f.write("\n" + "=" * 80 + "\n")
        f.write("VARIABLES BY FREQUENCY\n")
        f.write("=" * 80 + "\n")
        
        for var_name, file_list in sorted_vars:
            count = len(file_list)
            f.write(f"\n{var_name}: {count} occurrences\n")
            for example_file in file_list:
                f.write(f"  - {example_file}\n")
    
    print(f"\n\nDetailed results saved to: {output_file}")
    
    # Create a simple list of all unique variables
    simple_list_file = 'tfvars_variables_list.txt'
    with open(simple_list_file, 'w', encoding='utf-8') as f:
        for var_name, _ in sorted_vars:
            f.write(f"{var_name}\n")
    
    print(f"Simple variable list saved to: {simple_list_file}")

if __name__ == '__main__':
    main()

