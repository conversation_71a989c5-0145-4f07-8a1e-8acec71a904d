# Javaslatok a defaults.yaml bővítéséhez

## Összefoglaló

Az elemz<PERSON> alap<PERSON>án **67 változó** hiányzik a `tooling/env/defaults.yaml` fájlb<PERSON>l, amelyek a terraform-azurerm-* repository-k examples mappáiban hasz<PERSON>k.

## Naming Convention Pattern-ek

Az elemzés alapján a következő naming convention-öket azonosítottam:

### Region rövidítések
- `westeurope` → `weu`
- `germanywestcentral` → `gwc`

### Általános pattern-ek

**VNet naming:**
```
vnet-{region_short}-{environment}-{project}-01
Példa: vnet-weu-dev-demo01-01
```

**Resource Group naming:**
```
rgrp-{region_short}-{environment}-{project}-01
Példa: rgrp-weu-dev-demo01-01
```

**Subnet naming:**
```
snet-{region_short}-{purpose}
Példa: snet-weu-pe01 (private endpoint subnet)
```

**Key Vault naming (TST/PRD):**
```
{project}-{environment}-shared01
Példa: iac-tst-shared01
```

**Key Vault naming (DEV):**
```
kvau-{region_short}-{environment}-{unique_id}
Példa: kvau-weu-dev-DEVD000001
```

## Javasolt változók hozzáadása a defaults.yaml-hoz

### 1. Alapvető konvenciók (KRITIKUS - 100% lefedettség)

```yaml
# Core conventions
- name: project
  value: ${{ parameters.project }}

- name: environment
  value: ${{ parameters.environment }}

- name: region
  value: ${{ parameters.region }}

- name: subsidiary
  value: ${{ parameters.subsidiary }}
  
# Region short name mapping
- name: regionShort
  value: ${{ 
    if(eq(parameters.region, 'westeurope'), 'weu',
    if(eq(parameters.region, 'germanywestcentral'), 'gwc',
    if(eq(parameters.region, 'northeurope'), 'neu',
    'unknown'))) }}
```

### 2. Hálózati alapok (168 előfordulás)

```yaml
# Virtual Network
- name: vnet_name
  value: "vnet-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"

- name: vnet_rgrp_name
  value: "rgrp-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"

# Subnet (general)
- name: snet_name
  value: "snet-${{ variables.regionShort }}-pe01"  # Default: private endpoint subnet
```

### 3. Private Endpoint változók (64 előfordulás)

```yaml
# Private Endpoint Network Configuration
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}

- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: pe_snet_name
  value: ${{ variables.snet_name }}
```

### 4. Route Table (83 előfordulás)

```yaml
# Route Table
- name: rtbl_name
  value: "rtbl-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-core"

- name: rtbl_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: rtbl_rg_name  # Alternative naming
  value: ${{ variables.rtbl_rgrp_name }}
```

### 5. Key Vault (52 előfordulás)

```yaml
# Key Vault (kvau = Key Vault Azure)
- name: kvau_name
  value: ${{ 
    if(eq(parameters.environment, 'dev'), 
      format('kvau-{0}-{1}-{2}', variables.regionShort, parameters.environment, 'DEVD000001'),
      format('{0}-{1}-shared01', parameters.project, parameters.environment)) }}

- name: kvau_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

# Key Vault (kv = alternative naming)
- name: kv_name
  value: ${{ variables.kvau_name }}

- name: kv_rgrp_name
  value: ${{ variables.kvau_rgrp_name }}

# Key Vault ID (full resource ID)
- name: keyvault_id
  value: "/subscriptions/${{ parameters.subscriptionId }}/resourceGroups/${{ variables.kvau_rgrp_name }}/providers/Microsoft.KeyVault/vaults/${{ variables.kvau_name }}"

- name: keyvault_name
  value: ${{ variables.kvau_name }}
```

### 6. Prep Subnet (Private Endpoint Prep) (34 előfordulás)

```yaml
# Prep Subnet (for resources that need private endpoints)
- name: prep_subnet_name
  value: ${{ variables.snet_name }}

- name: prep_subnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: prep_subnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

### 7. VNet alternatív elnevezések (32 előfordulás)

```yaml
# Alternative VNet naming (used in some modules)
- name: vnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: vnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: vnet_snet_name
  value: ${{ variables.snet_name }}
```

### 8. Application Gateway (12 előfordulás)

```yaml
# Application Gateway Network
- name: appgw_vnet_name
  value: ${{ variables.vnet_name }}

- name: appgw_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: appgw_snet_name
  value: "snet-${{ variables.regionShort }}-appgw01"

- name: appgw_private_ip
  value: ""  # To be set per environment/project
```

### 9. Egyéb gyakori változók

```yaml
# Resource naming
- name: resource_name_suffix
  value: "01"

# Network
- name: address_prefix
  value: ""  # To be set per environment/project

# HSM/Encryption (for environments that use it)
- name: mhsm_umid_name
  value: "umid-${{ variables.regionShort }}-${{ parameters.environment }}-${{ parameters.project }}-01"

- name: mhsm_umid_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: mhsm_key
  value: ""  # To be set per environment/project

# Storage for prep
- name: prep_storage_subnet_name
  value: ${{ variables.snet_name }}

- name: prep_storage_subnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: prep_storage_subnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

## Implementációs stratégia

### Fázis 1: Alapvető változók (Azonnal)
1. `project`, `environment`, `region`, `subsidiary`
2. `regionShort` mapping
3. `vnet_name`, `vnet_rgrp_name`
4. `snet_name`

### Fázis 2: Hálózati kiterjesztések (1 hét)
1. Private Endpoint változók (`pe_*`)
2. Route Table változók (`rtbl_*`)
3. Alternatív VNet elnevezések (`vnet_vnet_*`)

### Fázis 3: Szolgáltatás-specifikus változók (2 hét)
1. Key Vault változók (`kvau_*`, `kv_*`)
2. Application Gateway változók (`appgw_*`)
3. HSM/Encryption változók (`mhsm_*`)

### Fázis 4: Speciális esetek (3 hét)
1. Load Balancer változók
2. Stream Analytics változók
3. Database/Synapse változók
4. Remote State változók

## Override stratégia

### Environment szintű override-ok
- `kvau_name` - DEV környezetben más naming convention
- `address_prefix` - környezetenként eltérő IP tartományok
- `mhsm_key` - környezetenként eltérő encryption key-ek

### Project szintű override-ok
- `vnet_name` - speciális projektek esetén
- `vnet_rgrp_name` - speciális projektek esetén
- `resource_name_suffix` - projekt-specifikus suffix

### Region szintű override-ok
- `regionShort` - új régiók hozzáadása
- `snet_name` - régió-specifikus subnet naming

## Megjegyzések és figyelmeztetések

1. **Naming convention különbségek:**
   - DEV környezetben: `kvau-weu-dev-DEVD000001`
   - TST/PRD környezetben: `{project}-{env}-shared01`
   - Ez a különbség megmarad az új rendszerben is

2. **Subnet naming:**
   - `subnet_name` vs `snet_name` - mindkettő használatos
   - Javaslat: `snet_name` legyen az elsődleges, `subnet_name` alias

3. **Resource Group naming:**
   - Régi: `OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01`
   - Új: `rgrp-weu-dev-{project}-01`
   - Az új convention-t használjuk

4. **Key Vault ID:**
   - Teljes resource ID szükséges néhány modulnál
   - Subscription ID-t paraméterként kell átadni

## Következő lépések

1. ✅ Elemzés elkészült
2. ⏳ Döntés a változók hozzáadásáról
3. ⏳ defaults.yaml frissítése
4. ⏳ Environment/region/project override-ok definiálása
5. ⏳ Tesztelés példa projektekkel
6. ⏳ Dokumentáció frissítése
7. ⏳ Migrációs útmutató készítése

## Kérdések tisztázásra

1. Mely változókat szeretnéd prioritásként hozzáadni?
2. Szükséges-e a régi naming convention támogatása (backward compatibility)?
3. Hogyan kezeljük a DEV vs TST/PRD közötti Key Vault naming különbséget?
4. Subscription ID honnan származzon (paraméter, environment változó)?
5. Mely változók legyenek kötelezőek vs opcionálisak?

