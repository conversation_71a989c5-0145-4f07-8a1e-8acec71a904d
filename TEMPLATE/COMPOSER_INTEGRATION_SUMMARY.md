# Composer rendszer integráció - Összefoglaló

## <PERSON>ttekintés

A composer rendszer integrálása a Terraform plan step-ekbe teljes vissza<PERSON> kompatibilitással.

## Változtatások

### Módosított fájlok

1. **tooling/iac-pipelines/iac-execute-step-plan-azure.yaml** (Azure)
2. **tooling/iac-pipelines/iac-execute-step-plan-gcp.yaml** (GCP)
3. **tooling/iac-pipelines/iac-execute-step-plan-aws.yaml** (AWS)
4. **tooling/iac-pipelines/iac-execute-step-generate-tfvars-azure.yaml** (Tfvars gener<PERSON>)

### Működési logika

A plan step-ek most intelligensen választanak a két rendszer között:

#### 1. Composer rendszer (ÚJ)
- **Feltétel**: Ha létezik `terraformVarsFile` környezeti változó ÉS a fájl létezik
- **Működés**:
  - A `iac-execute-step-generate-tfvars-azure.yaml` step generálja a tfvars fájlt template-ből
  - Beállítja a `terraformVarsFile` változót (csak a fájlnév, pl. `dev-weu-iac.tfvars`)
  - A plan step ezt használja (ugyanabban a working directory-ban fut)
- **Előny**: Dinamikus változó generálás, template-alapú konfiguráció

#### 2. Legacy rendszer (RÉGI)
- **Feltétel**: Ha nincs `terraformVarsFile` változó VAGY a fájl nem létezik
- **Működés**:
  - Használja a `GLOBAL_TFVARS` fájlt: `$(Pipeline.Workspace)/tooling_repo/env/${{ parameters.environment }}.tfvars`
  - Használja a `LOCAL_TFVARS` fájlt: `$(Build.SourcesDirectory)/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars`
- **Előny**: Meglévő projektek változtatás nélkül működnek

## Implementációs részletek

### Environment változók

Minden plan step-ben hozzáadtuk:
```yaml
env:
  TERRAFORM_VARS_FILE: $(terraformVarsFile)
```

### Bash script logika

```bash
# Check if composer system generated a tfvars file
if [[ -n "${TERRAFORM_VARS_FILE:-}" ]] && [[ -f "${TERRAFORM_VARS_FILE}" ]]; then
  # New composer system: use the generated tfvars file
  echo "Using composer-generated tfvars file:"
  echo "  File: ${TERRAFORM_VARS_FILE}"
  TFVARS_PARAMS="-var-file=${TERRAFORM_VARS_FILE}"
  cat "${TERRAFORM_VARS_FILE}"
else
  # Legacy system: use GLOBAL_TFVARS and LOCAL_TFVARS
  echo "Using legacy tfvars files:"
  # ... régi logika ...
fi
```

## Használat

### Új projekt (composer rendszerrel)

1. Hozz létre egy `terraform.tfvars.template` fájlt a projekt könyvtárában
2. Használj placeholder-eket: `{{ VARIABLE_NAME }}`
3. A pipeline automatikusan generálja a tfvars fájlt
4. Példa:
   ```hcl
   cloud       = "{{ CLOUD }}"
   environment = "{{ ENVIRONMENT }}"
   region      = "{{ REGION_FULL }}"
   project     = "{{ PROJECT }}"
   ```

### Régi projekt (legacy rendszer)

- Semmi változtatás nem szükséges
- Továbbra is használja a `DEV-iac.tfvars` típusú fájlokat
- Minden működik, ahogy eddig

## Előnyök

✅ **Teljes visszafelé kompatibilitás**: Minden meglévő projekt változtatás nélkül működik
✅ **Fokozatos migráció**: Projekt-szinten lehet átállni az új rendszerre
✅ **Automatikus detektálás**: Ha van template, automatikusan az új rendszert használja
✅ **Nincs breaking change**: Semmilyen meglévő pipeline nem törik el
✅ **Jobb logging**: Egyértelműen jelzi, melyik rendszert használja

## Migrációs útvonal

### 1. lépés: Template létrehozása
Hozz létre egy `terraform.tfvars.template` fájlt a projektben.

### 2. lépés: Composer változók használata
Cseréld le a fix értékeket placeholder-ekre:
- `cloud = "azure"` → `cloud = "{{ CLOUD }}"`
- `environment = "dev"` → `environment = "{{ ENVIRONMENT }}"`

### 3. lépés: Pipeline futtatás
A pipeline automatikusan felismeri a template-et és generálja a tfvars fájlt.

### 4. lépés: Régi fájlok törlése (opcionális)
Ha minden működik, törölheted a régi statikus tfvars fájlokat.

## Tesztelés

A változtatások tesztelése:

1. **Régi projekt**: Futtass egy pipeline-t template nélkül → Legacy rendszert használ
2. **Új projekt**: Futtass egy pipeline-t template-tel → Composer rendszert használ
3. **Ellenőrizd a logokat**: A pipeline kimenetében látható, melyik rendszert használja

## Kapcsolódó fájlok

- `tooling/iac-pipelines/iac-execute-step-generate-tfvars-azure.yaml` - Template generálás
- `tooling/env/azure/composer.yaml` - Composer változók definíciója
- `tooling/env/azure/defaults.yaml` - Alapértelmezett értékek
- `tooling/env/azure/overrides/` - Környezet/régió/projekt specifikus felülírások

## Következő lépések

1. Teszteld a változtatásokat egy dev környezetben
2. Válassz ki egy pilot projektet a migrációhoz
3. Hozz létre template-et a pilot projekthez
4. Futtasd a pipeline-t és ellenőrizd az eredményt
5. Ha sikeres, fokozatosan migráld a többi projektet

