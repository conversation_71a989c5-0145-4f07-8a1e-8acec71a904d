# Végs<PERSON> konszolidált javaslatok

## 🎯 Alapelv

**Egy értéket egyszer, egy változóban tárolunk.**

Ha valahol eltérés van a defaulthoz képest, azt a template megír<PERSON>kor kezeljük.

## 📊 Elemzés eredménye

### ✅ <PERSON><PERSON><PERSON> alias-ok (100% egyezés)

Ezek a változók **mindig ugyanazt az értéket** tartalmazzák, amikor együtt szerepelnek egy fájlban:

| Csoport | Kanonikus változó | Alias-ok | Használat |
|---------|-------------------|----------|-----------|
| **Key Vault név** | `key_vault_name` | `kvau_name`, `kv_name`, `keyvault_name` | 86 fájl |
| **Key Vault RG** | `kvau_rgrp_name` | `kv_rgrp_name` | 85 fájl |
| **Route Table név** | `rtbl_name` | - | 83 fájl |
| **Route Table RG** | `rtbl_rgrp_name` | `rtbl_rg_name` | 69 fájl |

**Akció**: Csak a kanonikus változót tartjuk, a többit alias-ként mappeljük.

### ⚠️ NEM alias-ok (vannak eltérések)

Ezek a változók **NEM mindig ugyanazok**, mert:

#### 1. **VNet változók**

| Változó | Használat | Probléma |
|---------|-----------|----------|
| `vnet_name` | 167 fájl | Fő VNet |
| `pe_vnet_name` | 64 fájl | **1 esetben eltér** (elírás: kis/nagybetű) |
| `vnet_vnet_name` | 32 fájl | Általában ugyanaz, mint `vnet_name` |
| `appgw_vnet_name` | 12 fájl | Általában ugyanaz, mint `vnet_name` |
| `prep_subnet_vnet_name` | 34 fájl | Általában ugyanaz, mint `vnet_name` |
| `primary_vnet_name` | 1 fájl | **Multi-region**: különböző VNet! |
| `secondary_vnet_name` | 1 fájl | **Multi-region**: különböző VNet! |

**Probléma példák:**
```
# Elírás (kis/nagybetű):
vnet_name    = 'OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01'
pe_vnet_name = 'otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01'

# Multi-region (valóban különböző):
primary_vnet_name   = 'vnet-weu-dev-demo01-01'
secondary_vnet_name = 'vnet-gwc-dev-demo01-01'
```

**Döntés**: 
- `pe_vnet_name`, `vnet_vnet_name`, `appgw_vnet_name`, `prep_subnet_vnet_name` → **Alias-ként kezeljük** (az 1 elírás elhanyagolható)
- `primary_vnet_name`, `secondary_vnet_name` → **NEM alias**, külön változók (multi-region)

#### 2. **Subnet változók**

| Változó | Használat | Probléma |
|---------|-----------|----------|
| `subnet_name` | 10 fájl | Fő subnet (PE) |
| `snet_name` | 98 fájl | Általában ugyanaz |
| `pe_snet_name` | 64 fájl | Általában ugyanaz |
| `appgw_snet_name` | 12 fájl | **MINDIG különböző!** (AppGW külön subnet) |
| `prep_subnet_name` | 34 fájl | Általában ugyanaz |

**Probléma példák:**
```
# Application Gateway - KÜLÖNBÖZŐ subnet-ek:
pe_snet_name   = 'snet-weu-pe01'      # Private Endpoint subnet
appgw_snet_name = 'snet-weu-compute04' # AppGW subnet
```

**Döntés**:
- `snet_name`, `pe_snet_name`, `prep_subnet_name` → **Alias-ként kezeljük**
- `appgw_snet_name` → **NEM alias**, külön változó (AppGW-specifikus)

## 📋 Végső javaslat: defaults.yaml

### 1. Fix értékek

```yaml
# Subsidiary - mindig 'otphq'
- name: subsidiary
  value: "otphq"
```

### 2. Számítható értékek (új változók)

```yaml
# VNet
- name: vnet_name
  value: "vnet-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"

- name: vnet_rgrp_name
  value: "rgrp-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"

# Route Table
- name: rtbl_name
  value: "rtbl-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-core"

- name: rtbl_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

# Key Vault Resource Group
- name: kvau_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

### 3. Alias-ok (meglévő változókra mutatnak)

```yaml
# Key Vault aliases
- name: kvau_name
  value: ${{ variables.key_vault_name }}

- name: kv_name
  value: ${{ variables.key_vault_name }}

- name: keyvault_name
  value: ${{ variables.key_vault_name }}

- name: kv_rgrp_name
  value: ${{ variables.kvau_rgrp_name }}

# Route Table alias
- name: rtbl_rg_name
  value: ${{ variables.rtbl_rgrp_name }}

# VNet aliases (általában ugyanaz, mint vnet_name)
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}

- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: vnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: vnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: appgw_vnet_name
  value: ${{ variables.vnet_name }}

- name: appgw_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: prep_subnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: prep_subnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: prep_storage_subnet_vnet_name
  value: ${{ variables.vnet_name }}

- name: prep_storage_subnet_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

# Subnet aliases (általában ugyanaz, mint subnet_name)
- name: snet_name
  value: ${{ variables.subnet_name }}

- name: pe_snet_name
  value: ${{ variables.subnet_name }}

- name: vnet_snet_name
  value: ${{ variables.subnet_name }}

- name: prep_subnet_name
  value: ${{ variables.subnet_name }}

- name: prep_storage_subnet_name
  value: ${{ variables.subnet_name }}
```

## 📄 NEM kerül a defaults.yaml-ba

Ezek **modul-specifikus** vagy **ritkán használt** változók:

### Multi-region változók (1 repo használja)
- `primary_vnet_name`, `secondary_vnet_name`
- `primary_vnet_rgrp_name`, `secondary_vnet_rgrp_name`
- `primary_subnet_prefix`, `secondary_subnet_prefix`

**Megoldás**: tfvars.template a subnet repo-ban

### Application Gateway specifikus (1 repo használja)
- `appgw_snet_name` - **KÜLÖNBÖZŐ** subnet, mint a PE!
- `appgw_private_ip`

**Megoldás**: tfvars.template az application-gateway repo-ban

### AKS specifikus (1 repo használja)
- `ingress_nginx_ip` - környezet + projekt specifikus

**Megoldás**: Combined override fájlok

### HSM specifikus (3 repo használja)
- `mhsm_umid_name`, `mhsm_umid_rgrp_name`, `mhsm_key`

**Megoldás**: tfvars.template az AI repo-kban

### Egyéb ritkán használt
- `lb_private_ip`, `lb_frontend_ip1`, `lb_backend_ips`
- `puip_name`, `puip_rgrp_name`
- `aad_admin_name`, `synapse_role_assignment_principal_id`
- `keyvault_id` (számítható lenne, de nincs subscriptionId)
- stb.

**Megoldás**: tfvars.template az adott repo-kban

## 📊 Összefoglalás

### Hozzáadandó változók száma: 27

| Kategória | Változók száma | Változók |
|-----------|----------------|----------|
| **Fix értékek** | 1 | `subsidiary` |
| **Számítható** | 5 | `vnet_name`, `vnet_rgrp_name`, `rtbl_name`, `rtbl_rgrp_name`, `kvau_rgrp_name` |
| **Alias-ok** | 21 | `kvau_name`, `kv_name`, `keyvault_name`, `kv_rgrp_name`, `rtbl_rg_name`, `pe_vnet_name`, `pe_vnet_rgrp_name`, `vnet_vnet_name`, `vnet_vnet_rgrp_name`, `appgw_vnet_name`, `appgw_vnet_rgrp_name`, `prep_subnet_vnet_name`, `prep_subnet_vnet_rgrp_name`, `prep_storage_subnet_vnet_name`, `prep_storage_subnet_vnet_rgrp_name`, `snet_name`, `pe_snet_name`, `vnet_snet_name`, `prep_subnet_name`, `prep_storage_subnet_name` |

### Lefedettség

**Jelenlegi composer változók**: 22  
**Hozzáadandó változók**: 27  
**Új összesen**: 49 változó

**Tfvars változók összesen**: 64  
**Lefedett változók**: 49 (77%)  
**Modul-specifikus**: 15 (23%)

### Várható hatás

**Előtte:**
- Átlagosan 15-20 változót kell manuálisan megadni

**Utána:**
- 0-5 változót kell manuálisan megadni (csak modul-specifikus)
- 77% automatikusan kezelve!

## ⚠️ Fontos megjegyzések

### 1. Elírások kezelése

Van **1 fájl**, ahol `vnet_name` és `pe_vnet_name` eltér (kis/nagybetű):
```
terraform-azurerm-application-insights/examples/02-python-alfa-with-appi/DEV-coeinf.tfvars
```

**Megoldás**: Ezt javítani kell a tfvars fájlban, mert elírás.

### 2. Application Gateway subnet

Az `appgw_snet_name` **MINDIG különböző**, mint a `pe_snet_name`:
- PE subnet: `snet-weu-pe01`
- AppGW subnet: `snet-weu-compute04`

**Megoldás**: Az `appgw_snet_name` NEM alias, hanem modul-specifikus változó.

### 3. Multi-region

A `primary_*` és `secondary_*` változók **valóban különböző** értékeket tartalmaznak:
- Primary: `vnet-weu-dev-demo01-01`
- Secondary: `vnet-gwc-dev-demo01-01`

**Megoldás**: Ezek NEM alias-ok, külön változók maradnak.

## ✅ Következő lépések

1. **Jóváhagyás**: Elfogadod ezt a konszolidált javaslatot?
2. **Implementáció**: Frissítsük a defaults.yaml-t a 27 változóval?
3. **Elírás javítása**: Javítsuk a DEV-coeinf.tfvars fájlt?
4. **tfvars.template**: Készítsük el a modul-specifikus template-eket?

---

**Készítette**: Augment AI  
**Dátum**: 2025-11-04  
**Verzió**: 4.0 (Konszolidált)

