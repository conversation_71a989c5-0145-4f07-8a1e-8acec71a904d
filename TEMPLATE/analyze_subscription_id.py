#!/usr/bin/env python3
"""
Analyze subscription_id usage patterns.
"""

import re
from pathlib import Path
from collections import defaultdict

def extract_values_from_tfvars(file_path):
    """Extract variable names and values from a tfvars file."""
    variables = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"([^"]*)"'
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('//') or line.startswith('#'):
                continue
                
            match = re.match(pattern, line)
            if match:
                var_name = match.group(1)
                var_value = match.group(2)
                variables[var_name] = var_value
                
    except Exception as e:
        pass
        
    return variables

def extract_env_from_filename(filename):
    """Extract environment from filename."""
    if filename.startswith('DEV-'):
        return 'DEV'
    elif filename.startswith('TST-'):
        return 'TST'
    elif filename.startswith('PRD-'):
        return 'PRD'
    return 'UNKNOWN'

def extract_project_from_filename(filename):
    """Extract project from filename."""
    parts = filename.replace('.tfvars', '').split('-')
    if len(parts) >= 2:
        return parts[1]
    return 'UNKNOWN'

def main():
    base_dir = Path('.')
    terraform_dirs = sorted([d for d in base_dir.iterdir() 
                            if d.is_dir() and d.name.startswith('terraform-azurerm-')])
    
    # Variables that might contain subscription ID
    subscription_vars = [
        'subscription_id',
        'subscriptionId',
        'subscription',
        'keyvault_id',  # Contains subscription ID in the path
    ]
    
    # Track subscription IDs by environment, project, region
    by_environment = defaultdict(set)
    by_project = defaultdict(set)
    by_region = defaultdict(set)
    by_env_project = defaultdict(set)
    
    # Track all files with subscription info
    files_with_subscription = []
    
    for terraform_dir in terraform_dirs:
        examples_dir = terraform_dir / 'examples'
        
        if not examples_dir.exists():
            continue
            
        tfvars_files = list(examples_dir.rglob('*.tfvars'))
        
        for tfvars_file in tfvars_files:
            variables = extract_values_from_tfvars(tfvars_file)
            
            env = extract_env_from_filename(tfvars_file.name)
            project = extract_project_from_filename(tfvars_file.name)
            region = variables.get('region', 'UNKNOWN')
            
            file_ref = f"{terraform_dir.name}/{tfvars_file.relative_to(terraform_dir)}"
            
            # Check for subscription-related variables
            for var_name in subscription_vars:
                if var_name in variables:
                    value = variables[var_name]
                    
                    # Extract subscription ID from keyvault_id if present
                    if var_name == 'keyvault_id':
                        match = re.search(r'/subscriptions/([a-f0-9-]+)/', value)
                        if match:
                            sub_id = match.group(1)
                        else:
                            continue
                    else:
                        sub_id = value
                    
                    by_environment[env].add(sub_id)
                    by_project[project].add(sub_id)
                    by_region[region].add(sub_id)
                    by_env_project[f"{env}-{project}"].add(sub_id)
                    
                    files_with_subscription.append({
                        'file': file_ref,
                        'env': env,
                        'project': project,
                        'region': region,
                        'var_name': var_name,
                        'sub_id': sub_id,
                    })
    
    output_file = 'SUBSCRIPTION_ID_ANALYSIS.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 100 + "\n")
        f.write("SUBSCRIPTION ID ANALYSIS\n")
        f.write("=" * 100 + "\n\n")
        
        if not files_with_subscription:
            f.write("No subscription ID found in any tfvars files.\n")
            print("No subscription ID found in any tfvars files.")
            return
        
        f.write(f"Total files with subscription info: {len(files_with_subscription)}\n\n")
        
        # Analysis by environment
        f.write("=" * 100 + "\n")
        f.write("BY ENVIRONMENT\n")
        f.write("=" * 100 + "\n\n")
        
        for env in sorted(by_environment.keys()):
            sub_ids = by_environment[env]
            f.write(f"{env}:\n")
            f.write(f"  Unique subscription IDs: {len(sub_ids)}\n")
            for sub_id in sorted(sub_ids):
                count = sum(1 for item in files_with_subscription 
                           if item['env'] == env and item['sub_id'] == sub_id)
                f.write(f"    {sub_id}: {count} files\n")
            f.write("\n")
        
        # Analysis by project
        f.write("=" * 100 + "\n")
        f.write("BY PROJECT\n")
        f.write("=" * 100 + "\n\n")
        
        for project in sorted(by_project.keys()):
            sub_ids = by_project[project]
            f.write(f"{project}:\n")
            f.write(f"  Unique subscription IDs: {len(sub_ids)}\n")
            for sub_id in sorted(sub_ids):
                count = sum(1 for item in files_with_subscription 
                           if item['project'] == project and item['sub_id'] == sub_id)
                f.write(f"    {sub_id}: {count} files\n")
            f.write("\n")
        
        # Analysis by environment + project
        f.write("=" * 100 + "\n")
        f.write("BY ENVIRONMENT + PROJECT\n")
        f.write("=" * 100 + "\n\n")
        
        for env_project in sorted(by_env_project.keys()):
            sub_ids = by_env_project[env_project]
            f.write(f"{env_project}:\n")
            f.write(f"  Unique subscription IDs: {len(sub_ids)}\n")
            for sub_id in sorted(sub_ids):
                count = sum(1 for item in files_with_subscription 
                           if f"{item['env']}-{item['project']}" == env_project and item['sub_id'] == sub_id)
                f.write(f"    {sub_id}: {count} files\n")
            f.write("\n")
        
        # Detailed file list
        f.write("=" * 100 + "\n")
        f.write("DETAILED FILE LIST\n")
        f.write("=" * 100 + "\n\n")
        
        for item in sorted(files_with_subscription, key=lambda x: (x['env'], x['project'], x['file'])):
            f.write(f"{item['file']}\n")
            f.write(f"  Environment: {item['env']}\n")
            f.write(f"  Project: {item['project']}\n")
            f.write(f"  Region: {item['region']}\n")
            f.write(f"  Variable: {item['var_name']}\n")
            f.write(f"  Subscription ID: {item['sub_id']}\n")
            f.write("\n")
        
        # Recommendation
        f.write("=" * 100 + "\n")
        f.write("RECOMMENDATION\n")
        f.write("=" * 100 + "\n\n")
        
        # Check if subscription ID is consistent per environment
        env_consistent = all(len(sub_ids) == 1 for sub_ids in by_environment.values())
        
        if env_consistent:
            f.write("✓ Subscription ID is CONSISTENT per environment!\n\n")
            f.write("Mapping:\n")
            for env in sorted(by_environment.keys()):
                sub_id = list(by_environment[env])[0]
                f.write(f"  {env}: {sub_id}\n")
            f.write("\n")
            f.write("Action: Add subscription_id to environment-specific overrides:\n")
            f.write("  - tooling/env/azure/overrides/environment/dev.yaml\n")
            f.write("  - tooling/env/azure/overrides/environment/tst.yaml\n")
            f.write("  - tooling/env/azure/overrides/environment/prd.yaml\n")
        else:
            f.write("✗ Subscription ID is NOT consistent per environment!\n\n")
            
            # Check if consistent per environment + project
            env_project_consistent = all(len(sub_ids) == 1 for sub_ids in by_env_project.values())
            
            if env_project_consistent:
                f.write("✓ Subscription ID is CONSISTENT per environment + project!\n\n")
                f.write("Mapping:\n")
                for env_project in sorted(by_env_project.keys()):
                    sub_id = list(by_env_project[env_project])[0]
                    f.write(f"  {env_project}: {sub_id}\n")
                f.write("\n")
                f.write("Action: Add subscription_id to combined overrides:\n")
                f.write("  - tooling/env/azure/overrides/combined/{env}-{project}.yaml\n")
            else:
                f.write("✗ Subscription ID varies even within environment + project!\n\n")
                f.write("Action: This needs manual investigation!\n")
    
    print(f"Subscription ID analysis saved to: {output_file}")
    
    # Print summary
    print("\n" + "=" * 100)
    print("SUMMARY")
    print("=" * 100)
    
    print(f"\nTotal files with subscription info: {len(files_with_subscription)}")
    
    print("\nBy Environment:")
    for env in sorted(by_environment.keys()):
        sub_ids = by_environment[env]
        print(f"  {env}: {len(sub_ids)} unique subscription ID(s)")
        for sub_id in sorted(sub_ids):
            count = sum(1 for item in files_with_subscription 
                       if item['env'] == env and item['sub_id'] == sub_id)
            print(f"    {sub_id}: {count} files")
    
    env_consistent = all(len(sub_ids) == 1 for sub_ids in by_environment.values())
    
    if env_consistent:
        print("\n✓ Subscription ID is CONSISTENT per environment!")
        print("  → Can be added to environment-specific overrides")
    else:
        print("\n✗ Subscription ID is NOT consistent per environment!")
        print("  → Needs further investigation")

if __name__ == '__main__':
    main()

