# Végső javaslatok - Alapos elemzés után

## <PERSON><PERSON><PERSON>oglaló

Az alapos elemzés után **sokkal jobb képet** kaptunk a helyzetről:

### Je<PERSON><PERSON><PERSON> helyzet
- **Composer rendszerben**: 22 v<PERSON><PERSON><PERSON><PERSON> van definiálva
- **<PERSON><PERSON><PERSON><PERSON>**: 64 egyedi változ<PERSON> has<PERSON>
- **<PERSON><PERSON><PERSON>**: 13 vált<PERSON><PERSON> (20%) - ezek már benne vannak vagy van mapping
- **Számítható**: 6 változó (9%) - ezek kiszámíthatók a meglévő változókból
- **Hozzáadandó**: 3 vált<PERSON><PERSON> (5%) - ezeket érdemes hozzáadni
- **R<PERSON><PERSON><PERSON> hasz<PERSON>lt**: 42 változó (66%) - ezek <5 repo-ban használatosak

## Kateg<PERSON><PERSON><PERSON> részletesen

### ✅ KATEGÓRIA 1: <PERSON><PERSON><PERSON> a composer-ben van (13 változó)

Ezek **m<PERSON><PERSON>**, nincs teend<PERSON>:

| <PERSON><PERSON><PERSON><PERSON> | <PERSON> v<PERSON><PERSON> | Használat |
|----------------|------------------|-----------|
| `project` | `project` | 307 használat, 45 repo |
| `cloud` | `cloud` | 293 használat, 44 repo |
| `environment` | `environment` | 293 használat, 44 repo |
| `region` | `region` | 292 használat, 44 repo |
| `snet_name` | `subnet_name` | 98 használat, 11 repo |
| `pe_snet_name` | `subnet_name` | 64 használat, 10 repo |
| `kvau_name` | `key_vault_name` | 49 használat, 9 repo |
| `kv_name` | `key_vault_name` | 34 használat, 7 repo |
| `prep_subnet_name` | `subnet_name` | 34 használat, 3 repo |
| `vnet_snet_name` | `subnet_name` | 16 használat, 1 repo |
| `subnet_name` | `subnet_name` | 10 használat, 1 repo |
| `keyvault_name` | `key_vault_name` | 3 használat, 1 repo |
| `prep_storage_subnet_name` | `subnet_name` | 3 használat, 1 repo |

**Akció**: Nincs, ezek már működnek!

---

### 🔧 KATEGÓRIA 2: Számítható változók (6 változó)

Ezeket **hozzá kell adni** a composer-hez, mert széles körben használatosak:

#### 2.1 Subsidiary (176 használat, 30 repo)
```yaml
# defaults.yaml-ba:
- name: subsidiary
  value: ${{ parameters.subsidiary }}  # Új parameter kell!
```

**Új parameter** kell a composer.yaml-ba:
```yaml
parameters:
  - name: subsidiary
    displayName: 'Subsidiary (otphu/otphq/test)'
    default: otphu
```

#### 2.2 VNet változók (167 használat, 23 repo)
```yaml
# defaults.yaml-ba:
- name: vnet_name
  value: "vnet-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"

- name: vnet_rgrp_name
  value: "rgrp-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"
```

#### 2.3 Route Table változók (83 használat, 18 repo)
```yaml
# defaults.yaml-ba:
- name: rtbl_name
  value: "rtbl-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-core"

- name: rtbl_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

#### 2.4 Key Vault Resource Group (52 használat, 10 repo)
```yaml
# defaults.yaml-ba:
- name: kvau_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}
```

**Akció**: Ezeket hozzá kell adni a defaults.yaml-hoz!

---

### ➕ KATEGÓRIA 3: Hozzáadandó alias-ok (3 változó)

Ezek **alias-ok**, amelyek gyakran használatosak:

```yaml
# defaults.yaml-ba:
- name: pe_vnet_name
  value: ${{ variables.vnet_name }}

- name: pe_vnet_rgrp_name
  value: ${{ variables.vnet_rgrp_name }}

- name: kv_rgrp_name
  value: ${{ variables.kvau_rgrp_name }}
```

**Akció**: Ezeket is hozzá kell adni a defaults.yaml-hoz!

---

### 📄 KATEGÓRIA 4: Ritkán használt változók (42 változó)

Ezeket **NEM** érdemes a defaults.yaml-ba tenni, helyette:

#### Megoldás: tfvars.template fájlok

Minden terraform-azurerm-* repo-ban legyen egy `examples/tfvars.template` fájl:

**Példa: terraform-azurerm-aks/examples/tfvars.template**
```hcl
# ============================================================================
# REQUIRED VARIABLES (provided by composer)
# ============================================================================
# cloud, environment, region, project, subsidiary
# vnet_name, vnet_rgrp_name, subnet_name
# rtbl_name, rtbl_rgrp_name
# key_vault_name, kvau_rgrp_name

# ============================================================================
# MODULE-SPECIFIC VARIABLES (must be provided)
# ============================================================================

# AKS specific
ingress_nginx_ip = "10.x.x.x"  # TODO: Set your ingress IP

# Optional: Override VNet settings if needed
# vnet_vnet_name = "custom-vnet-name"
# vnet_vnet_rgrp_name = "custom-vnet-rg"
```

**Példa: terraform-azurerm-ai-foundry/examples/tfvars.template**
```hcl
# ============================================================================
# REQUIRED VARIABLES (provided by composer)
# ============================================================================
# cloud, environment, region, project, subsidiary
# vnet_name, vnet_rgrp_name, subnet_name
# key_vault_name, kvau_rgrp_name

# ============================================================================
# MODULE-SPECIFIC VARIABLES (must be provided)
# ============================================================================

# HSM/Encryption (AI Foundry specific)
mhsm_umid_name = "umid-${region}-${environment}-${project}-01"
mhsm_umid_rgrp_name = "${vnet_rgrp_name}"
mhsm_key = "your-hsm-key-name"
```

**Példa: terraform-azurerm-application-gateway/examples/tfvars.template**
```hcl
# ============================================================================
# REQUIRED VARIABLES (provided by composer)
# ============================================================================
# cloud, environment, region, project, subsidiary
# vnet_name, vnet_rgrp_name, subnet_name

# ============================================================================
# MODULE-SPECIFIC VARIABLES (must be provided)
# ============================================================================

# Application Gateway specific
appgw_vnet_name = "${vnet_name}"  # Or custom VNet
appgw_vnet_rgrp_name = "${vnet_rgrp_name}"
appgw_snet_name = "snet-${region}-appgw01"
appgw_private_ip = "10.x.x.x"  # TODO: Set your AppGW IP
```

---

## Implementációs terv

### Fázis 1: Composer bővítése (1 nap)

#### 1.1 Új parameter hozzáadása
```yaml
# tooling/env/composer.yaml
parameters:
  - name: subsidiary
    displayName: 'Subsidiary (otphu/otphq/test)'
    default: otphu
```

#### 1.2 defaults.yaml bővítése
```yaml
# tooling/env/defaults.yaml
variables:
  # ... existing variables ...
  
  # Subsidiary
  - name: subsidiary
    value: ${{ parameters.subsidiary }}
  
  # VNet
  - name: vnet_name
    value: "vnet-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"
  
  - name: vnet_rgrp_name
    value: "rgrp-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-01"
  
  # Route Table
  - name: rtbl_name
    value: "rtbl-${{ variables.region }}-${{ variables.environment }}-${{ variables.project }}-core"
  
  - name: rtbl_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
  
  # Key Vault Resource Group
  - name: kvau_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
  
  # Private Endpoint aliases
  - name: pe_vnet_name
    value: ${{ variables.vnet_name }}
  
  - name: pe_vnet_rgrp_name
    value: ${{ variables.vnet_rgrp_name }}
  
  # Key Vault alias
  - name: kv_rgrp_name
    value: ${{ variables.kvau_rgrp_name }}
```

### Fázis 2: tfvars.template fájlok létrehozása (1 hét)

Minden terraform-azurerm-* repo-ban:

1. Létrehozni `examples/tfvars.template` fájlt
2. Dokumentálni a modul-specifikus változókat
3. Példákat adni az értékekre

**Prioritás szerinti sorrend:**

**Magas prioritás** (modul-specifikus változók vannak):
- terraform-azurerm-aks (ingress_nginx_ip)
- terraform-azurerm-ai-foundry (mhsm_*)
- terraform-azurerm-ai-language (mhsm_*)
- terraform-azurerm-ai-search (mhsm_*)
- terraform-azurerm-application-gateway (appgw_*)
- terraform-azurerm-synapse-workspace (aad_admin_name, synapse_role_assignment_principal_id)

**Közepes prioritás** (alias változók vannak):
- terraform-azurerm-datafactory (vnet_vnet_*, prep_subnet_*)
- terraform-azurerm-linux-function-app (vnet_vnet_*)
- terraform-azurerm-windows-function-app (vnet_vnet_*)
- terraform-azurerm-databricks (prep_subnet_*)
- terraform-azurerm-ai-services (prep_subnet_*)

**Alacsony prioritás** (csak standard változók):
- Minden más repo

### Fázis 3: Dokumentáció (3 nap)

#### 3.1 README frissítése
Minden repo README-jében:
```markdown
## Variables

### Standard Variables (provided by composer)
These variables are automatically provided by the composer system:
- `cloud`, `environment`, `region`, `project`, `subsidiary`
- `vnet_name`, `vnet_rgrp_name`, `subnet_name`
- `rtbl_name`, `rtbl_rgrp_name`
- `key_vault_name`, `kvau_rgrp_name`

### Module-Specific Variables
See `examples/tfvars.template` for module-specific variables that need to be provided.
```

#### 3.2 Központi dokumentáció
Létrehozni egy `VARIABLES.md` fájlt a tooling repo-ban:
```markdown
# Variable Mapping Guide

## Standard Variables
All terraform-azurerm-* modules use these standard variables...

## Module-Specific Variables
Some modules require additional variables...

## Migration Guide
If you're migrating from old tfvars files...
```

---

## Várható eredmények

### Előnyök

1. **Kevesebb manuális munka**
   - 13 változó már működik ✅
   - 9 új változó automatikusan generálódik 🆕
   - Csak modul-specifikus változókat kell megadni

2. **Konzisztencia**
   - Naming convention egységes
   - Kevesebb elírás
   - Könnyebb karbantartás

3. **Átláthatóság**
   - Világos, hogy mi a standard és mi a modul-specifikus
   - tfvars.template fájlok dokumentálják a követelményeket

### Számok

**Jelenlegi helyzet:**
- Átlagosan 15-20 változót kell megadni tfvars fájlban

**Új helyzet:**
- Standard változók: 0 (composer adja)
- Modul-specifikus változók: 0-5 (csak ami tényleg kell)

**Lefedettség:**
- 22 változó (34%) - már a composer-ben van
- 9 változó (14%) - hozzáadva a composer-hez
- 42 változó (66%) - modul-specifikus (tfvars.template)

---

## Következő lépések

1. ✅ **Elemzés elkészült**
2. ⏳ **Döntés**: Jóváhagyod a javaslatot?
3. ⏳ **Fázis 1**: Composer bővítése (1 nap)
4. ⏳ **Fázis 2**: tfvars.template fájlok (1 hét)
5. ⏳ **Fázis 3**: Dokumentáció (3 nap)
6. ⏳ **Tesztelés**: Példa projektek (1 hét)
7. ⏳ **Rollout**: Fokozatos bevezetés

---

## Kérdések

1. **Subsidiary parameter**: Milyen értékek lehetnek? (otphu, otphq, test, ...)
2. **VNet naming**: A jelenlegi pattern megfelelő? (`vnet-{region}-{env}-{project}-01`)
3. **tfvars.template**: Minden repo-ba kell, vagy csak ahol modul-specifikus változók vannak?
4. **Backward compatibility**: Kell-e támogatni a régi tfvars fájlokat?
5. **Environment overrides**: Kell-e environment-specifikus override a VNet naming-re?

---

**Készítette**: Augment AI  
**Dátum**: 2025-11-04  
**Verzió**: 2.0 (Alapos elemzés)

