parameters:
  - name: serviceConnectionName
    default: ""

  - name: helmchartRegistryUrl
  - name: helmchartRegistryAuthServiceConnection
    default: ""
  - name: helmchartRegistryUsername
    default: ""
  - name: helmchartRegistryPassword
    default: ""
  - name: helmchartRegistryIsOCI
    type: boolean
    default: true

  - name: helmAksName
    default: ""
  - name: helmAksResourceGroupName
    default: ""
  - name: helmAksIsAADIntegrated
    type: boolean
    default: false

  - name: ocServerAddress
    default: ""
  - name: ocToken
    default: ""

  - name: kubeConfigPath
    default: $(Pipeline.Workspace)/.kube/config

steps:
- template: ../cd-common/step-set-kubeconfig.yaml
  parameters: 
    serviceConnectionName: ${{ parameters.serviceConnectionName }}
    helmAksName: ${{ parameters.helmAksName }}
    helmAksResourceGroupName: ${{ parameters.helmAksResourceGroupName }}
    helmAksIsAADIntegrated: ${{ parameters.helmAksIsAADIntegrated }}
    ocServerAddress: ${{ parameters.ocServerAddress }}
    ocToken: ${{ parameters.ocToken }}
    kubeConfigPath: ${{ parameters.kubeConfigPath }}

- template: step-helm-login.yaml
  parameters:
    helmchartRegistryUrl: ${{ parameters.helmchartRegistryUrl }}
    helmchartRegistryAuthServiceConnection: ${{ parameters.helmchartRegistryAuthServiceConnection }}
    helmchartRegistryUsername: ${{ parameters.helmchartRegistryUsername }}
    helmchartRegistryPassword: ${{ parameters.helmchartRegistryPassword }}
    helmchartRegistryIsOCI: ${{ parameters.helmchartRegistryIsOCI }}
