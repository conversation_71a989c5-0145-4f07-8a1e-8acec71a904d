parameters:
  - name: helmchartRegistryUrl
    default: ''
  - name: helmchartRegistryIsOCI
    default: true
    type: boolean
  - name: helmchartRegistryUsername
    default: ''
  - name: helmchartRegistryPassword
    default: ''

steps:
- ${{ if and(ne(parameters.helmchartRegistryUrl, ''), ne(parameters.helmchartRegistryUsername, ''), ne(parameters.helmchartRegistryPassword, ''), eq(parameters.helmchartRegistryIsOCI, true)) }}: 
  - task: Bash@3
    displayName: Helm logout
    inputs:
      targetType: 'inline'
      script: |
          set -e
          trap "echo Error on line $LINENO" ERR

          echo "Performing a logout from Helm chart registry: ${{ parameters.helmchartRegistryUrl }}"
          helm registry logout "${{ parameters.helmchartRegistryUrl }}"