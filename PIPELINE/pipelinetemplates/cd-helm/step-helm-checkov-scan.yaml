parameters:
  - name: pipelinetemplateRepoReference

  - name: helmchartRegistryUrl
  - name: helmchartRegistryIsOCI
    type: boolean
    default: true
  - name: helmChartName
  - name: helmChartVersion
  - name: helmExtraParams
    default: ""
  - name: helmArtifactName
    default: "HelmYaml"

  - name: helmRuntimeNamespace
  - name: appCode

  - name: checkovScanFailPipeline
    type: boolean
    default: true
  - name: checkovExtraParams
    default: ""

steps:
- task: Bash@3
  displayName: Prepare Helm template for checkov - ${{ parameters.helmChartName }}
  inputs:
    targetType: 'inline'
    script: |
        set -e
        trap "echo Error on line $LINENO" ERR
        echo "Generating the helm template into a file for checkov helm scan"
        helm template "${{ parameters.appCode }}" \
          $HELM_VALUES_PARAMETER \
          ${{ parameters.helmExtraParams }} \
          $HELMCHART_DECLARATION \
          --version "${{ parameters.helmChartVersion }}" \
          --namespace "${{ parameters.helmRuntimeNamespace }}" > ./checkovhelm.yaml
  env:
    ${{ if eq(parameters.helmchartRegistryIsOCI, true) }}:
      HELMCHART_DECLARATION: "oci://${{ parameters.helmchartRegistryUrl }}/${{ parameters.helmChartName }}"
    ${{ elseif and(eq(parameters.helmchartRegistryIsOCI, false), ne(parameters.helmchartRegistryUsername, ''), ne(parameters.helmchartRegistryPassword, '')) }}:
      HELMCHART_DECLARATION: "--repo ${{ parameters.helmchartRegistryUrl }} ${{ parameters.helmChartName }} --username ${{ parameters.helmchartRegistryUsername }} --password ${{ parameters.helmchartRegistryPassword }}"
    ${{ else }}:
      HELMCHART_DECLARATION: "--repo ${{ parameters.helmchartRegistryUrl }} ${{ parameters.helmChartName }}"

- template: ../common/step-checkov-scan.yaml
  parameters:
    checkovExtraParams: "-f ./checkovhelm.yaml ${{ parameters.checkovExtraParams}}"
    checkovScanFailPipeline: "${{ parameters.checkovScanFailPipeline }}"
    pipelinetemplateRepoReference: ${{ parameters.pipelinetemplateRepoReference }}

- task: PublishPipelineArtifact@1
  displayName: "Publish generated Helm template as a pipeline artifact"
  inputs:
    targetPath: ./checkovhelm.yaml
    artifact: ${{ parameters.helmArtifactName }}
