parameters:
  - name: helmchartRegistryUrl
    default: ''
  - name: helmchartRegistryAuthServiceConnection
    default: ''
  - name: helmchartRegistryUsername
    default: ''
  - name: helmchartRegistryPassword
    default: ''
  - name: helmchartRegistryIsOCI
    default: true
    type: boolean

steps:
- ${{ if and(ne(parameters.helmchartRegistryUrl, ''), ne(parameters.helmchartRegistryAuthServiceConnection, ''), eq(parameters.helmchartRegistryIsOCI, true)) }}:
  - ${{ if not(or(contains(upper(parameters.helmchartRegistryAuthServiceConnection), '-FED-'), endswith(upper(parameters.helmchartRegistryAuthServiceConnection), '-FED'))) }}:
    - template: ../common/step-serviceconnection-login.yaml
      parameters:
        armServiceConnection: ${{ parameters.helmchartRegistryAuthServiceConnection }}
    - task: Bash@3
      displayName: Helm login using Service Connection
      inputs:
        targetType: 'inline'
        script: |
            set -e
            trap "echo Error on line $LINENO" ERR
            echo "Performing a login to this Helm chart registry using the Service Connection: ${{ parameters.helmchartRegistryUrl }}"
            helm registry login "${{ parameters.helmchartRegistryUrl }}" --username "$ARM_CLIENT_ID" --password "$ARM_CLIENT_SECRET"
      env:
        ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
  - ${{ else }}:
    - task: AzureCLI@2
      displayName: Helm login using Token
      inputs:
        azureSubscription: ${{ parameters.helmchartRegistryAuthServiceConnection }}
        scriptType: "bash"
        scriptLocation: inlineScript
        targetType: 'inline'
        inlineScript: |
            set -e
            trap "echo Error on line $LINENO" ERR

            echo "Performing a login to this Helm chart registry using Token: ${{ parameters.helmchartRegistryUrl }}"
            TOKEN=$(az acr login --name ${{ parameters.helmchartRegistryUrl }} --expose-token --output tsv --query accessToken)
            helm registry login ${{ parameters.helmchartRegistryUrl }} --username 00000000-0000-0000-0000-000000000000 --password-stdin <<< $TOKEN

- ${{ if and(ne(parameters.helmchartRegistryUrl, ''), ne(parameters.helmchartRegistryUsername, ''), ne(parameters.helmchartRegistryPassword, ''), eq(parameters.helmchartRegistryIsOCI, true)) }}:
  - task: Bash@3
    displayName: Helm login
    inputs:
      targetType: 'inline'
      script: |
          set -e
          trap "echo Error on line $LINENO" ERR
          echo "Performing a login to this Helm chart registry: ${{ parameters.helmchartRegistryUrl }}"
          helm registry login "${{ parameters.helmchartRegistryUrl }}" --username "${{ parameters.helmchartRegistryUsername }}" --password "${{ parameters.helmchartRegistryPassword }}"