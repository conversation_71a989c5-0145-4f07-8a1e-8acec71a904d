parameters:
  - name: configRepoReference
  - name: pipelinetemplateRepoReference
  - name: serviceConnectionName
    default: ""

  - name: helmchartRegistryUrl
  - name: helmchartRegistryIsOCI
    type: boolean
    default: true
  - name: helmChartName
  - name: helmChartVersion
  - name: helmArtifactName
    default: "HelmYaml"

  - name: helmRuntimeNamespace
  - name: helmAksIsAADIntegrated
    type: boolean
    default: false

  - name: ocServerAddress
    default: ""
  - name: ocToken
    default: ""

  - name: helmValuesFiles
    type: object
    default: []
  - name: helmExtraParams
    default: ""

  - name: appCode

  - name: checkovScanFailPipeline
    type: boolean
    default: true
  - name: checkovExtraParams
    default: ""

steps:
- task: Bash@3
  displayName: Process Helm values files - ${{ parameters.helmChartName }}
  inputs:
    targetType: 'inline'
    script: |
        set -e
        trap "echo Error on line $LINENO" ERR

        HELM_VALUES_FILES=${{ join(',',parameters.helmValuesFiles) }}
        HELM_VALUES_PARAMETER=""
        IFS=","
        for values_file in $HELM_VALUES_FILES
        do
            echo "$values_file"
            HELM_VALUES_PARAMETER="${HELM_VALUES_PARAMETER}-f $(Pipeline.Workspace)/${{ parameters.configRepoReference }}/$values_file "
        done
        echo "$HELM_VALUES_PARAMETER"
        echo "##vso[task.setvariable variable=HELM_VALUES_PARAMETER;]$HELM_VALUES_PARAMETER"

- template: step-helm-deploy.yaml
  parameters:
    serviceConnectionName: ${{ parameters.serviceConnectionName }}
    helmchartRegistryUrl: ${{ parameters.helmchartRegistryUrl }}
    helmchartRegistryIsOCI: ${{ parameters.helmchartRegistryIsOCI }}
    helmChartName: ${{ parameters.helmChartName }}
    helmChartVersion: ${{ parameters.helmChartVersion }}
    helmExtraParams: ${{ parameters.helmExtraParams }}
    helmRuntimeNamespace: ${{ parameters.helmRuntimeNamespace }}
    helmAksIsAADIntegrated: ${{ parameters.helmAksIsAADIntegrated }}
    ocServerAddress: ${{ parameters.ocServerAddress }}
    ocToken: ${{ parameters.ocToken }}
    appCode: ${{ parameters.appCode }}
    mode: "dry-run"

- template: step-helm-checkov-scan.yaml
  parameters:
    pipelinetemplateRepoReference: ${{ parameters.pipelinetemplateRepoReference }}
    helmchartRegistryUrl: ${{ parameters.helmchartRegistryUrl }}
    helmchartRegistryIsOCI: ${{ parameters.helmchartRegistryIsOCI }}
    helmChartName: ${{ parameters.helmChartName }}
    helmChartVersion: ${{ parameters.helmChartVersion }}
    helmRuntimeNamespace: ${{ parameters.helmRuntimeNamespace }}
    helmExtraParams: ${{ parameters.helmExtraParams }}
    helmArtifactName: ${{ parameters.helmArtifactName }}
    checkovScanFailPipeline: ${{ parameters.checkovScanFailPipeline}}
    checkovExtraParams: ${{ parameters.checkovExtraParams }}
    appCode: ${{ parameters.appCode }}
