parameters:
  - name: serviceConnectionName
    default: ""

  - name: helmchartRegistryUrl
  - name: helmchartRegistryIsOCI
    type: boolean
    default: true
  - name: helmChartName
  - name: helmChartVersion
  - name: helmExtraParams

  - name: helmRuntimeNamespace
  - name: helmAksIsAADIntegrated
    type: boolean
    default: false

  - name: ocServerAddress
    default: ""
  - name: ocToken
    default: ""

  - name: appCode
  - name: mode
    default: deploy
    values:
      - deploy
      - dry-run

steps:
- task: Bash@3
  displayName: Helm ${{ parameters.mode }} - ${{ parameters.helmChartName }}
  inputs:
    targetType: 'inline'
    script: |
        set -e
        trap "echo Error on line $LINENO" ERR

        helm upgrade $HELM_DRY_RUN --install "${{ parameters.appCode }}" \
          $HELM_VALUES_PARAMETER \
          ${{ parameters.helmExtraParams }} \
          $HELMCHART_DECLARATION \
          --version "${{ parameters.helmChartVersion }}" \
          --namespace "${{ parameters.helmRuntimeNamespace}}" $HELM_CREATE_NAMESPACE

        HELM_EXIT=$?
        echo "Helm ${{ variables.mode }} exited with $HELM_EXIT"
        exit $HELM_EXIT
  env:
    ${{ if eq(parameters.helmAksIsAADIntegrated, true) }}:
      ${{ if not(or(contains(upper(parameters.serviceConnectionName), '-FED-'), endswith(upper(parameters.serviceConnectionName), '-FED'))) }}:
        AAD_SERVICE_PRINCIPAL_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
        AAD_SERVICE_PRINCIPAL_CLIENT_ID: $(ARM_CLIENT_ID)
      ${{ else }}:
        AZURE_CLIENT_ID: $(ARM_CLIENT_ID)
        AZURE_TENANT_ID: $(ARM_TENANT_ID)
        AZURE_FEDERATED_TOKEN_FILE: oidc-token
        AZURE_AUTHORITY_HOST: 'https://login.microsoftonline.com/'
    ${{ if or(eq(parameters.mode, 'dry-run'), and(ne(parameters.ocServerAddress, ''), ne(parameters.ocToken, ''))) }}:
      HELM_CREATE_NAMESPACE: ""
    ${{ else }}:
      HELM_CREATE_NAMESPACE: "--create-namespace"
    ${{ if eq(parameters.helmchartRegistryIsOCI, true) }}:
      HELMCHART_DECLARATION: "oci://${{ parameters.helmchartRegistryUrl }}/${{ parameters.helmChartName }}"
    ${{ elseif and(eq(parameters.helmchartRegistryIsOCI, false), ne(parameters.helmchartRegistryUsername, ''), ne(parameters.helmchartRegistryPassword, '')) }}:
      HELMCHART_DECLARATION: "--repo ${{ parameters.helmchartRegistryUrl }} ${{ parameters.helmChartName }} --username ${{ parameters.helmchartRegistryUsername }} --password ${{ parameters.helmchartRegistryPassword }}"
    ${{ else }}:
      HELMCHART_DECLARATION: "--repo ${{ parameters.helmchartRegistryUrl }} ${{ parameters.helmChartName }}"
    ${{ if eq(parameters.mode, 'dry-run') }}:
      HELM_DRY_RUN: "--dry-run"
    ${{ else }}:
      HELM_DRY_RUN: ""
