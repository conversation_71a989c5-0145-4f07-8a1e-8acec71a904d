parameters:
  - name: helmchartRegistryUrl
  - name: helmchartRegistryIsOCI
    type: boolean
    default: true
  - name: helmchartRegistryUsername
    default: ""
  - name: helmchartRegistryPassword
    default: ""

  - name: serviceConnectionName
    default: ""
  - name: helmAksName
    default: ""
  - name: helmAksResourceGroupName
    default: ""

  - name: ocServerAddress
    default: ""
  - name: ocToken
    default: ""

steps:
- template: step-helm-logout.yaml
  parameters:
    helmchartRegistryUrl: ${{ parameters.helmchartRegistryUrl }}
    helmchartRegistryIsOCI: ${{ parameters.helmchartRegistryIsOCI }}
    helmchartRegistryUsername: ${{ parameters.helmchartRegistryUsername }}
    helmchartRegistryPassword: ${{ parameters.helmchartRegistryPassword }}

- ${{ if and(or(eq(parameters.serviceConnectionName, ''), eq(parameters.helmAksName, ''), eq(parameters.helmAksResourceGroupName, '')), and(ne(parameters.ocServerAddress, ''), ne(parameters.ocToken, ''))) }}: 
  - template: ../cd-common/step-oc-logout.yaml