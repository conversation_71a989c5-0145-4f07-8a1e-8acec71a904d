parameters:
  - name: serviceConnectionName
    default: ""

  - name: helmAksName
    default: ""
  - name: helmAksResourceGroupName
    default: ""
  - name: helmAksIsAADIntegrated
    type: boolean
    default: false

  - name: ocServerAddress
    default: ""
  - name: ocToken
    default: ""

  - name: kubeConfigPath
    default: $(Pipeline.Workspace)/.kube/config

steps:
- task: Bash@3
  displayName: Setting KubeConfig
  inputs:
    targetType: 'inline'
    script: |
      dname=$(dirname ${{ parameters.kubeConfigPath }})
      mkdir -p $dname
      touch ${{ parameters.kubeConfigPath }}
      chmod 600 ${{ parameters.kubeConfigPath }}

      echo "Setting the kubeconfig environment variable to ${{ parameters.kubeConfigPath }}"
      echo "##vso[task.setvariable variable=kubeconfig]${{ parameters.kubeConfigPath }}"

- ${{ if and(ne(parameters.serviceConnectionName, ''), ne(parameters.helmAksName, ''), ne(parameters.helmAksResourceGroupName, '')) }}:
  - ${{ if not(or(contains(upper(parameters.serviceConnectionName), '-FED-'), endswith(upper(parameters.serviceConnectionName), '-FED'))) }}:
    - template: ../common/step-serviceconnection-login.yaml
      parameters:
        armServiceConnection: ${{ parameters.serviceConnectionName }}
  - ${{ else }}:
    - template: ../common/step-wif-serviceconnection-login.yaml
      parameters:
        armServiceConnection: ${{ parameters.serviceConnectionName }}

  - task: AzureCLI@2
    displayName: Get kubeconfig
    env:
      AKS_IS_AAD_INTEGRATED: ${{ lower(parameters.helmAksIsAADIntegrated) }}
    inputs:
      azureSubscription: ${{ parameters.serviceConnectionName }}
      scriptType: "bash"
      scriptLocation: inlineScript
      inlineScript: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR

        echo "Getting the kubeconfig to the AKS using azure-cli."
        az aks get-credentials --name ${{ parameters.helmAksName }} --resource-group ${{ parameters.helmAksResourceGroupName }} -f $KUBECONFIG --overwrite-existing
        echo "Finished getting the kubeconfig to the AKS using azure-cli."

        if [[ "$AKS_IS_AAD_INTEGRATED" == "true" ]]; then
          echo "Starting to convert the kubeconfig to be compatible with an AzureAD integrated AKS and non-interactive login using Service Principal."
          if command -v kubelogin &> /dev/null
          then
            echo "Starting the conversion using the 'kubelogin' CLI tool."
            kubelogin convert-kubeconfig -l $(ARM_TYPE)
            echo "Finished the conversion using the 'kubelogin' CLI tool."
          else
            echo "The 'kubelogin' kubectl plugin is not installed on this agent. Please upgrade the used agent image/configuration, because this is needed and used for Azure AD integrated AKS authentication."
            exit 1
          fi
          echo "Finished converting the kubeconfig to be compatible with an AzureAD integrated AKS and non-interactive login using Service Principal."
        fi

- ${{ elseif and(ne(parameters.ocServerAddress, ''), ne(parameters.ocToken, '')) }}:
  - template: step-oc-login.yaml
    parameters:
      ocServerAddress: ${{ parameters.ocServerAddress }}
      ocToken: ${{ parameters.ocToken }}

- ${{ else }}:
  - task: Bash@3
    displayName: Missing parameters, error
    inputs:
      targetType: 'inline'
      script: |
          echo "##vso[task.logissue type=error]Either ocServerAddress, ocToken or helmAksResourceGroupName, serviceConnectionName and helmAksName must be provided"
          echo "##vso[task.complete result=Failed;]Error"