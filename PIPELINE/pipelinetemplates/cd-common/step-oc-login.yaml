parameters:
  - name: oc<PERSON><PERSON><PERSON><PERSON>dd<PERSON>
  - name: ocToken

steps:
- task: Bash@3
  displayName: Oc login
  inputs:
    targetType: 'inline'
    script: |
      set -e  # fail on error
      trap "echo Error on line $LINENO" ERR

      echo "Performing a login to the on-prem Openshift server at ${{ parameters.ocServerAddress }} using the token"
      oc login --token=${{ parameters.ocToken }} --server=${{ parameters.ocServerAddress }}
      echo "Finished logging in, asking who am I"
      oc whoami